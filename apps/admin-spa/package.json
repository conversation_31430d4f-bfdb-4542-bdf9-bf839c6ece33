{"name": "masteryos-admin-spa", "version": "1.0.0", "description": "MasteryOS 管理后台 - React Admin", "private": true, "type": "module", "scripts": {"dev": "vite --host 0.0.0.0 --port 3000", "build": "vite build", "preview": "vite preview --host 0.0.0.0 --port 3000", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/icons-material": "^6.5.0", "@mui/material": "^6.5.0", "axios": "^1.11.0", "date-fns": "^4.1.0", "lodash": "^4.17.21", "query-string": "^9.2.2", "ra-data-simple-rest": "^5.10.1", "ra-i18n-polyglot": "^5.10.1", "ra-language-chinese": "^2.0.10", "react": "^18.3.1", "react-admin": "^5.10.1", "react-dom": "^18.3.1", "react-router-dom": "^6.30.1", "recharts": "^2.15.4"}, "devDependencies": {"@types/lodash": "^4.17.20", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "@vitejs/plugin-react": "^4.7.0", "eslint": "^9.32.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "typescript": "5.8.3", "vite": "^6.3.5"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}