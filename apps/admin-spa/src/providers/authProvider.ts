import { AuthProvider } from 'react-admin';

const apiUrl =
  (globalThis as any)?.import?.meta?.env?.VITE_ADMIN_API_URL || 'http://localhost:3102';

export const authProvider: AuthProvider = {
  // 登录
  login: ({ username, password }) => {
    const request = new Request(`${apiUrl}/api/auth/login`, {
      method: 'POST',
      body: JSON.stringify({ email: username, password }),
      headers: new Headers({ 'Content-Type': 'application/json' }),
    });
    return fetch(request)
      .then((response) => {
        if (response.status < 200 || response.status >= 300) {
          throw new Error(response.statusText);
        }
        return response.json();
      })
      .then((auth) => {
        localStorage.setItem('token', auth.token);
        localStorage.setItem('user', JSON.stringify(auth.user));
      })
      .catch(() => {
        throw new Error('Network error');
      });
  },

  // 登出
  logout: () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    return Promise.resolve();
  },

  // 检查错误 (如401)
  checkError: ({ status }: { status: number }) => {
    if (status === 401 || status === 403) {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      return Promise.reject();
    }
    return Promise.resolve();
  },

  // 检查认证状态
  checkAuth: () => {
    return localStorage.getItem('token') ? Promise.resolve() : Promise.reject();
  },

  // 获取权限
  getPermissions: () => {
    const user = localStorage.getItem('user');
    if (user) {
      const userData = JSON.parse(user);
      return Promise.resolve(userData.role);
    }
    return Promise.reject();
  },

  // 获取用户身份
  getIdentity: () => {
    const user = localStorage.getItem('user');
    if (user) {
      const userData = JSON.parse(user);
      return Promise.resolve({
        id: userData.id,
        fullName: userData.name,
        avatar: userData.avatar_url,
      });
    }
    return Promise.reject();
  },
};
