{"lastValidatedTimestamp": 1754329922982, "projects": {"/Volumes/acasis/augment-projects/1w": {"name": "masteryos", "version": "1.0.0"}, "/Volumes/acasis/augment-projects/1w/apps/admin-bff": {"name": "masteryos-admin-bff", "version": "1.0.0"}, "/Volumes/acasis/augment-projects/1w/apps/admin-spa": {"name": "masteryos-admin-spa", "version": "1.0.0"}, "/Volumes/acasis/augment-projects/1w/apps/mobile-bff": {"name": "masteryos-mobile-bff", "version": "1.0.0"}, "/Volumes/acasis/augment-projects/1w/docs": {"name": "masteryos-docs", "version": "1.0.0"}}, "pnpmfiles": [], "settings": {"autoInstallPeers": true, "catalogs": {}, "dedupeDirectDeps": false, "dedupeInjectedDeps": true, "dedupePeerDependents": true, "dev": true, "excludeLinksFromLockfile": false, "hoistPattern": ["*"], "hoistWorkspacePackages": true, "injectWorkspacePackages": false, "linkWorkspacePackages": false, "nodeLinker": "isolated", "optional": true, "preferWorkspacePackages": false, "production": true, "publicHoistPattern": [], "workspacePackagePatterns": ["apps/*", "docs", "infrastructure/*", "packages/*"]}, "filteredInstall": true}