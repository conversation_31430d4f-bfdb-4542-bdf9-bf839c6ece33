hoistPattern:
  - '*'
hoistedDependencies:
  /@ampproject/remapping/2.3.0:
    '@ampproject/remapping': private
  /@angular-devkit/core/17.3.11(chokidar@3.6.0):
    '@angular-devkit/core': private
  /@angular-devkit/schematics-cli/17.3.11(chokidar@3.6.0):
    '@angular-devkit/schematics-cli': private
  /@angular-devkit/schematics/17.3.11(chokidar@3.6.0):
    '@angular-devkit/schematics': private
  /@babel/code-frame/7.27.1:
    '@babel/code-frame': private
  /@babel/compat-data/7.28.0:
    '@babel/compat-data': private
  /@babel/core/7.28.0:
    '@babel/core': private
  /@babel/generator/7.28.0:
    '@babel/generator': private
  /@babel/helper-compilation-targets/7.27.2:
    '@babel/helper-compilation-targets': private
  /@babel/helper-globals/7.28.0:
    '@babel/helper-globals': private
  /@babel/helper-module-imports/7.27.1:
    '@babel/helper-module-imports': private
  /@babel/helper-module-transforms/7.27.3(@babel/core@7.28.0):
    '@babel/helper-module-transforms': private
  /@babel/helper-plugin-utils/7.27.1:
    '@babel/helper-plugin-utils': private
  /@babel/helper-string-parser/7.27.1:
    '@babel/helper-string-parser': private
  /@babel/helper-validator-identifier/7.27.1:
    '@babel/helper-validator-identifier': private
  /@babel/helper-validator-option/7.27.1:
    '@babel/helper-validator-option': private
  /@babel/helpers/7.28.2:
    '@babel/helpers': private
  /@babel/parser/7.28.0:
    '@babel/parser': private
  /@babel/plugin-syntax-async-generators/7.8.4(@babel/core@7.28.0):
    '@babel/plugin-syntax-async-generators': private
  /@babel/plugin-syntax-bigint/7.8.3(@babel/core@7.28.0):
    '@babel/plugin-syntax-bigint': private
  /@babel/plugin-syntax-class-properties/7.12.13(@babel/core@7.28.0):
    '@babel/plugin-syntax-class-properties': private
  /@babel/plugin-syntax-class-static-block/7.14.5(@babel/core@7.28.0):
    '@babel/plugin-syntax-class-static-block': private
  /@babel/plugin-syntax-import-attributes/7.27.1(@babel/core@7.28.0):
    '@babel/plugin-syntax-import-attributes': private
  /@babel/plugin-syntax-import-meta/7.10.4(@babel/core@7.28.0):
    '@babel/plugin-syntax-import-meta': private
  /@babel/plugin-syntax-json-strings/7.8.3(@babel/core@7.28.0):
    '@babel/plugin-syntax-json-strings': private
  /@babel/plugin-syntax-jsx/7.27.1(@babel/core@7.28.0):
    '@babel/plugin-syntax-jsx': private
  /@babel/plugin-syntax-logical-assignment-operators/7.10.4(@babel/core@7.28.0):
    '@babel/plugin-syntax-logical-assignment-operators': private
  /@babel/plugin-syntax-nullish-coalescing-operator/7.8.3(@babel/core@7.28.0):
    '@babel/plugin-syntax-nullish-coalescing-operator': private
  /@babel/plugin-syntax-numeric-separator/7.10.4(@babel/core@7.28.0):
    '@babel/plugin-syntax-numeric-separator': private
  /@babel/plugin-syntax-object-rest-spread/7.8.3(@babel/core@7.28.0):
    '@babel/plugin-syntax-object-rest-spread': private
  /@babel/plugin-syntax-optional-catch-binding/7.8.3(@babel/core@7.28.0):
    '@babel/plugin-syntax-optional-catch-binding': private
  /@babel/plugin-syntax-optional-chaining/7.8.3(@babel/core@7.28.0):
    '@babel/plugin-syntax-optional-chaining': private
  /@babel/plugin-syntax-private-property-in-object/7.14.5(@babel/core@7.28.0):
    '@babel/plugin-syntax-private-property-in-object': private
  /@babel/plugin-syntax-top-level-await/7.14.5(@babel/core@7.28.0):
    '@babel/plugin-syntax-top-level-await': private
  /@babel/plugin-syntax-typescript/7.27.1(@babel/core@7.28.0):
    '@babel/plugin-syntax-typescript': private
  /@babel/plugin-transform-react-jsx-self/7.27.1(@babel/core@7.28.0):
    '@babel/plugin-transform-react-jsx-self': private
  /@babel/plugin-transform-react-jsx-source/7.27.1(@babel/core@7.28.0):
    '@babel/plugin-transform-react-jsx-source': private
  /@babel/runtime/7.28.2:
    '@babel/runtime': private
  /@babel/template/7.27.2:
    '@babel/template': private
  /@babel/traverse/7.28.0:
    '@babel/traverse': private
  /@babel/types/7.28.2:
    '@babel/types': private
  /@bcoe/v8-coverage/0.2.3:
    '@bcoe/v8-coverage': private
  /@colors/colors/1.5.0:
    '@colors/colors': private
  /@cspotcode/source-map-support/0.8.1:
    '@cspotcode/source-map-support': private
  /@emotion/babel-plugin/11.13.5:
    '@emotion/babel-plugin': private
  /@emotion/cache/11.14.0:
    '@emotion/cache': private
  /@emotion/hash/0.9.2:
    '@emotion/hash': private
  /@emotion/is-prop-valid/1.3.1:
    '@emotion/is-prop-valid': private
  /@emotion/memoize/0.9.0:
    '@emotion/memoize': private
  /@emotion/react/11.14.0(@types/react@18.3.23)(react@18.3.1):
    '@emotion/react': private
  /@emotion/serialize/1.3.3:
    '@emotion/serialize': private
  /@emotion/sheet/1.4.0:
    '@emotion/sheet': private
  /@emotion/styled/11.14.1(@emotion/react@11.14.0)(@types/react@18.3.23)(react@18.3.1):
    '@emotion/styled': private
  /@emotion/unitless/0.10.0:
    '@emotion/unitless': private
  /@emotion/use-insertion-effect-with-fallbacks/1.2.0(react@18.3.1):
    '@emotion/use-insertion-effect-with-fallbacks': private
  /@emotion/utils/1.4.2:
    '@emotion/utils': private
  /@emotion/weak-memoize/0.4.0:
    '@emotion/weak-memoize': private
  /@esbuild/aix-ppc64/0.25.8:
    '@esbuild/aix-ppc64': private
  /@esbuild/android-arm/0.25.8:
    '@esbuild/android-arm': private
  /@esbuild/android-arm64/0.25.8:
    '@esbuild/android-arm64': private
  /@esbuild/android-x64/0.25.8:
    '@esbuild/android-x64': private
  /@esbuild/darwin-arm64/0.25.8:
    '@esbuild/darwin-arm64': private
  /@esbuild/darwin-x64/0.25.8:
    '@esbuild/darwin-x64': private
  /@esbuild/freebsd-arm64/0.25.8:
    '@esbuild/freebsd-arm64': private
  /@esbuild/freebsd-x64/0.25.8:
    '@esbuild/freebsd-x64': private
  /@esbuild/linux-arm/0.25.8:
    '@esbuild/linux-arm': private
  /@esbuild/linux-arm64/0.25.8:
    '@esbuild/linux-arm64': private
  /@esbuild/linux-ia32/0.25.8:
    '@esbuild/linux-ia32': private
  /@esbuild/linux-loong64/0.25.8:
    '@esbuild/linux-loong64': private
  /@esbuild/linux-mips64el/0.25.8:
    '@esbuild/linux-mips64el': private
  /@esbuild/linux-ppc64/0.25.8:
    '@esbuild/linux-ppc64': private
  /@esbuild/linux-riscv64/0.25.8:
    '@esbuild/linux-riscv64': private
  /@esbuild/linux-s390x/0.25.8:
    '@esbuild/linux-s390x': private
  /@esbuild/linux-x64/0.25.8:
    '@esbuild/linux-x64': private
  /@esbuild/netbsd-arm64/0.25.8:
    '@esbuild/netbsd-arm64': private
  /@esbuild/netbsd-x64/0.25.8:
    '@esbuild/netbsd-x64': private
  /@esbuild/openbsd-arm64/0.25.8:
    '@esbuild/openbsd-arm64': private
  /@esbuild/openbsd-x64/0.25.8:
    '@esbuild/openbsd-x64': private
  /@esbuild/openharmony-arm64/0.25.8:
    '@esbuild/openharmony-arm64': private
  /@esbuild/sunos-x64/0.25.8:
    '@esbuild/sunos-x64': private
  /@esbuild/win32-arm64/0.25.8:
    '@esbuild/win32-arm64': private
  /@esbuild/win32-ia32/0.25.8:
    '@esbuild/win32-ia32': private
  /@esbuild/win32-x64/0.25.8:
    '@esbuild/win32-x64': private
  /@eslint-community/eslint-utils/4.7.0(eslint@8.57.1):
    '@eslint-community/eslint-utils': public
  /@eslint-community/regexpp/4.12.1:
    '@eslint-community/regexpp': public
  /@eslint/config-array/0.21.0:
    '@eslint/config-array': public
  /@eslint/config-helpers/0.3.0:
    '@eslint/config-helpers': public
  /@eslint/core/0.15.1:
    '@eslint/core': public
  /@eslint/eslintrc/2.1.4:
    '@eslint/eslintrc': public
  /@eslint/js/8.57.1:
    '@eslint/js': public
  /@eslint/object-schema/2.1.6:
    '@eslint/object-schema': public
  /@eslint/plugin-kit/0.3.4:
    '@eslint/plugin-kit': public
  /@humanfs/core/0.19.1:
    '@humanfs/core': private
  /@humanfs/node/0.16.6:
    '@humanfs/node': private
  /@humanwhocodes/config-array/0.13.0:
    '@humanwhocodes/config-array': private
  /@humanwhocodes/module-importer/1.0.1:
    '@humanwhocodes/module-importer': private
  /@humanwhocodes/object-schema/2.0.3:
    '@humanwhocodes/object-schema': private
  /@humanwhocodes/retry/0.4.3:
    '@humanwhocodes/retry': private
  /@isaacs/cliui/8.0.2:
    '@isaacs/cliui': private
  /@istanbuljs/load-nyc-config/1.1.0:
    '@istanbuljs/load-nyc-config': private
  /@istanbuljs/schema/0.1.3:
    '@istanbuljs/schema': private
  /@jest/console/29.7.0:
    '@jest/console': private
  /@jest/core/29.7.0(ts-node@10.9.2):
    '@jest/core': private
  /@jest/environment/29.7.0:
    '@jest/environment': private
  /@jest/expect-utils/29.7.0:
    '@jest/expect-utils': private
  /@jest/expect/29.7.0:
    '@jest/expect': private
  /@jest/fake-timers/29.7.0:
    '@jest/fake-timers': private
  /@jest/globals/29.7.0:
    '@jest/globals': private
  /@jest/reporters/29.7.0:
    '@jest/reporters': private
  /@jest/schemas/29.6.3:
    '@jest/schemas': private
  /@jest/source-map/29.6.3:
    '@jest/source-map': private
  /@jest/test-result/29.7.0:
    '@jest/test-result': private
  /@jest/test-sequencer/29.7.0:
    '@jest/test-sequencer': private
  /@jest/transform/29.7.0:
    '@jest/transform': private
  /@jest/types/29.6.3:
    '@jest/types': private
  /@jridgewell/gen-mapping/0.3.12:
    '@jridgewell/gen-mapping': private
  /@jridgewell/resolve-uri/3.1.2:
    '@jridgewell/resolve-uri': private
  /@jridgewell/source-map/0.3.10:
    '@jridgewell/source-map': private
  /@jridgewell/sourcemap-codec/1.5.4:
    '@jridgewell/sourcemap-codec': private
  /@jridgewell/trace-mapping/0.3.9:
    '@jridgewell/trace-mapping': private
  /@ljharb/through/2.3.14:
    '@ljharb/through': private
  /@lukeed/csprng/1.1.0:
    '@lukeed/csprng': private
  /@mdit-vue/plugin-component/2.1.4:
    '@mdit-vue/plugin-component': private
  /@mdit-vue/plugin-frontmatter/2.1.4:
    '@mdit-vue/plugin-frontmatter': private
  /@mdit-vue/plugin-headers/2.1.4:
    '@mdit-vue/plugin-headers': private
  /@mdit-vue/plugin-sfc/2.1.4:
    '@mdit-vue/plugin-sfc': private
  /@mdit-vue/plugin-title/2.1.4:
    '@mdit-vue/plugin-title': private
  /@mdit-vue/plugin-toc/2.1.4:
    '@mdit-vue/plugin-toc': private
  /@mdit-vue/shared/2.1.4:
    '@mdit-vue/shared': private
  /@mdit-vue/types/2.1.4:
    '@mdit-vue/types': private
  /@microsoft/tsdoc/0.15.1:
    '@microsoft/tsdoc': private
  /@mui/core-downloads-tracker/6.5.0:
    '@mui/core-downloads-tracker': private
  /@mui/icons-material/6.5.0(@mui/material@6.5.0)(@types/react@18.3.23)(react@18.3.1):
    '@mui/icons-material': private
  /@mui/material/6.5.0(@emotion/react@11.14.0)(@emotion/styled@11.14.1)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@mui/material': private
  /@mui/private-theming/6.4.9(@types/react@18.3.23)(react@18.3.1):
    '@mui/private-theming': private
  /@mui/styled-engine/6.5.0(@emotion/react@11.14.0)(@emotion/styled@11.14.1)(react@18.3.1):
    '@mui/styled-engine': private
  /@mui/system/6.5.0(@emotion/react@11.14.0)(@emotion/styled@11.14.1)(@types/react@18.3.23)(react@18.3.1):
    '@mui/system': private
  /@mui/types/7.2.24(@types/react@18.3.23):
    '@mui/types': private
  /@mui/utils/6.4.9(@types/react@18.3.23)(react@18.3.1):
    '@mui/utils': private
  /@nestjs/cli/10.4.9:
    '@nestjs/cli': private
  /@nestjs/common/10.4.20(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(rxjs@7.8.2):
    '@nestjs/common': private
  /@nestjs/config/3.3.0(@nestjs/common@10.4.20)(rxjs@7.8.2):
    '@nestjs/config': private
  /@nestjs/core/10.4.20(@nestjs/common@10.4.20)(@nestjs/platform-express@10.4.20)(reflect-metadata@0.2.2)(rxjs@7.8.2):
    '@nestjs/core': private
  /@nestjs/jwt/10.2.0(@nestjs/common@10.4.20):
    '@nestjs/jwt': private
  /@nestjs/mapped-types/2.1.0(@nestjs/common@10.4.20)(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2):
    '@nestjs/mapped-types': private
  /@nestjs/passport/10.0.3(@nestjs/common@10.4.20)(passport@0.7.0):
    '@nestjs/passport': private
  /@nestjs/platform-express/10.4.20(@nestjs/common@10.4.20)(@nestjs/core@10.4.20):
    '@nestjs/platform-express': private
  /@nestjs/schematics/10.2.3(typescript@5.9.2):
    '@nestjs/schematics': private
  /@nestjs/swagger/8.1.1(@nestjs/common@10.4.20)(@nestjs/core@10.4.20)(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2):
    '@nestjs/swagger': private
  /@nestjs/testing/10.4.20(@nestjs/common@10.4.20)(@nestjs/core@10.4.20)(@nestjs/platform-express@10.4.20):
    '@nestjs/testing': private
  /@nestjs/typeorm/10.0.2(@nestjs/common@10.4.20)(@nestjs/core@10.4.20)(reflect-metadata@0.2.2)(rxjs@7.8.2)(typeorm@0.3.25):
    '@nestjs/typeorm': private
  /@noble/hashes/1.8.0:
    '@noble/hashes': private
  /@nodelib/fs.scandir/2.1.5:
    '@nodelib/fs.scandir': private
  /@nodelib/fs.stat/2.0.5:
    '@nodelib/fs.stat': private
  /@nodelib/fs.walk/1.2.8:
    '@nodelib/fs.walk': private
  /@nuxtjs/opencollective/0.3.2:
    '@nuxtjs/opencollective': private
  /@oozcitak/dom/1.15.10:
    '@oozcitak/dom': private
  /@oozcitak/infra/1.0.8:
    '@oozcitak/infra': private
  /@oozcitak/url/1.0.4:
    '@oozcitak/url': private
  /@oozcitak/util/8.3.8:
    '@oozcitak/util': private
  /@paralleldrive/cuid2/2.2.2:
    '@paralleldrive/cuid2': private
  /@pkgjs/parseargs/0.11.0:
    '@pkgjs/parseargs': private
  /@pkgr/core/0.2.9:
    '@pkgr/core': private
  /@popperjs/core/2.11.8:
    '@popperjs/core': private
  /@redis/bloom/1.2.0(@redis/client@1.6.1):
    '@redis/bloom': private
  /@redis/client/1.6.1:
    '@redis/client': private
  /@redis/graph/1.1.1(@redis/client@1.6.1):
    '@redis/graph': private
  /@redis/json/1.0.7(@redis/client@1.6.1):
    '@redis/json': private
  /@redis/search/1.2.0(@redis/client@1.6.1):
    '@redis/search': private
  /@redis/time-series/1.1.0(@redis/client@1.6.1):
    '@redis/time-series': private
  /@remix-run/router/1.23.0:
    '@remix-run/router': private
  /@rolldown/pluginutils/1.0.0-beta.27:
    '@rolldown/pluginutils': private
  /@rollup/rollup-android-arm-eabi/4.46.2:
    '@rollup/rollup-android-arm-eabi': private
  /@rollup/rollup-android-arm64/4.46.2:
    '@rollup/rollup-android-arm64': private
  /@rollup/rollup-darwin-arm64/4.46.2:
    '@rollup/rollup-darwin-arm64': private
  /@rollup/rollup-darwin-x64/4.46.2:
    '@rollup/rollup-darwin-x64': private
  /@rollup/rollup-freebsd-arm64/4.46.2:
    '@rollup/rollup-freebsd-arm64': private
  /@rollup/rollup-freebsd-x64/4.46.2:
    '@rollup/rollup-freebsd-x64': private
  /@rollup/rollup-linux-arm-gnueabihf/4.46.2:
    '@rollup/rollup-linux-arm-gnueabihf': private
  /@rollup/rollup-linux-arm-musleabihf/4.46.2:
    '@rollup/rollup-linux-arm-musleabihf': private
  /@rollup/rollup-linux-arm64-gnu/4.46.2:
    '@rollup/rollup-linux-arm64-gnu': private
  /@rollup/rollup-linux-arm64-musl/4.46.2:
    '@rollup/rollup-linux-arm64-musl': private
  /@rollup/rollup-linux-loongarch64-gnu/4.46.2:
    '@rollup/rollup-linux-loongarch64-gnu': private
  /@rollup/rollup-linux-ppc64-gnu/4.46.2:
    '@rollup/rollup-linux-ppc64-gnu': private
  /@rollup/rollup-linux-riscv64-gnu/4.46.2:
    '@rollup/rollup-linux-riscv64-gnu': private
  /@rollup/rollup-linux-riscv64-musl/4.46.2:
    '@rollup/rollup-linux-riscv64-musl': private
  /@rollup/rollup-linux-s390x-gnu/4.46.2:
    '@rollup/rollup-linux-s390x-gnu': private
  /@rollup/rollup-linux-x64-gnu/4.46.2:
    '@rollup/rollup-linux-x64-gnu': private
  /@rollup/rollup-linux-x64-musl/4.46.2:
    '@rollup/rollup-linux-x64-musl': private
  /@rollup/rollup-win32-arm64-msvc/4.46.2:
    '@rollup/rollup-win32-arm64-msvc': private
  /@rollup/rollup-win32-ia32-msvc/4.46.2:
    '@rollup/rollup-win32-ia32-msvc': private
  /@rollup/rollup-win32-x64-msvc/4.46.2:
    '@rollup/rollup-win32-x64-msvc': private
  /@scarf/scarf/1.4.0:
    '@scarf/scarf': private
  /@sinclair/typebox/0.27.8:
    '@sinclair/typebox': private
  /@sindresorhus/merge-streams/2.3.0:
    '@sindresorhus/merge-streams': private
  /@sinonjs/commons/3.0.1:
    '@sinonjs/commons': private
  /@sinonjs/fake-timers/10.3.0:
    '@sinonjs/fake-timers': private
  /@sqltools/formatter/1.2.5:
    '@sqltools/formatter': private
  /@tanstack/query-core/5.83.1:
    '@tanstack/query-core': private
  /@tanstack/react-query/5.84.1(react@18.3.1):
    '@tanstack/react-query': private
  /@tokenizer/inflate/0.2.7:
    '@tokenizer/inflate': private
  /@tokenizer/token/0.3.0:
    '@tokenizer/token': private
  /@tootallnate/quickjs-emscripten/0.23.0:
    '@tootallnate/quickjs-emscripten': private
  /@tsconfig/node10/1.0.11:
    '@tsconfig/node10': private
  /@tsconfig/node12/1.0.11:
    '@tsconfig/node12': private
  /@tsconfig/node14/1.0.3:
    '@tsconfig/node14': private
  /@tsconfig/node16/1.0.4:
    '@tsconfig/node16': private
  /@types/babel__core/7.20.5:
    '@types/babel__core': private
  /@types/babel__generator/7.27.0:
    '@types/babel__generator': private
  /@types/babel__template/7.4.4:
    '@types/babel__template': private
  /@types/babel__traverse/7.28.0:
    '@types/babel__traverse': private
  /@types/bcryptjs/2.4.6:
    '@types/bcryptjs': private
  /@types/body-parser/1.19.6:
    '@types/body-parser': private
  /@types/connect/3.4.38:
    '@types/connect': private
  /@types/cookiejar/2.1.5:
    '@types/cookiejar': private
  /@types/d3-array/3.2.1:
    '@types/d3-array': private
  /@types/d3-color/3.1.3:
    '@types/d3-color': private
  /@types/d3-ease/3.0.2:
    '@types/d3-ease': private
  /@types/d3-interpolate/3.0.4:
    '@types/d3-interpolate': private
  /@types/d3-path/3.1.1:
    '@types/d3-path': private
  /@types/d3-scale/4.0.9:
    '@types/d3-scale': private
  /@types/d3-shape/3.1.7:
    '@types/d3-shape': private
  /@types/d3-time/3.0.4:
    '@types/d3-time': private
  /@types/d3-timer/3.0.2:
    '@types/d3-timer': private
  /@types/debug/4.1.12:
    '@types/debug': private
  /@types/eslint-scope/3.7.7:
    '@types/eslint-scope': public
  /@types/eslint/9.6.1:
    '@types/eslint': public
  /@types/estree/1.0.8:
    '@types/estree': private
  /@types/express-serve-static-core/5.0.7:
    '@types/express-serve-static-core': private
  /@types/express/5.0.3:
    '@types/express': private
  /@types/fs-extra/11.0.4:
    '@types/fs-extra': private
  /@types/graceful-fs/4.1.9:
    '@types/graceful-fs': private
  /@types/hash-sum/1.0.2:
    '@types/hash-sum': private
  /@types/http-errors/2.0.5:
    '@types/http-errors': private
  /@types/istanbul-lib-coverage/2.0.6:
    '@types/istanbul-lib-coverage': private
  /@types/istanbul-lib-report/3.0.3:
    '@types/istanbul-lib-report': private
  /@types/istanbul-reports/3.0.4:
    '@types/istanbul-reports': private
  /@types/jest/29.5.14:
    '@types/jest': private
  /@types/json-schema/7.0.15:
    '@types/json-schema': private
  /@types/jsonfile/6.1.4:
    '@types/jsonfile': private
  /@types/jsonwebtoken/9.0.5:
    '@types/jsonwebtoken': private
  /@types/linkify-it/5.0.0:
    '@types/linkify-it': private
  /@types/lodash/4.17.20:
    '@types/lodash': private
  /@types/markdown-it-emoji/3.0.1:
    '@types/markdown-it-emoji': private
  /@types/markdown-it/14.1.2:
    '@types/markdown-it': private
  /@types/mdurl/2.0.0:
    '@types/mdurl': private
  /@types/methods/1.1.4:
    '@types/methods': private
  /@types/mime/1.3.5:
    '@types/mime': private
  /@types/ms/2.1.0:
    '@types/ms': private
  /@types/multer/1.4.13:
    '@types/multer': private
  /@types/parse-json/4.0.2:
    '@types/parse-json': private
  /@types/passport-jwt/4.0.1:
    '@types/passport-jwt': private
  /@types/passport-local/1.0.38:
    '@types/passport-local': private
  /@types/passport-strategy/0.2.38:
    '@types/passport-strategy': private
  /@types/passport/1.0.17:
    '@types/passport': private
  /@types/prop-types/15.7.15:
    '@types/prop-types': private
  /@types/qs/6.14.0:
    '@types/qs': private
  /@types/range-parser/1.2.7:
    '@types/range-parser': private
  /@types/react-dom/18.3.7(@types/react@18.3.23):
    '@types/react-dom': private
  /@types/react-transition-group/4.4.12(@types/react@18.3.23):
    '@types/react-transition-group': private
  /@types/react/18.3.23:
    '@types/react': private
  /@types/semver/7.7.0:
    '@types/semver': private
  /@types/send/0.17.5:
    '@types/send': private
  /@types/serve-static/1.15.8:
    '@types/serve-static': private
  /@types/stack-utils/2.0.3:
    '@types/stack-utils': private
  /@types/superagent/8.1.9:
    '@types/superagent': private
  /@types/supertest/6.0.3:
    '@types/supertest': private
  /@types/trusted-types/2.0.7:
    '@types/trusted-types': private
  /@types/uuid/10.0.0:
    '@types/uuid': private
  /@types/validator/13.15.2:
    '@types/validator': private
  /@types/web-bluetooth/0.0.21:
    '@types/web-bluetooth': private
  /@types/yargs-parser/21.0.3:
    '@types/yargs-parser': private
  /@types/yargs/17.0.33:
    '@types/yargs': private
  /@typescript-eslint/project-service/8.38.0(typescript@5.9.2):
    '@typescript-eslint/project-service': public
  /@typescript-eslint/scope-manager/6.21.0:
    '@typescript-eslint/scope-manager': public
  /@typescript-eslint/tsconfig-utils/8.38.0(typescript@5.9.2):
    '@typescript-eslint/tsconfig-utils': public
  /@typescript-eslint/type-utils/6.21.0(eslint@8.57.1)(typescript@5.9.2):
    '@typescript-eslint/type-utils': public
  /@typescript-eslint/types/6.21.0:
    '@typescript-eslint/types': public
  /@typescript-eslint/typescript-estree/6.21.0(typescript@5.9.2):
    '@typescript-eslint/typescript-estree': public
  /@typescript-eslint/utils/6.21.0(eslint@8.57.1)(typescript@5.9.2):
    '@typescript-eslint/utils': public
  /@typescript-eslint/visitor-keys/6.21.0:
    '@typescript-eslint/visitor-keys': public
  /@ungap/structured-clone/1.3.0:
    '@ungap/structured-clone': private
  /@vitejs/plugin-react/4.7.0(vite@6.3.5):
    '@vitejs/plugin-react': private
  /@vue/compiler-core/3.5.18:
    '@vue/compiler-core': private
  /@vue/compiler-dom/3.5.18:
    '@vue/compiler-dom': private
  /@vue/compiler-sfc/3.5.18:
    '@vue/compiler-sfc': private
  /@vue/compiler-ssr/3.5.18:
    '@vue/compiler-ssr': private
  /@vue/devtools-api/7.7.7:
    '@vue/devtools-api': private
  /@vue/devtools-kit/7.7.7:
    '@vue/devtools-kit': private
  /@vue/devtools-shared/7.7.7:
    '@vue/devtools-shared': private
  /@vue/reactivity/3.5.18:
    '@vue/reactivity': private
  /@vue/runtime-core/3.5.18:
    '@vue/runtime-core': private
  /@vue/runtime-dom/3.5.18:
    '@vue/runtime-dom': private
  /@vue/server-renderer/3.5.18(vue@3.5.18):
    '@vue/server-renderer': private
  /@vue/shared/3.5.18:
    '@vue/shared': private
  /@vuepress/cli/2.0.0-rc.24(typescript@5.9.2):
    '@vuepress/cli': private
  /@vuepress/client/2.0.0-rc.24(typescript@5.9.2):
    '@vuepress/client': private
  /@vuepress/core/2.0.0-rc.24(typescript@5.9.2):
    '@vuepress/core': private
  /@vuepress/helper/2.0.0-rc.112(typescript@5.9.2)(vuepress@2.0.0-rc.24):
    '@vuepress/helper': private
  /@vuepress/markdown/2.0.0-rc.24:
    '@vuepress/markdown': private
  /@vuepress/plugin-back-to-top/2.0.0-rc.112(typescript@5.9.2)(vuepress@2.0.0-rc.24):
    '@vuepress/plugin-back-to-top': private
  /@vuepress/plugin-medium-zoom/2.0.0-rc.112(typescript@5.9.2)(vuepress@2.0.0-rc.24):
    '@vuepress/plugin-medium-zoom': private
  /@vuepress/plugin-nprogress/2.0.0-rc.112(typescript@5.9.2)(vuepress@2.0.0-rc.24):
    '@vuepress/plugin-nprogress': private
  /@vuepress/plugin-search/2.0.0-rc.112(typescript@5.9.2)(vuepress@2.0.0-rc.24):
    '@vuepress/plugin-search': private
  /@vuepress/shared/2.0.0-rc.24:
    '@vuepress/shared': private
  /@vuepress/utils/2.0.0-rc.24:
    '@vuepress/utils': private
  /@vueuse/core/13.6.0(vue@3.5.18):
    '@vueuse/core': private
  /@vueuse/metadata/13.6.0:
    '@vueuse/metadata': private
  /@vueuse/shared/13.6.0(vue@3.5.18):
    '@vueuse/shared': private
  /@webassemblyjs/ast/1.14.1:
    '@webassemblyjs/ast': private
  /@webassemblyjs/floating-point-hex-parser/1.13.2:
    '@webassemblyjs/floating-point-hex-parser': private
  /@webassemblyjs/helper-api-error/1.13.2:
    '@webassemblyjs/helper-api-error': private
  /@webassemblyjs/helper-buffer/1.14.1:
    '@webassemblyjs/helper-buffer': private
  /@webassemblyjs/helper-numbers/1.13.2:
    '@webassemblyjs/helper-numbers': private
  /@webassemblyjs/helper-wasm-bytecode/1.13.2:
    '@webassemblyjs/helper-wasm-bytecode': private
  /@webassemblyjs/helper-wasm-section/1.14.1:
    '@webassemblyjs/helper-wasm-section': private
  /@webassemblyjs/ieee754/1.13.2:
    '@webassemblyjs/ieee754': private
  /@webassemblyjs/leb128/1.13.2:
    '@webassemblyjs/leb128': private
  /@webassemblyjs/utf8/1.13.2:
    '@webassemblyjs/utf8': private
  /@webassemblyjs/wasm-edit/1.14.1:
    '@webassemblyjs/wasm-edit': private
  /@webassemblyjs/wasm-gen/1.14.1:
    '@webassemblyjs/wasm-gen': private
  /@webassemblyjs/wasm-opt/1.14.1:
    '@webassemblyjs/wasm-opt': private
  /@webassemblyjs/wasm-parser/1.14.1:
    '@webassemblyjs/wasm-parser': private
  /@webassemblyjs/wast-printer/1.14.1:
    '@webassemblyjs/wast-printer': private
  /@xtuc/ieee754/1.2.0:
    '@xtuc/ieee754': private
  /@xtuc/long/4.2.2:
    '@xtuc/long': private
  /accepts/1.3.8:
    accepts: private
  /acorn-import-phases/1.0.4(acorn@8.15.0):
    acorn-import-phases: private
  /acorn-jsx/5.3.2(acorn@8.15.0):
    acorn-jsx: private
  /acorn-walk/8.3.4:
    acorn-walk: private
  /acorn/8.15.0:
    acorn: private
  /agent-base/7.1.4:
    agent-base: private
  /ajv-formats/2.1.1(ajv@8.12.0):
    ajv-formats: private
  /ajv-keywords/3.5.2(ajv@6.12.6):
    ajv-keywords: private
  /ajv/6.12.6:
    ajv: private
  /ansi-colors/4.1.3:
    ansi-colors: private
  /ansi-escapes/4.3.2:
    ansi-escapes: private
  /ansi-regex/5.0.1:
    ansi-regex: private
  /ansi-styles/4.3.0:
    ansi-styles: private
  /ansis/3.17.0:
    ansis: private
  /anymatch/3.1.3:
    anymatch: private
  /app-root-path/3.1.0:
    app-root-path: private
  /append-field/1.0.0:
    append-field: private
  /arg/4.1.3:
    arg: private
  /argparse/2.0.1:
    argparse: private
  /array-flatten/1.1.1:
    array-flatten: private
  /array-timsort/1.0.3:
    array-timsort: private
  /array-union/2.1.0:
    array-union: private
  /asap/2.0.6:
    asap: private
  /ast-types/0.13.4:
    ast-types: private
  /async/3.2.6:
    async: private
  /asynckit/0.4.0:
    asynckit: private
  /attr-accept/2.2.5:
    attr-accept: private
  /autosuggest-highlight/3.3.4:
    autosuggest-highlight: private
  /available-typed-arrays/1.0.7:
    available-typed-arrays: private
  /axios/1.11.0:
    axios: private
  /babel-jest/29.7.0(@babel/core@7.28.0):
    babel-jest: private
  /babel-plugin-istanbul/6.1.1:
    babel-plugin-istanbul: private
  /babel-plugin-jest-hoist/29.6.3:
    babel-plugin-jest-hoist: private
  /babel-plugin-macros/3.1.0:
    babel-plugin-macros: private
  /babel-preset-current-node-syntax/1.2.0(@babel/core@7.28.0):
    babel-preset-current-node-syntax: private
  /babel-preset-jest/29.6.3(@babel/core@7.28.0):
    babel-preset-jest: private
  /balanced-match/1.0.2:
    balanced-match: private
  /base64-js/1.5.1:
    base64-js: private
  /basic-ftp/5.0.5:
    basic-ftp: private
  /bcryptjs/2.4.3:
    bcryptjs: private
  /binary-extensions/2.3.0:
    binary-extensions: private
  /birpc/2.5.0:
    birpc: private
  /bl/4.1.0:
    bl: private
  /body-parser/1.20.3:
    body-parser: private
  /boolbase/1.0.0:
    boolbase: private
  /brace-expansion/1.1.12:
    brace-expansion: private
  /braces/3.0.3:
    braces: private
  /browserslist/4.25.1:
    browserslist: private
  /bs-logger/0.2.6:
    bs-logger: private
  /bser/2.1.1:
    bser: private
  /buffer-equal-constant-time/1.0.1:
    buffer-equal-constant-time: private
  /buffer-from/1.1.2:
    buffer-from: private
  /buffer/6.0.3:
    buffer: private
  /busboy/1.6.0:
    busboy: private
  /bytes/3.1.2:
    bytes: private
  /cac/6.7.14:
    cac: private
  /call-bind-apply-helpers/1.0.2:
    call-bind-apply-helpers: private
  /call-bind/1.0.8:
    call-bind: private
  /call-bound/1.0.4:
    call-bound: private
  /callsites/3.1.0:
    callsites: private
  /camelcase/6.3.0:
    camelcase: private
  /caniuse-lite/1.0.30001731:
    caniuse-lite: private
  /chalk/4.1.2:
    chalk: private
  /char-regex/1.0.2:
    char-regex: private
  /chardet/0.7.0:
    chardet: private
  /cheerio-select/2.1.0:
    cheerio-select: private
  /cheerio/1.1.2:
    cheerio: private
  /chokidar/3.6.0:
    chokidar: private
  /chrome-trace-event/1.0.4:
    chrome-trace-event: private
  /ci-info/3.9.0:
    ci-info: private
  /cjs-module-lexer/1.4.3:
    cjs-module-lexer: private
  /class-transformer/0.5.1:
    class-transformer: private
  /class-validator/0.14.2:
    class-validator: private
  /cli-cursor/3.1.0:
    cli-cursor: private
  /cli-spinners/2.9.2:
    cli-spinners: private
  /cli-table3/0.6.5:
    cli-table3: private
  /cli-width/3.0.0:
    cli-width: private
  /cliui/8.0.1:
    cliui: private
  /clone/1.0.4:
    clone: private
  /clsx/2.1.1:
    clsx: private
  /cluster-key-slot/1.1.2:
    cluster-key-slot: private
  /co/4.6.0:
    co: private
  /collect-v8-coverage/1.0.2:
    collect-v8-coverage: private
  /color-convert/2.0.1:
    color-convert: private
  /color-name/1.1.4:
    color-name: private
  /combined-stream/1.0.8:
    combined-stream: private
  /commander/4.1.1:
    commander: private
  /comment-json/4.2.5:
    comment-json: private
  /component-emitter/1.3.1:
    component-emitter: private
  /concat-map/0.0.1:
    concat-map: private
  /concat-stream/1.6.2:
    concat-stream: private
  /consola/2.15.3:
    consola: private
  /content-disposition/0.5.4:
    content-disposition: private
  /content-type/1.0.5:
    content-type: private
  /convert-source-map/2.0.0:
    convert-source-map: private
  /cookie-signature/1.0.6:
    cookie-signature: private
  /cookie/0.7.1:
    cookie: private
  /cookiejar/2.1.4:
    cookiejar: private
  /copy-anything/3.0.5:
    copy-anything: private
  /core-util-is/1.0.3:
    core-util-is: private
  /cors/2.8.5:
    cors: private
  /cosmiconfig/8.3.6(typescript@5.7.2):
    cosmiconfig: private
  /create-jest/29.7.0(@types/node@22.17.0)(ts-node@10.9.2):
    create-jest: private
  /create-require/1.1.1:
    create-require: private
  /cross-spawn/7.0.6:
    cross-spawn: private
  /css-mediaquery/0.1.2:
    css-mediaquery: private
  /css-select/5.2.2:
    css-select: private
  /css-what/6.2.2:
    css-what: private
  /csstype/3.1.3:
    csstype: private
  /d3-array/3.2.4:
    d3-array: private
  /d3-color/3.1.0:
    d3-color: private
  /d3-ease/3.0.1:
    d3-ease: private
  /d3-format/3.1.0:
    d3-format: private
  /d3-interpolate/3.0.1:
    d3-interpolate: private
  /d3-path/3.1.0:
    d3-path: private
  /d3-scale/4.0.2:
    d3-scale: private
  /d3-shape/3.2.0:
    d3-shape: private
  /d3-time-format/4.1.0:
    d3-time-format: private
  /d3-time/3.1.0:
    d3-time: private
  /d3-timer/3.0.1:
    d3-timer: private
  /data-uri-to-buffer/6.0.2:
    data-uri-to-buffer: private
  /date-fns/4.1.0:
    date-fns: private
  /dayjs/1.11.13:
    dayjs: private
  /debug/4.4.1:
    debug: private
  /decimal.js-light/2.5.1:
    decimal.js-light: private
  /decode-uri-component/0.2.2:
    decode-uri-component: private
  /dedent/1.6.0:
    dedent: private
  /deep-extend/0.6.0:
    deep-extend: private
  /deep-is/0.1.4:
    deep-is: private
  /deepmerge/4.3.1:
    deepmerge: private
  /defaults/1.0.4:
    defaults: private
  /define-data-property/1.1.4:
    define-data-property: private
  /define-properties/1.2.1:
    define-properties: private
  /degenerator/5.0.1:
    degenerator: private
  /delayed-stream/1.0.0:
    delayed-stream: private
  /depd/2.0.0:
    depd: private
  /destroy/1.2.0:
    destroy: private
  /detect-newline/3.1.0:
    detect-newline: private
  /dezalgo/1.0.4:
    dezalgo: private
  /diacritic/0.0.2:
    diacritic: private
  /diff-sequences/29.6.3:
    diff-sequences: private
  /diff/4.0.2:
    diff: private
  /dir-glob/3.0.1:
    dir-glob: private
  /doctrine/3.0.0:
    doctrine: private
  /dom-helpers/5.2.1:
    dom-helpers: private
  /dom-serializer/2.0.0:
    dom-serializer: private
  /domelementtype/2.3.0:
    domelementtype: private
  /domhandler/5.0.3:
    domhandler: private
  /dompurify/3.2.6:
    dompurify: private
  /domutils/3.2.2:
    domutils: private
  /dotenv-expand/10.0.0:
    dotenv-expand: private
  /dotenv/16.4.5:
    dotenv: private
  /dunder-proto/1.0.1:
    dunder-proto: private
  /eastasianwidth/0.2.0:
    eastasianwidth: private
  /ecdsa-sig-formatter/1.0.11:
    ecdsa-sig-formatter: private
  /ee-first/1.1.1:
    ee-first: private
  /electron-to-chromium/1.5.194:
    electron-to-chromium: private
  /emittery/0.13.1:
    emittery: private
  /emoji-regex/8.0.0:
    emoji-regex: private
  /encodeurl/2.0.0:
    encodeurl: private
  /encoding-sniffer/0.2.1:
    encoding-sniffer: private
  /enhanced-resolve/5.18.2:
    enhanced-resolve: private
  /entities/3.0.1:
    entities: private
  /envinfo/7.14.0:
    envinfo: private
  /error-ex/1.3.2:
    error-ex: private
  /es-define-property/1.0.1:
    es-define-property: private
  /es-errors/1.3.0:
    es-errors: private
  /es-module-lexer/1.7.0:
    es-module-lexer: private
  /es-object-atoms/1.1.1:
    es-object-atoms: private
  /es-set-tostringtag/2.1.0:
    es-set-tostringtag: private
  /esbuild/0.25.8:
    esbuild: private
  /escalade/3.2.0:
    escalade: private
  /escape-html/1.0.3:
    escape-html: private
  /escape-string-regexp/4.0.0:
    escape-string-regexp: private
  /escodegen/2.1.0:
    escodegen: private
  /eslint-plugin-react-hooks/5.2.0(eslint@9.32.0):
    eslint-plugin-react-hooks: public
  /eslint-plugin-react-refresh/0.4.20(eslint@9.32.0):
    eslint-plugin-react-refresh: public
  /eslint-scope/7.2.2:
    eslint-scope: public
  /eslint-visitor-keys/3.4.3:
    eslint-visitor-keys: public
  /espree/9.6.1:
    espree: private
  /esprima/4.0.1:
    esprima: private
  /esquery/1.6.0:
    esquery: private
  /esrecurse/4.3.0:
    esrecurse: private
  /estraverse/5.3.0:
    estraverse: private
  /estree-walker/2.0.2:
    estree-walker: private
  /esutils/2.0.3:
    esutils: private
  /etag/1.8.1:
    etag: private
  /eventemitter3/4.0.7:
    eventemitter3: private
  /events/3.3.0:
    events: private
  /execa/5.1.1:
    execa: private
  /exit/0.1.2:
    exit: private
  /expect/29.7.0:
    expect: private
  /express/4.21.2:
    express: private
  /extend-shallow/2.0.1:
    extend-shallow: private
  /external-editor/3.1.0:
    external-editor: private
  /fast-deep-equal/3.1.3:
    fast-deep-equal: private
  /fast-diff/1.3.0:
    fast-diff: private
  /fast-equals/5.2.2:
    fast-equals: private
  /fast-glob/3.3.3:
    fast-glob: private
  /fast-json-stable-stringify/2.1.0:
    fast-json-stable-stringify: private
  /fast-levenshtein/2.0.6:
    fast-levenshtein: private
  /fast-safe-stringify/2.1.1:
    fast-safe-stringify: private
  /fast-uri/3.0.6:
    fast-uri: private
  /fastq/1.19.1:
    fastq: private
  /fb-watchman/2.0.2:
    fb-watchman: private
  /fdir/6.4.6(picomatch@4.0.3):
    fdir: private
  /fflate/0.8.2:
    fflate: private
  /figures/3.2.0:
    figures: private
  /file-entry-cache/6.0.1:
    file-entry-cache: private
  /file-selector/2.1.2:
    file-selector: private
  /file-type/20.4.1:
    file-type: private
  /fill-range/7.1.1:
    fill-range: private
  /filter-obj/1.1.0:
    filter-obj: private
  /finalhandler/1.3.1:
    finalhandler: private
  /find-root/1.1.0:
    find-root: private
  /find-up/5.0.0:
    find-up: private
  /flat-cache/3.2.0:
    flat-cache: private
  /flatted/3.3.3:
    flatted: private
  /follow-redirects/1.15.11:
    follow-redirects: private
  /for-each/0.3.5:
    for-each: private
  /foreground-child/3.3.1:
    foreground-child: private
  /fork-ts-checker-webpack-plugin/9.0.2(typescript@5.7.2)(webpack@5.97.1):
    fork-ts-checker-webpack-plugin: private
  /form-data/4.0.4:
    form-data: private
  /formidable/3.5.4:
    formidable: private
  /forwarded/0.2.0:
    forwarded: private
  /fresh/0.5.2:
    fresh: private
  /fs-extra/11.3.0:
    fs-extra: private
  /fs-monkey/1.1.0:
    fs-monkey: private
  /fs.realpath/1.0.0:
    fs.realpath: private
  /fsevents/2.3.3:
    fsevents: private
  /function-bind/1.1.2:
    function-bind: private
  /generic-pool/3.9.0:
    generic-pool: private
  /gensync/1.0.0-beta.2:
    gensync: private
  /get-caller-file/2.0.5:
    get-caller-file: private
  /get-east-asian-width/1.3.0:
    get-east-asian-width: private
  /get-intrinsic/1.3.0:
    get-intrinsic: private
  /get-package-type/0.1.0:
    get-package-type: private
  /get-proto/1.0.1:
    get-proto: private
  /get-stdin/9.0.0:
    get-stdin: private
  /get-stream/6.0.1:
    get-stream: private
  /get-uri/6.0.5:
    get-uri: private
  /glob-parent/6.0.2:
    glob-parent: private
  /glob-to-regexp/0.4.1:
    glob-to-regexp: private
  /glob/10.4.5:
    glob: private
  /globals/13.24.0:
    globals: private
  /globby/11.1.0:
    globby: private
  /gopd/1.2.0:
    gopd: private
  /graceful-fs/4.2.11:
    graceful-fs: private
  /graphemer/1.4.0:
    graphemer: private
  /gray-matter/4.0.3:
    gray-matter: private
  /handlebars/4.7.8:
    handlebars: private
  /has-flag/4.0.0:
    has-flag: private
  /has-own-prop/2.0.0:
    has-own-prop: private
  /has-property-descriptors/1.0.2:
    has-property-descriptors: private
  /has-symbols/1.1.0:
    has-symbols: private
  /has-tostringtag/1.0.2:
    has-tostringtag: private
  /hash-sum/2.0.0:
    hash-sum: private
  /hasown/2.0.2:
    hasown: private
  /hoist-non-react-statics/3.3.2:
    hoist-non-react-statics: private
  /hookable/5.5.3:
    hookable: private
  /html-escaper/2.0.2:
    html-escaper: private
  /html-link-extractor/1.0.5:
    html-link-extractor: private
  /htmlparser2/10.0.0:
    htmlparser2: private
  /http-errors/2.0.0:
    http-errors: private
  /http-proxy-agent/7.0.2:
    http-proxy-agent: private
  /https-proxy-agent/7.0.6:
    https-proxy-agent: private
  /human-signals/2.1.0:
    human-signals: private
  /iconv-lite/0.4.24:
    iconv-lite: private
  /ieee754/1.2.1:
    ieee754: private
  /ignore/5.3.2:
    ignore: private
  /import-fresh/3.3.1:
    import-fresh: private
  /import-local/3.2.0:
    import-local: private
  /imurmurhash/0.1.4:
    imurmurhash: private
  /inflection/3.0.2:
    inflection: private
  /inflight/1.0.6:
    inflight: private
  /inherits/2.0.4:
    inherits: private
  /ini/4.1.3:
    ini: private
  /inquirer/8.2.6:
    inquirer: private
  /internmap/2.0.3:
    internmap: private
  /ip-address/9.0.5:
    ip-address: private
  /ipaddr.js/1.9.1:
    ipaddr.js: private
  /is-absolute-url/4.0.1:
    is-absolute-url: private
  /is-arrayish/0.2.1:
    is-arrayish: private
  /is-binary-path/2.1.0:
    is-binary-path: private
  /is-callable/1.2.7:
    is-callable: private
  /is-core-module/2.16.1:
    is-core-module: private
  /is-extendable/0.1.1:
    is-extendable: private
  /is-extglob/2.1.1:
    is-extglob: private
  /is-fullwidth-code-point/3.0.0:
    is-fullwidth-code-point: private
  /is-generator-fn/2.1.0:
    is-generator-fn: private
  /is-glob/4.0.3:
    is-glob: private
  /is-interactive/1.0.0:
    is-interactive: private
  /is-number/7.0.0:
    is-number: private
  /is-path-inside/3.0.3:
    is-path-inside: private
  /is-relative-url/4.0.0:
    is-relative-url: private
  /is-stream/2.0.1:
    is-stream: private
  /is-typed-array/1.1.15:
    is-typed-array: private
  /is-unicode-supported/0.1.0:
    is-unicode-supported: private
  /is-what/4.1.16:
    is-what: private
  /isarray/1.0.0:
    isarray: private
  /isexe/2.0.0:
    isexe: private
  /istanbul-lib-coverage/3.2.2:
    istanbul-lib-coverage: private
  /istanbul-lib-instrument/6.0.3:
    istanbul-lib-instrument: private
  /istanbul-lib-report/3.0.1:
    istanbul-lib-report: private
  /istanbul-lib-source-maps/4.0.1:
    istanbul-lib-source-maps: private
  /istanbul-reports/3.1.7:
    istanbul-reports: private
  /iterare/1.2.1:
    iterare: private
  /jackspeak/3.4.3:
    jackspeak: private
  /jest-changed-files/29.7.0:
    jest-changed-files: private
  /jest-circus/29.7.0:
    jest-circus: private
  /jest-cli/29.7.0(@types/node@22.17.0)(ts-node@10.9.2):
    jest-cli: private
  /jest-config/29.7.0(@types/node@22.17.0)(ts-node@10.9.2):
    jest-config: private
  /jest-diff/29.7.0:
    jest-diff: private
  /jest-docblock/29.7.0:
    jest-docblock: private
  /jest-each/29.7.0:
    jest-each: private
  /jest-environment-node/29.7.0:
    jest-environment-node: private
  /jest-get-type/29.6.3:
    jest-get-type: private
  /jest-haste-map/29.7.0:
    jest-haste-map: private
  /jest-leak-detector/29.7.0:
    jest-leak-detector: private
  /jest-matcher-utils/29.7.0:
    jest-matcher-utils: private
  /jest-message-util/29.7.0:
    jest-message-util: private
  /jest-mock/29.7.0:
    jest-mock: private
  /jest-pnp-resolver/1.2.3(jest-resolve@29.7.0):
    jest-pnp-resolver: private
  /jest-regex-util/29.6.3:
    jest-regex-util: private
  /jest-resolve-dependencies/29.7.0:
    jest-resolve-dependencies: private
  /jest-resolve/29.7.0:
    jest-resolve: private
  /jest-runner/29.7.0:
    jest-runner: private
  /jest-runtime/29.7.0:
    jest-runtime: private
  /jest-snapshot/29.7.0:
    jest-snapshot: private
  /jest-util/29.7.0:
    jest-util: private
  /jest-validate/29.7.0:
    jest-validate: private
  /jest-watcher/29.7.0:
    jest-watcher: private
  /jest-worker/29.7.0:
    jest-worker: private
  /jest/29.7.0(@types/node@22.17.0)(ts-node@10.9.2):
    jest: private
  /js-tokens/4.0.0:
    js-tokens: private
  /js-yaml/4.1.0:
    js-yaml: private
  /jsbn/1.1.0:
    jsbn: private
  /jsesc/3.1.0:
    jsesc: private
  /json-buffer/3.0.1:
    json-buffer: private
  /json-parse-even-better-errors/2.3.1:
    json-parse-even-better-errors: private
  /json-schema-traverse/0.4.1:
    json-schema-traverse: private
  /json-stable-stringify-without-jsonify/1.0.1:
    json-stable-stringify-without-jsonify: private
  /json5/2.2.3:
    json5: private
  /jsonc-parser/3.3.1:
    jsonc-parser: private
  /jsonexport/3.2.0:
    jsonexport: private
  /jsonfile/6.1.0:
    jsonfile: private
  /jsonwebtoken/9.0.2:
    jsonwebtoken: private
  /jwa/1.4.2:
    jwa: private
  /jws/3.2.2:
    jws: private
  /keyv/4.5.4:
    keyv: private
  /kind-of/6.0.3:
    kind-of: private
  /kleur/3.0.3:
    kleur: private
  /leven/3.1.0:
    leven: private
  /levn/0.4.1:
    levn: private
  /libphonenumber-js/1.12.10:
    libphonenumber-js: private
  /lines-and-columns/1.2.4:
    lines-and-columns: private
  /link-check/5.4.0:
    link-check: private
  /linkify-it/4.0.1:
    linkify-it: private
  /loader-runner/4.3.0:
    loader-runner: private
  /locate-path/6.0.0:
    locate-path: private
  /lodash.includes/4.3.0:
    lodash.includes: private
  /lodash.isboolean/3.0.3:
    lodash.isboolean: private
  /lodash.isinteger/4.0.4:
    lodash.isinteger: private
  /lodash.isnumber/3.0.3:
    lodash.isnumber: private
  /lodash.isplainobject/4.0.6:
    lodash.isplainobject: private
  /lodash.isstring/4.0.1:
    lodash.isstring: private
  /lodash.memoize/4.1.2:
    lodash.memoize: private
  /lodash.merge/4.6.2:
    lodash.merge: private
  /lodash.once/4.1.1:
    lodash.once: private
  /lodash/4.17.21:
    lodash: private
  /log-symbols/4.1.0:
    log-symbols: private
  /loose-envify/1.4.0:
    loose-envify: private
  /lru-cache/7.18.3:
    lru-cache: private
  /magic-string/0.30.8:
    magic-string: private
  /make-dir/4.0.0:
    make-dir: private
  /make-error/1.3.6:
    make-error: private
  /makeerror/1.0.12:
    makeerror: private
  /markdown-it-anchor/9.2.0(@types/markdown-it@14.1.2)(markdown-it@14.1.0):
    markdown-it-anchor: private
  /markdown-it-emoji/3.0.0:
    markdown-it-emoji: private
  /markdown-it/14.1.0:
    markdown-it: private
  /markdown-link-check/3.13.7:
    markdown-link-check: private
  /markdown-link-extractor/4.0.2:
    markdown-link-extractor: private
  /markdownlint-cli/0.37.0:
    markdownlint-cli: private
  /markdownlint-micromark/0.1.7:
    markdownlint-micromark: private
  /markdownlint/0.31.1:
    markdownlint: private
  /marked/12.0.2:
    marked: private
  /math-intrinsics/1.1.0:
    math-intrinsics: private
  /mdurl/2.0.0:
    mdurl: private
  /media-typer/0.3.0:
    media-typer: private
  /medium-zoom/1.1.0:
    medium-zoom: private
  /memfs/3.5.3:
    memfs: private
  /merge-descriptors/1.0.3:
    merge-descriptors: private
  /merge-stream/2.0.0:
    merge-stream: private
  /merge2/1.4.1:
    merge2: private
  /methods/1.1.2:
    methods: private
  /micromatch/4.0.8:
    micromatch: private
  /mime-db/1.52.0:
    mime-db: private
  /mime-types/2.1.35:
    mime-types: private
  /mime/2.6.0:
    mime: private
  /mimic-fn/2.1.0:
    mimic-fn: private
  /mimic-function/5.0.1:
    mimic-function: private
  /minimatch/3.1.2:
    minimatch: private
  /minimist/1.2.8:
    minimist: private
  /minipass/7.1.2:
    minipass: private
  /mitt/3.0.1:
    mitt: private
  /mkdirp/0.5.6:
    mkdirp: private
  /ms/2.1.3:
    ms: private
  /multer/1.4.5-lts.2:
    multer: private
  /mute-stream/0.0.8:
    mute-stream: private
  /nanoid/3.3.11:
    nanoid: private
  /natural-compare/1.4.0:
    natural-compare: private
  /needle/3.3.1:
    needle: private
  /negotiator/0.6.3:
    negotiator: private
  /neo-async/2.6.2:
    neo-async: private
  /netmask/2.0.2:
    netmask: private
  /node-abort-controller/3.1.1:
    node-abort-controller: private
  /node-email-verifier/2.0.0:
    node-email-verifier: private
  /node-emoji/1.11.0:
    node-emoji: private
  /node-fetch/2.7.0:
    node-fetch: private
  /node-int64/0.4.0:
    node-int64: private
  /node-polyglot/2.6.0:
    node-polyglot: private
  /node-releases/2.0.19:
    node-releases: private
  /normalize-path/3.0.0:
    normalize-path: private
  /npm-run-path/4.0.1:
    npm-run-path: private
  /nth-check/2.1.1:
    nth-check: private
  /object-assign/4.1.1:
    object-assign: private
  /object-inspect/1.13.4:
    object-inspect: private
  /object-keys/1.1.1:
    object-keys: private
  /object.entries/1.1.9:
    object.entries: private
  /on-finished/2.4.1:
    on-finished: private
  /once/1.4.0:
    once: private
  /onetime/5.1.2:
    onetime: private
  /optionator/0.9.4:
    optionator: private
  /ora/5.4.1:
    ora: private
  /os-tmpdir/1.0.2:
    os-tmpdir: private
  /p-limit/3.1.0:
    p-limit: private
  /p-locate/5.0.0:
    p-locate: private
  /p-try/2.2.0:
    p-try: private
  /pac-proxy-agent/7.2.0:
    pac-proxy-agent: private
  /pac-resolver/7.0.1:
    pac-resolver: private
  /package-json-from-dist/1.0.1:
    package-json-from-dist: private
  /parent-module/1.0.1:
    parent-module: private
  /parse-json/5.2.0:
    parse-json: private
  /parse5-htmlparser2-tree-adapter/7.1.0:
    parse5-htmlparser2-tree-adapter: private
  /parse5-parser-stream/7.1.2:
    parse5-parser-stream: private
  /parse5/7.3.0:
    parse5: private
  /parseurl/1.3.3:
    parseurl: private
  /passport-jwt/4.0.1:
    passport-jwt: private
  /passport-local/1.0.0:
    passport-local: private
  /passport-strategy/1.0.0:
    passport-strategy: private
  /passport/0.7.0:
    passport: private
  /path-exists/4.0.0:
    path-exists: private
  /path-is-absolute/1.0.1:
    path-is-absolute: private
  /path-key/3.1.1:
    path-key: private
  /path-parse/1.0.7:
    path-parse: private
  /path-scurry/1.11.1:
    path-scurry: private
  /path-to-regexp/3.3.0:
    path-to-regexp: private
  /path-type/4.0.0:
    path-type: private
  /pause/0.0.1:
    pause: private
  /perfect-debounce/1.0.0:
    perfect-debounce: private
  /pg-cloudflare/1.2.7:
    pg-cloudflare: private
  /pg-connection-string/2.9.1:
    pg-connection-string: private
  /pg-int8/1.0.1:
    pg-int8: private
  /pg-pool/3.10.1(pg@8.16.3):
    pg-pool: private
  /pg-protocol/1.10.3:
    pg-protocol: private
  /pg-types/2.2.0:
    pg-types: private
  /pg/8.16.3:
    pg: private
  /pgpass/1.0.5:
    pgpass: private
  /picocolors/1.1.1:
    picocolors: private
  /picomatch/4.0.3:
    picomatch: private
  /pirates/4.0.7:
    pirates: private
  /pkg-dir/4.2.0:
    pkg-dir: private
  /pluralize/8.0.0:
    pluralize: private
  /possible-typed-array-names/1.1.0:
    possible-typed-array-names: private
  /postcss/8.5.6:
    postcss: private
  /postgres-array/2.0.0:
    postgres-array: private
  /postgres-bytea/1.0.0:
    postgres-bytea: private
  /postgres-date/1.0.7:
    postgres-date: private
  /postgres-interval/1.2.0:
    postgres-interval: private
  /prelude-ls/1.2.1:
    prelude-ls: private
  /prettier-linter-helpers/1.0.0:
    prettier-linter-helpers: public
  /pretty-format/29.7.0:
    pretty-format: private
  /process-nextick-args/2.0.1:
    process-nextick-args: private
  /progress/2.0.3:
    progress: private
  /prompts/2.4.2:
    prompts: private
  /prop-types/15.8.1:
    prop-types: private
  /proxy-addr/2.0.7:
    proxy-addr: private
  /proxy-agent/6.5.0:
    proxy-agent: private
  /proxy-from-env/1.1.0:
    proxy-from-env: private
  /punycode.js/2.3.1:
    punycode.js: private
  /punycode/2.3.1:
    punycode: private
  /pure-rand/6.1.0:
    pure-rand: private
  /qs/6.13.0:
    qs: private
  /query-string/7.1.3:
    query-string: private
  /queue-microtask/1.2.3:
    queue-microtask: private
  /ra-core/5.10.1(react-dom@18.3.1)(react-hook-form@7.62.0)(react-router-dom@6.30.1)(react-router@7.7.1)(react@18.3.1):
    ra-core: private
  /ra-data-simple-rest/5.10.1(ra-core@5.10.1):
    ra-data-simple-rest: private
  /ra-i18n-polyglot/5.10.1(react-dom@18.3.1)(react-hook-form@7.62.0)(react-router-dom@6.30.1)(react-router@7.7.1)(react@18.3.1):
    ra-i18n-polyglot: private
  /ra-language-chinese/2.0.10:
    ra-language-chinese: private
  /ra-language-english/5.10.1(react-dom@18.3.1)(react-hook-form@7.62.0)(react-router-dom@6.30.1)(react-router@7.7.1)(react@18.3.1):
    ra-language-english: private
  /ra-ui-materialui/5.10.1(@mui/icons-material@6.5.0)(@mui/material@6.5.0)(@mui/system@7.2.0)(@mui/utils@7.2.0)(csstype@3.1.3)(ra-core@5.10.1)(react-dom@18.3.1)(react-hook-form@7.62.0)(react-is@19.1.1)(react-router-dom@6.30.1)(react-router@7.7.1)(react@18.3.1):
    ra-ui-materialui: private
  /randombytes/2.1.0:
    randombytes: private
  /range-parser/1.2.1:
    range-parser: private
  /raw-body/2.5.2:
    raw-body: private
  /react-admin/5.10.1(@mui/system@7.2.0)(@mui/utils@7.2.0)(@types/react@18.3.23)(csstype@3.1.3)(react-dom@18.3.1)(react-is@19.1.1)(react@18.3.1):
    react-admin: private
  /react-dom/18.3.1(react@18.3.1):
    react-dom: private
  /react-dropzone/14.3.8(react@18.3.1):
    react-dropzone: private
  /react-error-boundary/4.1.2(react@18.3.1):
    react-error-boundary: private
  /react-hook-form/7.62.0(react@18.3.1):
    react-hook-form: private
  /react-hotkeys-hook/5.1.0(react-dom@18.3.1)(react@18.3.1):
    react-hotkeys-hook: private
  /react-is/19.1.1:
    react-is: private
  /react-refresh/0.17.0:
    react-refresh: private
  /react-router-dom/6.30.1(react-dom@18.3.1)(react@18.3.1):
    react-router-dom: private
  /react-router/7.7.1(react-dom@18.3.1)(react@18.3.1):
    react-router: private
  /react-smooth/4.0.4(react-dom@18.3.1)(react@18.3.1):
    react-smooth: private
  /react-transition-group/4.4.5(react-dom@18.3.1)(react@18.3.1):
    react-transition-group: private
  /react/18.3.1:
    react: private
  /readable-stream/2.3.8:
    readable-stream: private
  /readdirp/3.6.0:
    readdirp: private
  /recharts-scale/0.4.5:
    recharts-scale: private
  /recharts/2.15.4(react-dom@18.3.1)(react@18.3.1):
    recharts: private
  /redis/4.7.1:
    redis: private
  /reflect-metadata/0.2.2:
    reflect-metadata: private
  /remove-accents/0.4.4:
    remove-accents: private
  /repeat-string/1.6.1:
    repeat-string: private
  /require-directory/2.1.1:
    require-directory: private
  /require-from-string/2.0.2:
    require-from-string: private
  /resolve-cwd/3.0.0:
    resolve-cwd: private
  /resolve-from/4.0.0:
    resolve-from: private
  /resolve.exports/2.0.3:
    resolve.exports: private
  /resolve/1.22.10:
    resolve: private
  /restore-cursor/3.1.0:
    restore-cursor: private
  /reusify/1.1.0:
    reusify: private
  /rfdc/1.4.1:
    rfdc: private
  /rimraf/3.0.2:
    rimraf: private
  /rollup/4.46.2:
    rollup: private
  /run-async/2.4.1:
    run-async: private
  /run-con/1.3.2:
    run-con: private
  /run-parallel/1.2.0:
    run-parallel: private
  /rxjs/7.8.2:
    rxjs: private
  /safe-buffer/5.2.1:
    safe-buffer: private
  /safer-buffer/2.1.2:
    safer-buffer: private
  /sax/1.4.1:
    sax: private
  /scheduler/0.23.2:
    scheduler: private
  /schema-utils/3.3.0:
    schema-utils: private
  /section-matter/1.0.0:
    section-matter: private
  /semver/7.7.2:
    semver: private
  /send/0.19.0:
    send: private
  /serialize-javascript/6.0.2:
    serialize-javascript: private
  /serve-static/1.16.2:
    serve-static: private
  /set-cookie-parser/2.7.1:
    set-cookie-parser: private
  /set-function-length/1.2.2:
    set-function-length: private
  /setprototypeof/1.2.0:
    setprototypeof: private
  /sha.js/2.4.12:
    sha.js: private
  /shebang-command/2.0.0:
    shebang-command: private
  /shebang-regex/3.0.0:
    shebang-regex: private
  /side-channel-list/1.0.0:
    side-channel-list: private
  /side-channel-map/1.0.1:
    side-channel-map: private
  /side-channel-weakmap/1.0.2:
    side-channel-weakmap: private
  /side-channel/1.1.0:
    side-channel: private
  /signal-exit/4.1.0:
    signal-exit: private
  /sisteransi/1.0.5:
    sisteransi: private
  /slash/3.0.0:
    slash: private
  /smart-buffer/4.2.0:
    smart-buffer: private
  /socks-proxy-agent/8.0.5:
    socks-proxy-agent: private
  /socks/2.8.6:
    socks: private
  /source-map-js/1.2.1:
    source-map-js: private
  /source-map-support/0.5.21:
    source-map-support: private
  /source-map/0.6.1:
    source-map: private
  /speakingurl/14.0.1:
    speakingurl: private
  /split-on-first/1.1.0:
    split-on-first: private
  /split2/4.2.0:
    split2: private
  /sprintf-js/1.1.3:
    sprintf-js: private
  /sql-highlight/6.1.0:
    sql-highlight: private
  /stack-utils/2.0.6:
    stack-utils: private
  /statuses/2.0.1:
    statuses: private
  /stdin-discarder/0.2.2:
    stdin-discarder: private
  /streamsearch/1.1.0:
    streamsearch: private
  /strict-uri-encode/2.0.0:
    strict-uri-encode: private
  /string-length/4.0.2:
    string-length: private
  /string-width/4.2.3:
    string-width: private
    string-width-cjs: private
  /string_decoder/1.1.1:
    string_decoder: private
  /strip-ansi/6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  /strip-bom-string/1.0.0:
    strip-bom-string: private
  /strip-bom/3.0.0:
    strip-bom: private
  /strip-final-newline/2.0.0:
    strip-final-newline: private
  /strip-json-comments/3.1.1:
    strip-json-comments: private
  /strtok3/10.3.4:
    strtok3: private
  /stylis/4.2.0:
    stylis: private
  /superagent/10.2.3:
    superagent: private
  /superjson/2.2.2:
    superjson: private
  /supertest/7.1.4:
    supertest: private
  /supports-color/7.2.0:
    supports-color: private
  /supports-preserve-symlinks-flag/1.0.0:
    supports-preserve-symlinks-flag: private
  /swagger-ui-dist/5.18.2:
    swagger-ui-dist: private
  /symbol-observable/4.0.0:
    symbol-observable: private
  /synckit/0.11.11:
    synckit: private
  /tapable/2.2.2:
    tapable: private
  /terser-webpack-plugin/5.3.14(webpack@5.101.0):
    terser-webpack-plugin: private
  /terser/5.43.1:
    terser: private
  /test-exclude/6.0.0:
    test-exclude: private
  /text-table/0.2.0:
    text-table: private
  /through/2.3.8:
    through: private
  /tiny-invariant/1.3.3:
    tiny-invariant: private
  /tinyglobby/0.2.14:
    tinyglobby: private
  /tmp/0.0.33:
    tmp: private
  /tmpl/1.0.5:
    tmpl: private
  /to-buffer/1.2.1:
    to-buffer: private
  /to-regex-range/5.0.1:
    to-regex-range: private
  /toidentifier/1.0.1:
    toidentifier: private
  /token-types/6.0.4:
    token-types: private
  /tr46/0.0.3:
    tr46: private
  /tree-kill/1.2.2:
    tree-kill: private
  /ts-api-utils/1.4.3(typescript@5.9.2):
    ts-api-utils: private
  /ts-jest/29.4.1(@babel/core@7.28.0)(jest@29.7.0)(typescript@5.9.2):
    ts-jest: private
  /ts-loader/9.5.2(typescript@5.9.2)(webpack@5.101.0):
    ts-loader: private
  /ts-node/10.9.2(@types/node@22.17.0)(typescript@5.9.2):
    ts-node: private
  /tsconfig-paths-webpack-plugin/4.2.0:
    tsconfig-paths-webpack-plugin: private
  /tsconfig-paths/4.2.0:
    tsconfig-paths: private
  /tslib/2.8.1:
    tslib: private
  /type-check/0.4.0:
    type-check: private
  /type-detect/4.0.8:
    type-detect: private
  /type-fest/4.41.0:
    type-fest: private
  /type-is/1.6.18:
    type-is: private
  /typed-array-buffer/1.0.3:
    typed-array-buffer: private
  /typedarray/0.0.6:
    typedarray: private
  /typeorm/0.3.25(pg@8.16.3)(redis@4.7.1)(reflect-metadata@0.2.2)(ts-node@10.9.2):
    typeorm: private
  /uc.micro/1.0.6:
    uc.micro: private
  /uglify-js/3.19.3:
    uglify-js: private
  /uid/2.0.2:
    uid: private
  /uint8array-extras/1.4.0:
    uint8array-extras: private
  /undici-types/6.21.0:
    undici-types: private
  /undici/7.13.0:
    undici: private
  /unicorn-magic/0.3.0:
    unicorn-magic: private
  /universalify/2.0.1:
    universalify: private
  /unpipe/1.0.0:
    unpipe: private
  /upath/2.0.1:
    upath: private
  /update-browserslist-db/1.1.3(browserslist@4.25.1):
    update-browserslist-db: private
  /uri-js/4.4.1:
    uri-js: private
  /util-deprecate/1.0.2:
    util-deprecate: private
  /utils-merge/1.0.1:
    utils-merge: private
  /uuid/11.1.0:
    uuid: private
  /v8-compile-cache-lib/3.0.1:
    v8-compile-cache-lib: private
  /v8-to-istanbul/9.3.0:
    v8-to-istanbul: private
  /validator/13.15.15:
    validator: private
  /vary/1.1.2:
    vary: private
  /victory-vendor/36.9.2:
    victory-vendor: private
  /vite/6.3.5(@types/node@20.19.9):
    vite: private
  /vue-router/4.5.1(vue@3.5.18):
    vue-router: private
  /vue/3.5.18(typescript@5.9.2):
    vue: private
  /vuepress/2.0.0-rc.24(typescript@5.9.2)(vue@3.5.18):
    vuepress: private
  /walker/1.0.8:
    walker: private
  /warning/4.0.3:
    warning: private
  /watchpack/2.4.4:
    watchpack: private
  /wcwidth/1.0.1:
    wcwidth: private
  /webidl-conversions/3.0.1:
    webidl-conversions: private
  /webpack-node-externals/3.0.0:
    webpack-node-externals: private
  /webpack-sources/3.3.3:
    webpack-sources: private
  /webpack/5.97.1:
    webpack: private
  /whatwg-encoding/3.1.1:
    whatwg-encoding: private
  /whatwg-mimetype/4.0.0:
    whatwg-mimetype: private
  /whatwg-url/5.0.0:
    whatwg-url: private
  /which-typed-array/1.1.19:
    which-typed-array: private
  /which/2.0.2:
    which: private
  /word-wrap/1.2.5:
    word-wrap: private
  /wordwrap/1.0.0:
    wordwrap: private
  /wrap-ansi/6.2.0:
    wrap-ansi: private
  /wrap-ansi/7.0.0:
    wrap-ansi-cjs: private
  /wrappy/1.0.2:
    wrappy: private
  /write-file-atomic/4.0.2:
    write-file-atomic: private
  /xmlbuilder2/3.1.1:
    xmlbuilder2: private
  /xtend/4.0.2:
    xtend: private
  /y18n/5.0.8:
    y18n: private
  /yallist/4.0.0:
    yallist: private
  /yaml/1.10.2:
    yaml: private
  /yargs-parser/21.1.1:
    yargs-parser: private
  /yargs/17.7.2:
    yargs: private
  /yn/3.1.1:
    yn: private
  /yocto-queue/0.1.0:
    yocto-queue: private
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@angular-devkit/core@17.3.11(chokidar@3.6.0)':
    '@angular-devkit/core': private
  '@angular-devkit/schematics-cli@17.3.11(chokidar@3.6.0)':
    '@angular-devkit/schematics-cli': private
  '@angular-devkit/schematics@17.3.11(chokidar@3.6.0)':
    '@angular-devkit/schematics': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.28.0':
    '@babel/compat-data': private
  '@babel/core@7.28.0':
    '@babel/core': private
  '@babel/generator@7.28.0':
    '@babel/generator': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-globals@7.28.0':
    '@babel/helper-globals': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.27.3(@babel/core@7.28.0)':
    '@babel/helper-module-transforms': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helpers@7.28.2':
    '@babel/helpers': private
  '@babel/parser@7.28.0':
    '@babel/parser': private
  '@babel/plugin-syntax-async-generators@7.8.4(@babel/core@7.28.0)':
    '@babel/plugin-syntax-async-generators': private
  '@babel/plugin-syntax-bigint@7.8.3(@babel/core@7.28.0)':
    '@babel/plugin-syntax-bigint': private
  '@babel/plugin-syntax-class-properties@7.12.13(@babel/core@7.28.0)':
    '@babel/plugin-syntax-class-properties': private
  '@babel/plugin-syntax-class-static-block@7.14.5(@babel/core@7.28.0)':
    '@babel/plugin-syntax-class-static-block': private
  '@babel/plugin-syntax-import-attributes@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-import-attributes': private
  '@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.28.0)':
    '@babel/plugin-syntax-import-meta': private
  '@babel/plugin-syntax-json-strings@7.8.3(@babel/core@7.28.0)':
    '@babel/plugin-syntax-json-strings': private
  '@babel/plugin-syntax-jsx@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-jsx': private
  '@babel/plugin-syntax-logical-assignment-operators@7.10.4(@babel/core@7.28.0)':
    '@babel/plugin-syntax-logical-assignment-operators': private
  '@babel/plugin-syntax-nullish-coalescing-operator@7.8.3(@babel/core@7.28.0)':
    '@babel/plugin-syntax-nullish-coalescing-operator': private
  '@babel/plugin-syntax-numeric-separator@7.10.4(@babel/core@7.28.0)':
    '@babel/plugin-syntax-numeric-separator': private
  '@babel/plugin-syntax-object-rest-spread@7.8.3(@babel/core@7.28.0)':
    '@babel/plugin-syntax-object-rest-spread': private
  '@babel/plugin-syntax-optional-catch-binding@7.8.3(@babel/core@7.28.0)':
    '@babel/plugin-syntax-optional-catch-binding': private
  '@babel/plugin-syntax-optional-chaining@7.8.3(@babel/core@7.28.0)':
    '@babel/plugin-syntax-optional-chaining': private
  '@babel/plugin-syntax-private-property-in-object@7.14.5(@babel/core@7.28.0)':
    '@babel/plugin-syntax-private-property-in-object': private
  '@babel/plugin-syntax-top-level-await@7.14.5(@babel/core@7.28.0)':
    '@babel/plugin-syntax-top-level-await': private
  '@babel/plugin-syntax-typescript@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-typescript': private
  '@babel/plugin-transform-react-jsx-self@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-react-jsx-self': private
  '@babel/plugin-transform-react-jsx-source@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-react-jsx-source': private
  '@babel/runtime@7.28.2':
    '@babel/runtime': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.28.0':
    '@babel/traverse': private
  '@babel/types@7.28.2':
    '@babel/types': private
  '@bcoe/v8-coverage@0.2.3':
    '@bcoe/v8-coverage': private
  '@colors/colors@1.5.0':
    '@colors/colors': private
  '@cspotcode/source-map-support@0.8.1':
    '@cspotcode/source-map-support': private
  '@emotion/babel-plugin@11.13.5':
    '@emotion/babel-plugin': private
  '@emotion/cache@11.14.0':
    '@emotion/cache': private
  '@emotion/hash@0.9.2':
    '@emotion/hash': private
  '@emotion/is-prop-valid@1.3.1':
    '@emotion/is-prop-valid': private
  '@emotion/memoize@0.9.0':
    '@emotion/memoize': private
  '@emotion/react@11.14.0(@types/react@18.3.23)(react@18.3.1)':
    '@emotion/react': private
  '@emotion/serialize@1.3.3':
    '@emotion/serialize': private
  '@emotion/sheet@1.4.0':
    '@emotion/sheet': private
  '@emotion/styled@11.14.1(@emotion/react@11.14.0(@types/react@18.3.23)(react@18.3.1))(@types/react@18.3.23)(react@18.3.1)':
    '@emotion/styled': private
  '@emotion/unitless@0.10.0':
    '@emotion/unitless': private
  '@emotion/use-insertion-effect-with-fallbacks@1.2.0(react@18.3.1)':
    '@emotion/use-insertion-effect-with-fallbacks': private
  '@emotion/utils@1.4.2':
    '@emotion/utils': private
  '@emotion/weak-memoize@0.4.0':
    '@emotion/weak-memoize': private
  '@esbuild/darwin-arm64@0.25.8':
    '@esbuild/darwin-arm64': private
  '@eslint-community/eslint-utils@4.7.0(eslint@9.32.0)':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/config-array@0.21.0':
    '@eslint/config-array': private
  '@eslint/config-helpers@0.3.0':
    '@eslint/config-helpers': private
  '@eslint/core@0.15.1':
    '@eslint/core': private
  '@eslint/eslintrc@3.3.1':
    '@eslint/eslintrc': private
  '@eslint/object-schema@2.1.6':
    '@eslint/object-schema': private
  '@eslint/plugin-kit@0.3.4':
    '@eslint/plugin-kit': private
  '@humanfs/core@0.19.1':
    '@humanfs/core': private
  '@humanfs/node@0.16.6':
    '@humanfs/node': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/retry@0.4.3':
    '@humanwhocodes/retry': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@istanbuljs/load-nyc-config@1.1.0':
    '@istanbuljs/load-nyc-config': private
  '@istanbuljs/schema@0.1.3':
    '@istanbuljs/schema': private
  '@jest/console@29.7.0':
    '@jest/console': private
  '@jest/core@29.7.0(babel-plugin-macros@3.1.0)(ts-node@10.9.2(@types/node@22.17.0)(typescript@5.8.3))':
    '@jest/core': private
  '@jest/environment@29.7.0':
    '@jest/environment': private
  '@jest/expect-utils@29.7.0':
    '@jest/expect-utils': private
  '@jest/expect@29.7.0':
    '@jest/expect': private
  '@jest/fake-timers@29.7.0':
    '@jest/fake-timers': private
  '@jest/globals@29.7.0':
    '@jest/globals': private
  '@jest/reporters@29.7.0':
    '@jest/reporters': private
  '@jest/schemas@29.6.3':
    '@jest/schemas': private
  '@jest/source-map@29.6.3':
    '@jest/source-map': private
  '@jest/test-result@29.7.0':
    '@jest/test-result': private
  '@jest/test-sequencer@29.7.0':
    '@jest/test-sequencer': private
  '@jest/transform@29.7.0':
    '@jest/transform': private
  '@jest/types@29.6.3':
    '@jest/types': private
  '@jridgewell/gen-mapping@0.3.12':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/source-map@0.3.10':
    '@jridgewell/source-map': private
  '@jridgewell/sourcemap-codec@1.5.4':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.29':
    '@jridgewell/trace-mapping': private
  '@ljharb/through@2.3.14':
    '@ljharb/through': private
  '@lukeed/csprng@1.1.0':
    '@lukeed/csprng': private
  '@mdit-vue/plugin-component@2.1.4':
    '@mdit-vue/plugin-component': private
  '@mdit-vue/plugin-frontmatter@2.1.4':
    '@mdit-vue/plugin-frontmatter': private
  '@mdit-vue/plugin-headers@2.1.4':
    '@mdit-vue/plugin-headers': private
  '@mdit-vue/plugin-sfc@2.1.4':
    '@mdit-vue/plugin-sfc': private
  '@mdit-vue/plugin-title@2.1.4':
    '@mdit-vue/plugin-title': private
  '@mdit-vue/plugin-toc@2.1.4':
    '@mdit-vue/plugin-toc': private
  '@mdit-vue/shared@2.1.4':
    '@mdit-vue/shared': private
  '@mdit-vue/types@2.1.4':
    '@mdit-vue/types': private
  '@microsoft/tsdoc@0.15.1':
    '@microsoft/tsdoc': private
  '@mui/core-downloads-tracker@6.5.0':
    '@mui/core-downloads-tracker': private
  '@mui/icons-material@6.5.0(@mui/material@6.5.0(@emotion/react@11.14.0(@types/react@18.3.23)(react@18.3.1))(@emotion/styled@11.14.1(@emotion/react@11.14.0(@types/react@18.3.23)(react@18.3.1))(@types/react@18.3.23)(react@18.3.1))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@types/react@18.3.23)(react@18.3.1)':
    '@mui/icons-material': private
  '@mui/material@6.5.0(@emotion/react@11.14.0(@types/react@18.3.23)(react@18.3.1))(@emotion/styled@11.14.1(@emotion/react@11.14.0(@types/react@18.3.23)(react@18.3.1))(@types/react@18.3.23)(react@18.3.1))(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@mui/material': private
  '@mui/private-theming@6.4.9(@types/react@18.3.23)(react@18.3.1)':
    '@mui/private-theming': private
  '@mui/styled-engine@6.5.0(@emotion/react@11.14.0(@types/react@18.3.23)(react@18.3.1))(@emotion/styled@11.14.1(@emotion/react@11.14.0(@types/react@18.3.23)(react@18.3.1))(@types/react@18.3.23)(react@18.3.1))(react@18.3.1)':
    '@mui/styled-engine': private
  '@mui/system@6.5.0(@emotion/react@11.14.0(@types/react@18.3.23)(react@18.3.1))(@emotion/styled@11.14.1(@emotion/react@11.14.0(@types/react@18.3.23)(react@18.3.1))(@types/react@18.3.23)(react@18.3.1))(@types/react@18.3.23)(react@18.3.1)':
    '@mui/system': private
  '@mui/types@7.2.24(@types/react@18.3.23)':
    '@mui/types': private
  '@mui/utils@6.4.9(@types/react@18.3.23)(react@18.3.1)':
    '@mui/utils': private
  '@nestjs/cli@10.4.9':
    '@nestjs/cli': private
  '@nestjs/common@10.4.20(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(rxjs@7.8.2)':
    '@nestjs/common': private
  '@nestjs/config@3.3.0(@nestjs/common@10.4.20(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(rxjs@7.8.2))(rxjs@7.8.2)':
    '@nestjs/config': private
  '@nestjs/core@10.4.20(@nestjs/common@10.4.20(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(rxjs@7.8.2))(@nestjs/platform-express@10.4.20)(reflect-metadata@0.2.2)(rxjs@7.8.2)':
    '@nestjs/core': private
  '@nestjs/jwt@10.2.0(@nestjs/common@10.4.20(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(rxjs@7.8.2))':
    '@nestjs/jwt': private
  '@nestjs/mapped-types@2.1.0(@nestjs/common@10.4.20(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(rxjs@7.8.2))(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)':
    '@nestjs/mapped-types': private
  '@nestjs/passport@10.0.3(@nestjs/common@10.4.20(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(rxjs@7.8.2))(passport@0.7.0)':
    '@nestjs/passport': private
  '@nestjs/platform-express@10.4.20(@nestjs/common@10.4.20(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(rxjs@7.8.2))(@nestjs/core@10.4.20)':
    '@nestjs/platform-express': private
  '@nestjs/schematics@10.2.3(chokidar@3.6.0)(typescript@5.8.3)':
    '@nestjs/schematics': private
  '@nestjs/swagger@8.1.1(@nestjs/common@10.4.20(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(rxjs@7.8.2))(@nestjs/core@10.4.20)(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)':
    '@nestjs/swagger': private
  '@nestjs/testing@10.4.20(@nestjs/common@10.4.20(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(rxjs@7.8.2))(@nestjs/core@10.4.20)(@nestjs/platform-express@10.4.20)':
    '@nestjs/testing': private
  '@nestjs/typeorm@10.0.2(@nestjs/common@10.4.20(class-transformer@0.5.1)(class-validator@0.14.2)(reflect-metadata@0.2.2)(rxjs@7.8.2))(@nestjs/core@10.4.20)(reflect-metadata@0.2.2)(rxjs@7.8.2)(typeorm@0.3.25(babel-plugin-macros@3.1.0)(pg@8.16.3)(redis@4.7.1)(reflect-metadata@0.2.2)(ts-node@10.9.2(@types/node@22.17.0)(typescript@5.8.3)))':
    '@nestjs/typeorm': private
  '@noble/hashes@1.8.0':
    '@noble/hashes': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@nuxtjs/opencollective@0.3.2':
    '@nuxtjs/opencollective': private
  '@oozcitak/dom@1.15.10':
    '@oozcitak/dom': private
  '@oozcitak/infra@1.0.8':
    '@oozcitak/infra': private
  '@oozcitak/url@1.0.4':
    '@oozcitak/url': private
  '@oozcitak/util@8.3.8':
    '@oozcitak/util': private
  '@paralleldrive/cuid2@2.2.2':
    '@paralleldrive/cuid2': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@pkgr/core@0.2.9':
    '@pkgr/core': private
  '@popperjs/core@2.11.8':
    '@popperjs/core': private
  '@redis/bloom@1.2.0(@redis/client@1.6.1)':
    '@redis/bloom': private
  '@redis/client@1.6.1':
    '@redis/client': private
  '@redis/graph@1.1.1(@redis/client@1.6.1)':
    '@redis/graph': private
  '@redis/json@1.0.7(@redis/client@1.6.1)':
    '@redis/json': private
  '@redis/search@1.2.0(@redis/client@1.6.1)':
    '@redis/search': private
  '@redis/time-series@1.1.0(@redis/client@1.6.1)':
    '@redis/time-series': private
  '@remix-run/router@1.23.0':
    '@remix-run/router': private
  '@rolldown/pluginutils@1.0.0-beta.27':
    '@rolldown/pluginutils': private
  '@rollup/rollup-darwin-arm64@4.46.2':
    '@rollup/rollup-darwin-arm64': private
  '@scarf/scarf@1.4.0':
    '@scarf/scarf': private
  '@sinclair/typebox@0.27.8':
    '@sinclair/typebox': private
  '@sindresorhus/merge-streams@2.3.0':
    '@sindresorhus/merge-streams': private
  '@sinonjs/commons@3.0.1':
    '@sinonjs/commons': private
  '@sinonjs/fake-timers@10.3.0':
    '@sinonjs/fake-timers': private
  '@sqltools/formatter@1.2.5':
    '@sqltools/formatter': private
  '@tanstack/query-core@5.83.1':
    '@tanstack/query-core': private
  '@tanstack/react-query@5.84.1(react@18.3.1)':
    '@tanstack/react-query': private
  '@tokenizer/inflate@0.2.7':
    '@tokenizer/inflate': private
  '@tokenizer/token@0.3.0':
    '@tokenizer/token': private
  '@tootallnate/quickjs-emscripten@0.23.0':
    '@tootallnate/quickjs-emscripten': private
  '@tsconfig/node10@1.0.11':
    '@tsconfig/node10': private
  '@tsconfig/node12@1.0.11':
    '@tsconfig/node12': private
  '@tsconfig/node14@1.0.3':
    '@tsconfig/node14': private
  '@tsconfig/node16@1.0.4':
    '@tsconfig/node16': private
  '@types/babel__core@7.20.5':
    '@types/babel__core': private
  '@types/babel__generator@7.27.0':
    '@types/babel__generator': private
  '@types/babel__template@7.4.4':
    '@types/babel__template': private
  '@types/babel__traverse@7.28.0':
    '@types/babel__traverse': private
  '@types/bcryptjs@2.4.6':
    '@types/bcryptjs': private
  '@types/body-parser@1.19.6':
    '@types/body-parser': private
  '@types/connect@3.4.38':
    '@types/connect': private
  '@types/cookiejar@2.1.5':
    '@types/cookiejar': private
  '@types/d3-array@3.2.1':
    '@types/d3-array': private
  '@types/d3-color@3.1.3':
    '@types/d3-color': private
  '@types/d3-ease@3.0.2':
    '@types/d3-ease': private
  '@types/d3-interpolate@3.0.4':
    '@types/d3-interpolate': private
  '@types/d3-path@3.1.1':
    '@types/d3-path': private
  '@types/d3-scale@4.0.9':
    '@types/d3-scale': private
  '@types/d3-shape@3.1.7':
    '@types/d3-shape': private
  '@types/d3-time@3.0.4':
    '@types/d3-time': private
  '@types/d3-timer@3.0.2':
    '@types/d3-timer': private
  '@types/debug@4.1.12':
    '@types/debug': private
  '@types/eslint-scope@3.7.7':
    '@types/eslint-scope': private
  '@types/eslint@9.6.1':
    '@types/eslint': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/express-serve-static-core@5.0.7':
    '@types/express-serve-static-core': private
  '@types/express@5.0.3':
    '@types/express': private
  '@types/fs-extra@11.0.4':
    '@types/fs-extra': private
  '@types/graceful-fs@4.1.9':
    '@types/graceful-fs': private
  '@types/hash-sum@1.0.2':
    '@types/hash-sum': private
  '@types/http-errors@2.0.5':
    '@types/http-errors': private
  '@types/istanbul-lib-coverage@2.0.6':
    '@types/istanbul-lib-coverage': private
  '@types/istanbul-lib-report@3.0.3':
    '@types/istanbul-lib-report': private
  '@types/istanbul-reports@3.0.4':
    '@types/istanbul-reports': private
  '@types/jest@29.5.14':
    '@types/jest': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/jsonfile@6.1.4':
    '@types/jsonfile': private
  '@types/jsonwebtoken@9.0.5':
    '@types/jsonwebtoken': private
  '@types/linkify-it@5.0.0':
    '@types/linkify-it': private
  '@types/lodash@4.17.20':
    '@types/lodash': private
  '@types/markdown-it-emoji@3.0.1':
    '@types/markdown-it-emoji': private
  '@types/markdown-it@14.1.2':
    '@types/markdown-it': private
  '@types/mdurl@2.0.0':
    '@types/mdurl': private
  '@types/methods@1.1.4':
    '@types/methods': private
  '@types/mime@1.3.5':
    '@types/mime': private
  '@types/ms@2.1.0':
    '@types/ms': private
  '@types/multer@1.4.13':
    '@types/multer': private
  '@types/parse-json@4.0.2':
    '@types/parse-json': private
  '@types/passport-jwt@4.0.1':
    '@types/passport-jwt': private
  '@types/passport-local@1.0.38':
    '@types/passport-local': private
  '@types/passport-strategy@0.2.38':
    '@types/passport-strategy': private
  '@types/passport@1.0.17':
    '@types/passport': private
  '@types/prop-types@15.7.15':
    '@types/prop-types': private
  '@types/qs@6.14.0':
    '@types/qs': private
  '@types/range-parser@1.2.7':
    '@types/range-parser': private
  '@types/react-dom@18.3.7(@types/react@18.3.23)':
    '@types/react-dom': private
  '@types/react-transition-group@4.4.12(@types/react@18.3.23)':
    '@types/react-transition-group': private
  '@types/react@18.3.23':
    '@types/react': private
  '@types/send@0.17.5':
    '@types/send': private
  '@types/serve-static@1.15.8':
    '@types/serve-static': private
  '@types/stack-utils@2.0.3':
    '@types/stack-utils': private
  '@types/superagent@8.1.9':
    '@types/superagent': private
  '@types/supertest@6.0.3':
    '@types/supertest': private
  '@types/trusted-types@2.0.7':
    '@types/trusted-types': private
  '@types/uuid@10.0.0':
    '@types/uuid': private
  '@types/validator@13.15.2':
    '@types/validator': private
  '@types/web-bluetooth@0.0.21':
    '@types/web-bluetooth': private
  '@types/yargs-parser@21.0.3':
    '@types/yargs-parser': private
  '@types/yargs@17.0.33':
    '@types/yargs': private
  '@typescript-eslint/project-service@8.38.0(typescript@5.8.3)':
    '@typescript-eslint/project-service': private
  '@typescript-eslint/scope-manager@8.38.0':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/tsconfig-utils@8.38.0(typescript@5.8.3)':
    '@typescript-eslint/tsconfig-utils': private
  '@typescript-eslint/type-utils@8.38.0(eslint@9.32.0)(typescript@5.8.3)':
    '@typescript-eslint/type-utils': private
  '@typescript-eslint/types@8.38.0':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@8.38.0(typescript@5.8.3)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@8.38.0(eslint@9.32.0)(typescript@5.8.3)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@8.38.0':
    '@typescript-eslint/visitor-keys': private
  '@vitejs/plugin-react@4.7.0(vite@6.3.5(@types/node@24.2.0)(terser@5.43.1))':
    '@vitejs/plugin-react': private
  '@vue/compiler-core@3.5.18':
    '@vue/compiler-core': private
  '@vue/compiler-dom@3.5.18':
    '@vue/compiler-dom': private
  '@vue/compiler-sfc@3.5.18':
    '@vue/compiler-sfc': private
  '@vue/compiler-ssr@3.5.18':
    '@vue/compiler-ssr': private
  '@vue/devtools-api@7.7.7':
    '@vue/devtools-api': private
  '@vue/devtools-kit@7.7.7':
    '@vue/devtools-kit': private
  '@vue/devtools-shared@7.7.7':
    '@vue/devtools-shared': private
  '@vue/reactivity@3.5.18':
    '@vue/reactivity': private
  '@vue/runtime-core@3.5.18':
    '@vue/runtime-core': private
  '@vue/runtime-dom@3.5.18':
    '@vue/runtime-dom': private
  '@vue/server-renderer@3.5.18(vue@3.5.18(typescript@5.9.2))':
    '@vue/server-renderer': private
  '@vue/shared@3.5.18':
    '@vue/shared': private
  '@vuepress/cli@2.0.0-rc.24(typescript@5.9.2)':
    '@vuepress/cli': private
  '@vuepress/client@2.0.0-rc.24(typescript@5.9.2)':
    '@vuepress/client': private
  '@vuepress/core@2.0.0-rc.24(typescript@5.9.2)':
    '@vuepress/core': private
  '@vuepress/helper@2.0.0-rc.112(typescript@5.9.2)(vuepress@2.0.0-rc.24(typescript@5.9.2)(vue@3.5.18(typescript@5.9.2)))':
    '@vuepress/helper': private
  '@vuepress/markdown@2.0.0-rc.24':
    '@vuepress/markdown': private
  '@vuepress/plugin-back-to-top@2.0.0-rc.112(typescript@5.9.2)(vuepress@2.0.0-rc.24(typescript@5.9.2)(vue@3.5.18(typescript@5.9.2)))':
    '@vuepress/plugin-back-to-top': private
  '@vuepress/plugin-medium-zoom@2.0.0-rc.112(typescript@5.9.2)(vuepress@2.0.0-rc.24(typescript@5.9.2)(vue@3.5.18(typescript@5.9.2)))':
    '@vuepress/plugin-medium-zoom': private
  '@vuepress/plugin-nprogress@2.0.0-rc.112(typescript@5.9.2)(vuepress@2.0.0-rc.24(typescript@5.9.2)(vue@3.5.18(typescript@5.9.2)))':
    '@vuepress/plugin-nprogress': private
  '@vuepress/plugin-search@2.0.0-rc.112(typescript@5.9.2)(vuepress@2.0.0-rc.24(typescript@5.9.2)(vue@3.5.18(typescript@5.9.2)))':
    '@vuepress/plugin-search': private
  '@vuepress/shared@2.0.0-rc.24':
    '@vuepress/shared': private
  '@vuepress/utils@2.0.0-rc.24':
    '@vuepress/utils': private
  '@vueuse/core@13.6.0(vue@3.5.18(typescript@5.9.2))':
    '@vueuse/core': private
  '@vueuse/metadata@13.6.0':
    '@vueuse/metadata': private
  '@vueuse/shared@13.6.0(vue@3.5.18(typescript@5.9.2))':
    '@vueuse/shared': private
  '@webassemblyjs/ast@1.14.1':
    '@webassemblyjs/ast': private
  '@webassemblyjs/floating-point-hex-parser@1.13.2':
    '@webassemblyjs/floating-point-hex-parser': private
  '@webassemblyjs/helper-api-error@1.13.2':
    '@webassemblyjs/helper-api-error': private
  '@webassemblyjs/helper-buffer@1.14.1':
    '@webassemblyjs/helper-buffer': private
  '@webassemblyjs/helper-numbers@1.13.2':
    '@webassemblyjs/helper-numbers': private
  '@webassemblyjs/helper-wasm-bytecode@1.13.2':
    '@webassemblyjs/helper-wasm-bytecode': private
  '@webassemblyjs/helper-wasm-section@1.14.1':
    '@webassemblyjs/helper-wasm-section': private
  '@webassemblyjs/ieee754@1.13.2':
    '@webassemblyjs/ieee754': private
  '@webassemblyjs/leb128@1.13.2':
    '@webassemblyjs/leb128': private
  '@webassemblyjs/utf8@1.13.2':
    '@webassemblyjs/utf8': private
  '@webassemblyjs/wasm-edit@1.14.1':
    '@webassemblyjs/wasm-edit': private
  '@webassemblyjs/wasm-gen@1.14.1':
    '@webassemblyjs/wasm-gen': private
  '@webassemblyjs/wasm-opt@1.14.1':
    '@webassemblyjs/wasm-opt': private
  '@webassemblyjs/wasm-parser@1.14.1':
    '@webassemblyjs/wasm-parser': private
  '@webassemblyjs/wast-printer@1.14.1':
    '@webassemblyjs/wast-printer': private
  '@xtuc/ieee754@1.2.0':
    '@xtuc/ieee754': private
  '@xtuc/long@4.2.2':
    '@xtuc/long': private
  accepts@1.3.8:
    accepts: private
  acorn-jsx@5.3.2(acorn@8.15.0):
    acorn-jsx: private
  acorn-walk@8.3.4:
    acorn-walk: private
  acorn@8.15.0:
    acorn: private
  agent-base@7.1.4:
    agent-base: private
  ajv-formats@2.1.1(ajv@8.12.0):
    ajv-formats: private
  ajv-keywords@3.5.2(ajv@6.12.6):
    ajv-keywords: private
  ajv@6.12.6:
    ajv: private
  ajv@8.12.0:
    ajv: private
  ajv@8.17.1:
    ajv: private
  ansi-colors@4.1.3:
    ansi-colors: private
  ansi-escapes@4.3.2:
    ansi-escapes: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  ansis@3.17.0:
    ansis: private
  anymatch@3.1.3:
    anymatch: private
  app-root-path@3.1.0:
    app-root-path: private
  append-field@1.0.0:
    append-field: private
  arg@4.1.3:
    arg: private
  argparse@2.0.1:
    argparse: private
  array-flatten@1.1.1:
    array-flatten: private
  array-timsort@1.0.3:
    array-timsort: private
  asap@2.0.6:
    asap: private
  ast-types@0.13.4:
    ast-types: private
  async@3.2.6:
    async: private
  asynckit@0.4.0:
    asynckit: private
  attr-accept@2.2.5:
    attr-accept: private
  autosuggest-highlight@3.3.4:
    autosuggest-highlight: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  axios@1.11.0:
    axios: private
  babel-jest@29.7.0(@babel/core@7.28.0):
    babel-jest: private
  babel-plugin-istanbul@6.1.1:
    babel-plugin-istanbul: private
  babel-plugin-jest-hoist@29.6.3:
    babel-plugin-jest-hoist: private
  babel-plugin-macros@3.1.0:
    babel-plugin-macros: private
  babel-preset-current-node-syntax@1.2.0(@babel/core@7.28.0):
    babel-preset-current-node-syntax: private
  babel-preset-jest@29.6.3(@babel/core@7.28.0):
    babel-preset-jest: private
  balanced-match@1.0.2:
    balanced-match: private
  base64-js@1.5.1:
    base64-js: private
  basic-ftp@5.0.5:
    basic-ftp: private
  bcryptjs@2.4.3:
    bcryptjs: private
  binary-extensions@2.3.0:
    binary-extensions: private
  birpc@2.5.0:
    birpc: private
  bl@4.1.0:
    bl: private
  body-parser@1.20.3:
    body-parser: private
  boolbase@1.0.0:
    boolbase: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.25.1:
    browserslist: private
  bs-logger@0.2.6:
    bs-logger: private
  bser@2.1.1:
    bser: private
  buffer-equal-constant-time@1.0.1:
    buffer-equal-constant-time: private
  buffer-from@1.1.2:
    buffer-from: private
  buffer@6.0.3:
    buffer: private
  busboy@1.6.0:
    busboy: private
  bytes@3.1.2:
    bytes: private
  cac@6.7.14:
    cac: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bind@1.0.8:
    call-bind: private
  call-bound@1.0.4:
    call-bound: private
  callsites@3.1.0:
    callsites: private
  camelcase@6.3.0:
    camelcase: private
  caniuse-lite@1.0.30001731:
    caniuse-lite: private
  chalk@4.1.2:
    chalk: private
  char-regex@1.0.2:
    char-regex: private
  chardet@0.7.0:
    chardet: private
  cheerio-select@2.1.0:
    cheerio-select: private
  cheerio@1.1.2:
    cheerio: private
  chokidar@3.6.0:
    chokidar: private
  chrome-trace-event@1.0.4:
    chrome-trace-event: private
  ci-info@3.9.0:
    ci-info: private
  cjs-module-lexer@1.4.3:
    cjs-module-lexer: private
  class-transformer@0.5.1:
    class-transformer: private
  class-validator@0.14.2:
    class-validator: private
  cli-cursor@3.1.0:
    cli-cursor: private
  cli-spinners@2.9.2:
    cli-spinners: private
  cli-table3@0.6.5:
    cli-table3: private
  cli-width@3.0.0:
    cli-width: private
  cliui@8.0.1:
    cliui: private
  clone@1.0.4:
    clone: private
  clsx@2.1.1:
    clsx: private
  cluster-key-slot@1.1.2:
    cluster-key-slot: private
  co@4.6.0:
    co: private
  collect-v8-coverage@1.0.2:
    collect-v8-coverage: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  combined-stream@1.0.8:
    combined-stream: private
  commander@4.1.1:
    commander: private
  comment-json@4.2.5:
    comment-json: private
  component-emitter@1.3.1:
    component-emitter: private
  concat-map@0.0.1:
    concat-map: private
  concat-stream@1.6.2:
    concat-stream: private
  consola@2.15.3:
    consola: private
  content-disposition@0.5.4:
    content-disposition: private
  content-type@1.0.5:
    content-type: private
  convert-source-map@2.0.0:
    convert-source-map: private
  cookie-signature@1.0.6:
    cookie-signature: private
  cookie@1.0.2:
    cookie: private
  cookiejar@2.1.4:
    cookiejar: private
  copy-anything@3.0.5:
    copy-anything: private
  core-util-is@1.0.3:
    core-util-is: private
  cors@2.8.5:
    cors: private
  cosmiconfig@7.1.0:
    cosmiconfig: private
  create-jest@29.7.0(@types/node@22.17.0)(babel-plugin-macros@3.1.0)(ts-node@10.9.2(@types/node@22.17.0)(typescript@5.8.3)):
    create-jest: private
  create-require@1.1.1:
    create-require: private
  cross-spawn@7.0.6:
    cross-spawn: private
  css-mediaquery@0.1.2:
    css-mediaquery: private
  css-select@5.2.2:
    css-select: private
  css-what@6.2.2:
    css-what: private
  csstype@3.1.3:
    csstype: private
  d3-array@3.2.4:
    d3-array: private
  d3-color@3.1.0:
    d3-color: private
  d3-ease@3.0.1:
    d3-ease: private
  d3-format@3.1.0:
    d3-format: private
  d3-interpolate@3.0.1:
    d3-interpolate: private
  d3-path@3.1.0:
    d3-path: private
  d3-scale@4.0.2:
    d3-scale: private
  d3-shape@3.2.0:
    d3-shape: private
  d3-time-format@4.1.0:
    d3-time-format: private
  d3-time@3.1.0:
    d3-time: private
  d3-timer@3.0.1:
    d3-timer: private
  data-uri-to-buffer@6.0.2:
    data-uri-to-buffer: private
  date-fns@4.1.0:
    date-fns: private
  dayjs@1.11.13:
    dayjs: private
  debug@4.4.1:
    debug: private
  decimal.js-light@2.5.1:
    decimal.js-light: private
  decode-uri-component@0.2.2:
    decode-uri-component: private
  decode-uri-component@0.4.1:
    decode-uri-component: private
  dedent@1.6.0(babel-plugin-macros@3.1.0):
    dedent: private
  deep-extend@0.6.0:
    deep-extend: private
  deep-is@0.1.4:
    deep-is: private
  deepmerge@4.3.1:
    deepmerge: private
  defaults@1.0.4:
    defaults: private
  define-data-property@1.1.4:
    define-data-property: private
  define-properties@1.2.1:
    define-properties: private
  degenerator@5.0.1:
    degenerator: private
  delayed-stream@1.0.0:
    delayed-stream: private
  depd@2.0.0:
    depd: private
  destroy@1.2.0:
    destroy: private
  detect-newline@3.1.0:
    detect-newline: private
  dezalgo@1.0.4:
    dezalgo: private
  diacritic@0.0.2:
    diacritic: private
  diff-sequences@29.6.3:
    diff-sequences: private
  diff@4.0.2:
    diff: private
  dom-helpers@5.2.1:
    dom-helpers: private
  dom-serializer@2.0.0:
    dom-serializer: private
  domelementtype@2.3.0:
    domelementtype: private
  domhandler@5.0.3:
    domhandler: private
  dompurify@3.2.6:
    dompurify: private
  domutils@3.2.2:
    domutils: private
  dotenv-expand@10.0.0:
    dotenv-expand: private
  dotenv@16.4.5:
    dotenv: private
  dunder-proto@1.0.1:
    dunder-proto: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  ecdsa-sig-formatter@1.0.11:
    ecdsa-sig-formatter: private
  ee-first@1.1.1:
    ee-first: private
  electron-to-chromium@1.5.194:
    electron-to-chromium: private
  emittery@0.13.1:
    emittery: private
  emoji-regex@8.0.0:
    emoji-regex: private
  encodeurl@2.0.0:
    encodeurl: private
  encoding-sniffer@0.2.1:
    encoding-sniffer: private
  enhanced-resolve@5.18.2:
    enhanced-resolve: private
  entities@4.5.0:
    entities: private
  envinfo@7.14.0:
    envinfo: private
  error-ex@1.3.2:
    error-ex: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-module-lexer@1.7.0:
    es-module-lexer: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  esbuild@0.25.8:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-html@1.0.3:
    escape-html: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  escodegen@2.1.0:
    escodegen: private
  eslint-plugin-react-hooks@5.2.0(eslint@9.32.0):
    eslint-plugin-react-hooks: private
  eslint-plugin-react-refresh@0.4.20(eslint@9.32.0):
    eslint-plugin-react-refresh: private
  eslint-scope@8.4.0:
    eslint-scope: private
  eslint-visitor-keys@4.2.1:
    eslint-visitor-keys: private
  espree@10.4.0:
    espree: private
  esprima@4.0.1:
    esprima: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@4.3.0:
    estraverse: private
  estraverse@5.3.0:
    estraverse: private
  estree-walker@2.0.2:
    estree-walker: private
  esutils@2.0.3:
    esutils: private
  etag@1.8.1:
    etag: private
  eventemitter3@5.0.1:
    eventemitter3: private
  events@3.3.0:
    events: private
  execa@5.1.1:
    execa: private
  exit@0.1.2:
    exit: private
  expect@29.7.0:
    expect: private
  express@4.21.2:
    express: private
  extend-shallow@2.0.1:
    extend-shallow: private
  external-editor@3.1.0:
    external-editor: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-diff@1.3.0:
    fast-diff: private
  fast-equals@5.2.2:
    fast-equals: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fast-safe-stringify@2.1.1:
    fast-safe-stringify: private
  fast-uri@3.0.6:
    fast-uri: private
  fastq@1.19.1:
    fastq: private
  fb-watchman@2.0.2:
    fb-watchman: private
  fdir@6.4.6(picomatch@4.0.3):
    fdir: private
  fflate@0.8.2:
    fflate: private
  figures@3.2.0:
    figures: private
  file-entry-cache@8.0.0:
    file-entry-cache: private
  file-selector@2.1.2:
    file-selector: private
  file-type@20.4.1:
    file-type: private
  fill-range@7.1.1:
    fill-range: private
  filter-obj@1.1.0:
    filter-obj: private
  filter-obj@5.1.0:
    filter-obj: private
  finalhandler@1.3.1:
    finalhandler: private
  find-root@1.1.0:
    find-root: private
  find-up@5.0.0:
    find-up: private
  flat-cache@4.0.1:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  follow-redirects@1.15.11:
    follow-redirects: private
  for-each@0.3.5:
    for-each: private
  foreground-child@3.3.1:
    foreground-child: private
  fork-ts-checker-webpack-plugin@9.0.2(typescript@5.7.2)(webpack@5.97.1):
    fork-ts-checker-webpack-plugin: private
  form-data@4.0.4:
    form-data: private
  formidable@3.5.4:
    formidable: private
  forwarded@0.2.0:
    forwarded: private
  fresh@0.5.2:
    fresh: private
  fs-extra@11.3.0:
    fs-extra: private
  fs-monkey@1.1.0:
    fs-monkey: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  generic-pool@3.9.0:
    generic-pool: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-east-asian-width@1.3.0:
    get-east-asian-width: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-package-type@0.1.0:
    get-package-type: private
  get-proto@1.0.1:
    get-proto: private
  get-stdin@9.0.0:
    get-stdin: private
  get-stream@6.0.1:
    get-stream: private
  get-uri@6.0.5:
    get-uri: private
  glob-parent@5.1.2:
    glob-parent: private
  glob-parent@6.0.2:
    glob-parent: private
  glob-to-regexp@0.4.1:
    glob-to-regexp: private
  glob@10.4.5:
    glob: private
  globals@14.0.0:
    globals: private
  globby@14.1.0:
    globby: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  gray-matter@4.0.3:
    gray-matter: private
  handlebars@4.7.8:
    handlebars: private
  has-flag@4.0.0:
    has-flag: private
  has-own-prop@2.0.0:
    has-own-prop: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hash-sum@2.0.0:
    hash-sum: private
  hasown@2.0.2:
    hasown: private
  hoist-non-react-statics@3.3.2:
    hoist-non-react-statics: private
  hookable@5.5.3:
    hookable: private
  html-escaper@2.0.2:
    html-escaper: private
  html-link-extractor@1.0.5:
    html-link-extractor: private
  htmlparser2@10.0.0:
    htmlparser2: private
  http-errors@2.0.0:
    http-errors: private
  http-proxy-agent@7.0.2:
    http-proxy-agent: private
  https-proxy-agent@7.0.6:
    https-proxy-agent: private
  human-signals@2.1.0:
    human-signals: private
  iconv-lite@0.4.24:
    iconv-lite: private
  ieee754@1.2.1:
    ieee754: private
  ignore@7.0.5:
    ignore: private
  import-fresh@3.3.1:
    import-fresh: private
  import-local@3.2.0:
    import-local: private
  imurmurhash@0.1.4:
    imurmurhash: private
  inflection@3.0.2:
    inflection: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  ini@4.1.3:
    ini: private
  inquirer@8.2.6:
    inquirer: private
  internmap@2.0.3:
    internmap: private
  ip-address@9.0.5:
    ip-address: private
  ipaddr.js@1.9.1:
    ipaddr.js: private
  is-absolute-url@4.0.1:
    is-absolute-url: private
  is-arrayish@0.2.1:
    is-arrayish: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-callable@1.2.7:
    is-callable: private
  is-core-module@2.16.1:
    is-core-module: private
  is-extendable@0.1.1:
    is-extendable: private
  is-extglob@2.1.1:
    is-extglob: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-generator-fn@2.1.0:
    is-generator-fn: private
  is-glob@4.0.3:
    is-glob: private
  is-interactive@1.0.0:
    is-interactive: private
  is-number@7.0.0:
    is-number: private
  is-relative-url@4.0.0:
    is-relative-url: private
  is-stream@2.0.1:
    is-stream: private
  is-typed-array@1.1.15:
    is-typed-array: private
  is-unicode-supported@0.1.0:
    is-unicode-supported: private
  is-what@4.1.16:
    is-what: private
  isarray@1.0.0:
    isarray: private
  isexe@2.0.0:
    isexe: private
  istanbul-lib-coverage@3.2.2:
    istanbul-lib-coverage: private
  istanbul-lib-instrument@5.2.1:
    istanbul-lib-instrument: private
  istanbul-lib-report@3.0.1:
    istanbul-lib-report: private
  istanbul-lib-source-maps@4.0.1:
    istanbul-lib-source-maps: private
  istanbul-reports@3.1.7:
    istanbul-reports: private
  iterare@1.2.1:
    iterare: private
  jackspeak@3.4.3:
    jackspeak: private
  jest-changed-files@29.7.0:
    jest-changed-files: private
  jest-circus@29.7.0(babel-plugin-macros@3.1.0):
    jest-circus: private
  jest-cli@29.7.0(@types/node@22.17.0)(babel-plugin-macros@3.1.0)(ts-node@10.9.2(@types/node@22.17.0)(typescript@5.8.3)):
    jest-cli: private
  jest-config@29.7.0(@types/node@22.17.0)(babel-plugin-macros@3.1.0)(ts-node@10.9.2(@types/node@22.17.0)(typescript@5.8.3)):
    jest-config: private
  jest-diff@29.7.0:
    jest-diff: private
  jest-docblock@29.7.0:
    jest-docblock: private
  jest-each@29.7.0:
    jest-each: private
  jest-environment-node@29.7.0:
    jest-environment-node: private
  jest-get-type@29.6.3:
    jest-get-type: private
  jest-haste-map@29.7.0:
    jest-haste-map: private
  jest-leak-detector@29.7.0:
    jest-leak-detector: private
  jest-matcher-utils@29.7.0:
    jest-matcher-utils: private
  jest-message-util@29.7.0:
    jest-message-util: private
  jest-mock@29.7.0:
    jest-mock: private
  jest-pnp-resolver@1.2.3(jest-resolve@29.7.0):
    jest-pnp-resolver: private
  jest-regex-util@29.6.3:
    jest-regex-util: private
  jest-resolve-dependencies@29.7.0:
    jest-resolve-dependencies: private
  jest-resolve@29.7.0:
    jest-resolve: private
  jest-runner@29.7.0:
    jest-runner: private
  jest-runtime@29.7.0:
    jest-runtime: private
  jest-snapshot@29.7.0:
    jest-snapshot: private
  jest-util@29.7.0:
    jest-util: private
  jest-validate@29.7.0:
    jest-validate: private
  jest-watcher@29.7.0:
    jest-watcher: private
  jest-worker@29.7.0:
    jest-worker: private
  jest@29.7.0(@types/node@22.17.0)(babel-plugin-macros@3.1.0)(ts-node@10.9.2(@types/node@22.17.0)(typescript@5.8.3)):
    jest: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsbn@1.1.0:
    jsbn: private
  jsesc@3.1.0:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-schema-traverse@1.0.0:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json5@2.2.3:
    json5: private
  jsonc-parser@3.3.1:
    jsonc-parser: private
  jsonexport@3.2.0:
    jsonexport: private
  jsonfile@6.1.0:
    jsonfile: private
  jsonwebtoken@9.0.2:
    jsonwebtoken: private
  jwa@1.4.2:
    jwa: private
  jws@3.2.2:
    jws: private
  keyv@4.5.4:
    keyv: private
  kind-of@6.0.3:
    kind-of: private
  kleur@3.0.3:
    kleur: private
  leven@3.1.0:
    leven: private
  levn@0.4.1:
    levn: private
  libphonenumber-js@1.12.10:
    libphonenumber-js: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  link-check@5.4.0:
    link-check: private
  linkify-it@5.0.0:
    linkify-it: private
  loader-runner@4.3.0:
    loader-runner: private
  locate-path@6.0.0:
    locate-path: private
  lodash.includes@4.3.0:
    lodash.includes: private
  lodash.isboolean@3.0.3:
    lodash.isboolean: private
  lodash.isinteger@4.0.4:
    lodash.isinteger: private
  lodash.isnumber@3.0.3:
    lodash.isnumber: private
  lodash.isplainobject@4.0.6:
    lodash.isplainobject: private
  lodash.isstring@4.0.1:
    lodash.isstring: private
  lodash.memoize@4.1.2:
    lodash.memoize: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash.once@4.1.1:
    lodash.once: private
  lodash@4.17.21:
    lodash: private
  log-symbols@4.1.0:
    log-symbols: private
  loose-envify@1.4.0:
    loose-envify: private
  lru-cache@5.1.1:
    lru-cache: private
  magic-string@0.30.8:
    magic-string: private
  make-dir@4.0.0:
    make-dir: private
  make-error@1.3.6:
    make-error: private
  makeerror@1.0.12:
    makeerror: private
  markdown-it-anchor@9.2.0(@types/markdown-it@14.1.2)(markdown-it@14.1.0):
    markdown-it-anchor: private
  markdown-it-emoji@3.0.0:
    markdown-it-emoji: private
  markdown-it@14.1.0:
    markdown-it: private
  markdown-link-check@3.13.7:
    markdown-link-check: private
  markdown-link-extractor@4.0.2:
    markdown-link-extractor: private
  markdownlint-cli@0.37.0:
    markdownlint-cli: private
  markdownlint-micromark@0.1.7:
    markdownlint-micromark: private
  markdownlint@0.31.1:
    markdownlint: private
  marked@12.0.2:
    marked: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  mdurl@2.0.0:
    mdurl: private
  media-typer@0.3.0:
    media-typer: private
  medium-zoom@1.1.0:
    medium-zoom: private
  memfs@3.5.3:
    memfs: private
  merge-descriptors@1.0.3:
    merge-descriptors: private
  merge-stream@2.0.0:
    merge-stream: private
  merge2@1.4.1:
    merge2: private
  methods@1.1.2:
    methods: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mime@2.6.0:
    mime: private
  mimic-fn@2.1.0:
    mimic-fn: private
  mimic-function@5.0.1:
    mimic-function: private
  minimatch@3.1.2:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass@7.1.2:
    minipass: private
  mitt@3.0.1:
    mitt: private
  mkdirp@0.5.6:
    mkdirp: private
  ms@2.1.3:
    ms: private
  multer@1.4.5-lts.2:
    multer: private
  mute-stream@0.0.8:
    mute-stream: private
  nanoid@3.3.11:
    nanoid: private
  natural-compare@1.4.0:
    natural-compare: private
  needle@3.3.1:
    needle: private
  negotiator@0.6.3:
    negotiator: private
  neo-async@2.6.2:
    neo-async: private
  netmask@2.0.2:
    netmask: private
  node-abort-controller@3.1.1:
    node-abort-controller: private
  node-email-verifier@2.0.0:
    node-email-verifier: private
  node-emoji@1.11.0:
    node-emoji: private
  node-fetch@2.7.0:
    node-fetch: private
  node-int64@0.4.0:
    node-int64: private
  node-polyglot@2.6.0:
    node-polyglot: private
  node-releases@2.0.19:
    node-releases: private
  normalize-path@3.0.0:
    normalize-path: private
  npm-run-path@4.0.1:
    npm-run-path: private
  nth-check@2.1.1:
    nth-check: private
  object-assign@4.1.1:
    object-assign: private
  object-inspect@1.13.4:
    object-inspect: private
  object-keys@1.1.1:
    object-keys: private
  object.entries@1.1.9:
    object.entries: private
  on-finished@2.4.1:
    on-finished: private
  once@1.4.0:
    once: private
  onetime@5.1.2:
    onetime: private
  optionator@0.9.4:
    optionator: private
  ora@5.4.1:
    ora: private
  os-tmpdir@1.0.2:
    os-tmpdir: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  p-try@2.2.0:
    p-try: private
  pac-proxy-agent@7.2.0:
    pac-proxy-agent: private
  pac-resolver@7.0.1:
    pac-resolver: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  parent-module@1.0.1:
    parent-module: private
  parse-json@5.2.0:
    parse-json: private
  parse5-htmlparser2-tree-adapter@7.1.0:
    parse5-htmlparser2-tree-adapter: private
  parse5-parser-stream@7.1.2:
    parse5-parser-stream: private
  parse5@7.3.0:
    parse5: private
  parseurl@1.3.3:
    parseurl: private
  passport-jwt@4.0.1:
    passport-jwt: private
  passport-local@1.0.0:
    passport-local: private
  passport-strategy@1.0.0:
    passport-strategy: private
  passport@0.7.0:
    passport: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@1.11.1:
    path-scurry: private
  path-to-regexp@3.3.0:
    path-to-regexp: private
  path-type@4.0.0:
    path-type: private
  pause@0.0.1:
    pause: private
  perfect-debounce@1.0.0:
    perfect-debounce: private
  pg-cloudflare@1.2.7:
    pg-cloudflare: private
  pg-connection-string@2.9.1:
    pg-connection-string: private
  pg-int8@1.0.1:
    pg-int8: private
  pg-pool@3.10.1(pg@8.16.3):
    pg-pool: private
  pg-protocol@1.10.3:
    pg-protocol: private
  pg-types@2.2.0:
    pg-types: private
  pg@8.16.3:
    pg: private
  pgpass@1.0.5:
    pgpass: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@2.3.1:
    picomatch: private
  picomatch@4.0.3:
    picomatch: private
  pirates@4.0.7:
    pirates: private
  pkg-dir@4.2.0:
    pkg-dir: private
  pluralize@8.0.0:
    pluralize: private
  possible-typed-array-names@1.1.0:
    possible-typed-array-names: private
  postcss@8.5.6:
    postcss: private
  postgres-array@2.0.0:
    postgres-array: private
  postgres-bytea@1.0.0:
    postgres-bytea: private
  postgres-date@1.0.7:
    postgres-date: private
  postgres-interval@1.2.0:
    postgres-interval: private
  prelude-ls@1.2.1:
    prelude-ls: private
  prettier-linter-helpers@1.0.0:
    prettier-linter-helpers: private
  pretty-format@29.7.0:
    pretty-format: private
  process-nextick-args@2.0.1:
    process-nextick-args: private
  progress@2.0.3:
    progress: private
  prompts@2.4.2:
    prompts: private
  prop-types@15.8.1:
    prop-types: private
  proxy-addr@2.0.7:
    proxy-addr: private
  proxy-agent@6.5.0:
    proxy-agent: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  punycode.js@2.3.1:
    punycode.js: private
  punycode@2.3.1:
    punycode: private
  pure-rand@6.1.0:
    pure-rand: private
  qs@6.13.0:
    qs: private
  query-string@7.1.3:
    query-string: private
  query-string@9.2.2:
    query-string: private
  queue-microtask@1.2.3:
    queue-microtask: private
  ra-core@5.10.1(react-dom@18.3.1(react@18.3.1))(react-hook-form@7.62.0(react@18.3.1))(react-router-dom@6.30.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-router@7.7.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react@18.3.1):
    ra-core: private
  ra-data-simple-rest@5.10.1(ra-core@5.10.1(react-dom@18.3.1(react@18.3.1))(react-hook-form@7.62.0(react@18.3.1))(react-router-dom@6.30.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-router@7.7.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react@18.3.1)):
    ra-data-simple-rest: private
  ra-i18n-polyglot@5.10.1(react-dom@18.3.1(react@18.3.1))(react-hook-form@7.62.0(react@18.3.1))(react-router-dom@6.30.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-router@7.7.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react@18.3.1):
    ra-i18n-polyglot: private
  ra-language-chinese@2.0.10:
    ra-language-chinese: private
  ra-language-english@5.10.1(react-dom@18.3.1(react@18.3.1))(react-hook-form@7.62.0(react@18.3.1))(react-router-dom@6.30.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react-router@7.7.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(react@18.3.1):
    ra-language-english: private
  ra-ui-materialui@5.10.1(3f66e586fcb40e7a2c4e73e6a30c6b13):
    ra-ui-materialui: private
  randombytes@2.1.0:
    randombytes: private
  range-parser@1.2.1:
    range-parser: private
  raw-body@2.5.2:
    raw-body: private
  react-admin@5.10.1(@mui/system@6.5.0(@emotion/react@11.14.0(@types/react@18.3.23)(react@18.3.1))(@emotion/styled@11.14.1(@emotion/react@11.14.0(@types/react@18.3.23)(react@18.3.1))(@types/react@18.3.23)(react@18.3.1))(@types/react@18.3.23)(react@18.3.1))(@mui/utils@6.4.9(@types/react@18.3.23)(react@18.3.1))(@types/react@18.3.23)(csstype@3.1.3)(react-dom@18.3.1(react@18.3.1))(react-is@19.1.1)(react@18.3.1):
    react-admin: private
  react-dom@18.3.1(react@18.3.1):
    react-dom: private
  react-dropzone@14.3.8(react@18.3.1):
    react-dropzone: private
  react-error-boundary@4.1.2(react@18.3.1):
    react-error-boundary: private
  react-hook-form@7.62.0(react@18.3.1):
    react-hook-form: private
  react-hotkeys-hook@5.1.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    react-hotkeys-hook: private
  react-is@19.1.1:
    react-is: private
  react-refresh@0.17.0:
    react-refresh: private
  react-router-dom@6.30.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    react-router-dom: private
  react-router@7.7.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    react-router: private
  react-smooth@4.0.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    react-smooth: private
  react-transition-group@4.4.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    react-transition-group: private
  react@18.3.1:
    react: private
  readable-stream@2.3.8:
    readable-stream: private
  readdirp@3.6.0:
    readdirp: private
  recharts-scale@0.4.5:
    recharts-scale: private
  recharts@2.15.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    recharts: private
  redis@4.7.1:
    redis: private
  reflect-metadata@0.2.2:
    reflect-metadata: private
  remove-accents@0.4.4:
    remove-accents: private
  repeat-string@1.6.1:
    repeat-string: private
  require-directory@2.1.1:
    require-directory: private
  require-from-string@2.0.2:
    require-from-string: private
  resolve-cwd@3.0.0:
    resolve-cwd: private
  resolve-from@4.0.0:
    resolve-from: private
  resolve-from@5.0.0:
    resolve-from: private
  resolve.exports@2.0.3:
    resolve.exports: private
  resolve@1.22.10:
    resolve: private
  restore-cursor@3.1.0:
    restore-cursor: private
  reusify@1.1.0:
    reusify: private
  rfdc@1.4.1:
    rfdc: private
  rollup@4.46.2:
    rollup: private
  run-async@2.4.1:
    run-async: private
  run-con@1.3.2:
    run-con: private
  run-parallel@1.2.0:
    run-parallel: private
  rxjs@7.8.2:
    rxjs: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safer-buffer@2.1.2:
    safer-buffer: private
  sax@1.4.1:
    sax: private
  scheduler@0.23.2:
    scheduler: private
  schema-utils@3.3.0:
    schema-utils: private
  section-matter@1.0.0:
    section-matter: private
  semver@6.3.1:
    semver: private
  semver@7.7.2:
    semver: private
  send@0.19.0:
    send: private
  serialize-javascript@6.0.2:
    serialize-javascript: private
  serve-static@1.16.2:
    serve-static: private
  set-cookie-parser@2.7.1:
    set-cookie-parser: private
  set-function-length@1.2.2:
    set-function-length: private
  setprototypeof@1.2.0:
    setprototypeof: private
  sha.js@2.4.12:
    sha.js: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  signal-exit@3.0.7:
    signal-exit: private
  sisteransi@1.0.5:
    sisteransi: private
  slash@3.0.0:
    slash: private
  smart-buffer@4.2.0:
    smart-buffer: private
  socks-proxy-agent@8.0.5:
    socks-proxy-agent: private
  socks@2.8.6:
    socks: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map@0.6.1:
    source-map: private
  speakingurl@14.0.1:
    speakingurl: private
  split-on-first@1.1.0:
    split-on-first: private
  split-on-first@3.0.0:
    split-on-first: private
  split2@4.2.0:
    split2: private
  sprintf-js@1.0.3:
    sprintf-js: private
  sql-highlight@6.1.0:
    sql-highlight: private
  stack-utils@2.0.6:
    stack-utils: private
  statuses@2.0.1:
    statuses: private
  stdin-discarder@0.2.2:
    stdin-discarder: private
  streamsearch@1.1.0:
    streamsearch: private
  strict-uri-encode@2.0.0:
    strict-uri-encode: private
  string-length@4.0.2:
    string-length: private
  string-width@4.2.3:
    string-width: private
    string-width-cjs: private
  string_decoder@1.1.1:
    string_decoder: private
  strip-ansi@6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  strip-bom-string@1.0.0:
    strip-bom-string: private
  strip-bom@3.0.0:
    strip-bom: private
  strip-final-newline@2.0.0:
    strip-final-newline: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  strtok3@10.3.4:
    strtok3: private
  stylis@4.2.0:
    stylis: private
  superagent@10.2.3:
    superagent: private
  superjson@2.2.2:
    superjson: private
  supertest@7.1.4:
    supertest: private
  supports-color@7.2.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  swagger-ui-dist@5.18.2:
    swagger-ui-dist: private
  symbol-observable@4.0.0:
    symbol-observable: private
  synckit@0.11.11:
    synckit: private
  tapable@2.2.2:
    tapable: private
  terser-webpack-plugin@5.3.14(webpack@5.97.1):
    terser-webpack-plugin: private
  terser@5.43.1:
    terser: private
  test-exclude@6.0.0:
    test-exclude: private
  through@2.3.8:
    through: private
  tiny-invariant@1.3.3:
    tiny-invariant: private
  tinyglobby@0.2.14:
    tinyglobby: private
  tmp@0.0.33:
    tmp: private
  tmpl@1.0.5:
    tmpl: private
  to-buffer@1.2.1:
    to-buffer: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toidentifier@1.0.1:
    toidentifier: private
  token-types@6.0.4:
    token-types: private
  tr46@0.0.3:
    tr46: private
  tree-kill@1.2.2:
    tree-kill: private
  ts-api-utils@2.1.0(typescript@5.8.3):
    ts-api-utils: private
  ts-jest@29.4.1(@babel/core@7.28.0)(@jest/transform@29.7.0)(@jest/types@29.6.3)(babel-jest@29.7.0(@babel/core@7.28.0))(jest-util@29.7.0)(jest@29.7.0(@types/node@22.17.0)(babel-plugin-macros@3.1.0)(ts-node@10.9.2(@types/node@22.17.0)(typescript@5.8.3)))(typescript@5.8.3):
    ts-jest: private
  ts-loader@9.5.2(typescript@5.8.3)(webpack@5.97.1):
    ts-loader: private
  ts-node@10.9.2(@types/node@22.17.0)(typescript@5.8.3):
    ts-node: private
  tsconfig-paths-webpack-plugin@4.2.0:
    tsconfig-paths-webpack-plugin: private
  tsconfig-paths@4.2.0:
    tsconfig-paths: private
  tslib@2.8.1:
    tslib: private
  type-check@0.4.0:
    type-check: private
  type-detect@4.0.8:
    type-detect: private
  type-fest@4.41.0:
    type-fest: private
  type-is@1.6.18:
    type-is: private
  typed-array-buffer@1.0.3:
    typed-array-buffer: private
  typedarray@0.0.6:
    typedarray: private
  typeorm@0.3.25(babel-plugin-macros@3.1.0)(pg@8.16.3)(redis@4.7.1)(reflect-metadata@0.2.2)(ts-node@10.9.2(@types/node@22.17.0)(typescript@5.8.3)):
    typeorm: private
  uc.micro@2.1.0:
    uc.micro: private
  uglify-js@3.19.3:
    uglify-js: private
  uid@2.0.2:
    uid: private
  uint8array-extras@1.4.0:
    uint8array-extras: private
  undici-types@6.21.0:
    undici-types: private
  undici@7.13.0:
    undici: private
  unicorn-magic@0.3.0:
    unicorn-magic: private
  universalify@2.0.1:
    universalify: private
  unpipe@1.0.0:
    unpipe: private
  upath@2.0.1:
    upath: private
  update-browserslist-db@1.1.3(browserslist@4.25.1):
    update-browserslist-db: private
  uri-js@4.4.1:
    uri-js: private
  util-deprecate@1.0.2:
    util-deprecate: private
  utils-merge@1.0.1:
    utils-merge: private
  uuid@11.1.0:
    uuid: private
  v8-compile-cache-lib@3.0.1:
    v8-compile-cache-lib: private
  v8-to-istanbul@9.3.0:
    v8-to-istanbul: private
  validator@13.15.15:
    validator: private
  vary@1.1.2:
    vary: private
  victory-vendor@36.9.2:
    victory-vendor: private
  vite@6.3.5(@types/node@24.2.0)(terser@5.43.1):
    vite: private
  vue-router@4.5.1(vue@3.5.18(typescript@5.9.2)):
    vue-router: private
  vue@3.5.18(typescript@5.9.2):
    vue: private
  vuepress@2.0.0-rc.24(typescript@5.9.2)(vue@3.5.18(typescript@5.9.2)):
    vuepress: private
  walker@1.0.8:
    walker: private
  warning@4.0.3:
    warning: private
  watchpack@2.4.4:
    watchpack: private
  wcwidth@1.0.1:
    wcwidth: private
  webidl-conversions@3.0.1:
    webidl-conversions: private
  webpack-node-externals@3.0.0:
    webpack-node-externals: private
  webpack-sources@3.3.3:
    webpack-sources: private
  webpack@5.97.1:
    webpack: private
  whatwg-encoding@3.1.1:
    whatwg-encoding: private
  whatwg-mimetype@4.0.0:
    whatwg-mimetype: private
  whatwg-url@5.0.0:
    whatwg-url: private
  which-typed-array@1.1.19:
    which-typed-array: private
  which@2.0.2:
    which: private
  word-wrap@1.2.5:
    word-wrap: private
  wordwrap@1.0.0:
    wordwrap: private
  wrap-ansi@6.2.0:
    wrap-ansi: private
  wrap-ansi@7.0.0:
    wrap-ansi-cjs: private
  wrappy@1.0.2:
    wrappy: private
  write-file-atomic@4.0.2:
    write-file-atomic: private
  xmlbuilder2@3.1.1:
    xmlbuilder2: private
  xtend@4.0.2:
    xtend: private
  y18n@5.0.8:
    y18n: private
  yallist@4.0.0:
    yallist: private
  yaml@1.10.2:
    yaml: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yargs@17.7.2:
    yargs: private
  yn@3.1.1:
    yn: private
  yocto-queue@0.1.0:
    yocto-queue: private
ignoredBuilds:
  - '@nestjs/core'
  - '@scarf/scarf'
  - esbuild
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.14.0
pendingBuilds: []
prunedAt: Mon, 04 Aug 2025 15:38:29 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - /@esbuild/aix-ppc64/0.25.8
  - /@esbuild/android-arm/0.25.8
  - /@esbuild/android-arm64/0.25.8
  - /@esbuild/android-x64/0.25.8
  - /@esbuild/darwin-x64/0.25.8
  - /@esbuild/freebsd-arm64/0.25.8
  - /@esbuild/freebsd-x64/0.25.8
  - /@esbuild/linux-arm/0.25.8
  - /@esbuild/linux-arm64/0.25.8
  - /@esbuild/linux-ia32/0.25.8
  - /@esbuild/linux-loong64/0.25.8
  - /@esbuild/linux-mips64el/0.25.8
  - /@esbuild/linux-ppc64/0.25.8
  - /@esbuild/linux-riscv64/0.25.8
  - /@esbuild/linux-s390x/0.25.8
  - /@esbuild/linux-x64/0.25.8
  - /@esbuild/netbsd-arm64/0.25.8
  - /@esbuild/netbsd-x64/0.25.8
  - /@esbuild/openbsd-arm64/0.25.8
  - /@esbuild/openbsd-x64/0.25.8
  - /@esbuild/openharmony-arm64/0.25.8
  - /@esbuild/sunos-x64/0.25.8
  - /@esbuild/win32-arm64/0.25.8
  - /@esbuild/win32-ia32/0.25.8
  - /@esbuild/win32-x64/0.25.8
  - /@rollup/rollup-android-arm-eabi/4.46.2
  - /@rollup/rollup-android-arm64/4.46.2
  - /@rollup/rollup-darwin-x64/4.46.2
  - /@rollup/rollup-freebsd-arm64/4.46.2
  - /@rollup/rollup-freebsd-x64/4.46.2
  - /@rollup/rollup-linux-arm-gnueabihf/4.46.2
  - /@rollup/rollup-linux-arm-musleabihf/4.46.2
  - /@rollup/rollup-linux-arm64-gnu/4.46.2
  - /@rollup/rollup-linux-arm64-musl/4.46.2
  - /@rollup/rollup-linux-loongarch64-gnu/4.46.2
  - /@rollup/rollup-linux-ppc64-gnu/4.46.2
  - /@rollup/rollup-linux-riscv64-gnu/4.46.2
  - /@rollup/rollup-linux-riscv64-musl/4.46.2
  - /@rollup/rollup-linux-s390x-gnu/4.46.2
  - /@rollup/rollup-linux-x64-gnu/4.46.2
  - /@rollup/rollup-linux-x64-musl/4.46.2
  - /@rollup/rollup-win32-arm64-msvc/4.46.2
  - /@rollup/rollup-win32-ia32-msvc/4.46.2
  - /@rollup/rollup-win32-x64-msvc/4.46.2
  - '@esbuild/aix-ppc64@0.25.8'
  - '@esbuild/android-arm64@0.25.8'
  - '@esbuild/android-arm@0.25.8'
  - '@esbuild/android-x64@0.25.8'
  - '@esbuild/darwin-x64@0.25.8'
  - '@esbuild/freebsd-arm64@0.25.8'
  - '@esbuild/freebsd-x64@0.25.8'
  - '@esbuild/linux-arm64@0.25.8'
  - '@esbuild/linux-arm@0.25.8'
  - '@esbuild/linux-ia32@0.25.8'
  - '@esbuild/linux-loong64@0.25.8'
  - '@esbuild/linux-mips64el@0.25.8'
  - '@esbuild/linux-ppc64@0.25.8'
  - '@esbuild/linux-riscv64@0.25.8'
  - '@esbuild/linux-s390x@0.25.8'
  - '@esbuild/linux-x64@0.25.8'
  - '@esbuild/netbsd-arm64@0.25.8'
  - '@esbuild/netbsd-x64@0.25.8'
  - '@esbuild/openbsd-arm64@0.25.8'
  - '@esbuild/openbsd-x64@0.25.8'
  - '@esbuild/openharmony-arm64@0.25.8'
  - '@esbuild/sunos-x64@0.25.8'
  - '@esbuild/win32-arm64@0.25.8'
  - '@esbuild/win32-ia32@0.25.8'
  - '@esbuild/win32-x64@0.25.8'
  - '@rollup/rollup-android-arm-eabi@4.46.2'
  - '@rollup/rollup-android-arm64@4.46.2'
  - '@rollup/rollup-darwin-x64@4.46.2'
  - '@rollup/rollup-freebsd-arm64@4.46.2'
  - '@rollup/rollup-freebsd-x64@4.46.2'
  - '@rollup/rollup-linux-arm-gnueabihf@4.46.2'
  - '@rollup/rollup-linux-arm-musleabihf@4.46.2'
  - '@rollup/rollup-linux-arm64-gnu@4.46.2'
  - '@rollup/rollup-linux-arm64-musl@4.46.2'
  - '@rollup/rollup-linux-loongarch64-gnu@4.46.2'
  - '@rollup/rollup-linux-ppc64-gnu@4.46.2'
  - '@rollup/rollup-linux-riscv64-gnu@4.46.2'
  - '@rollup/rollup-linux-riscv64-musl@4.46.2'
  - '@rollup/rollup-linux-s390x-gnu@4.46.2'
  - '@rollup/rollup-linux-x64-gnu@4.46.2'
  - '@rollup/rollup-linux-x64-musl@4.46.2'
  - '@rollup/rollup-win32-arm64-msvc@4.46.2'
  - '@rollup/rollup-win32-ia32-msvc@4.46.2'
  - '@rollup/rollup-win32-x64-msvc@4.46.2'
storeDir: /Volumes/acasis/.pnpm-store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
