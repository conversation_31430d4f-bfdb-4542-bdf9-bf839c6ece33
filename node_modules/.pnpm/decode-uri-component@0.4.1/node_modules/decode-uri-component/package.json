{"name": "decode-uri-component", "version": "0.4.1", "description": "A better decodeURIComponent", "license": "MIT", "repository": "SamVerschueren/decode-uri-component", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "github.com/SamVerschueren"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "engines": {"node": ">=14.16"}, "scripts": {"test": "xo && nyc ava && tsd", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "files": ["index.js", "index.d.ts"], "keywords": ["decode", "uri", "component", "decodeuricomponent", "components", "decoder", "url"], "devDependencies": {"ava": "^5.1.0", "coveralls": "^3.1.1", "nyc": "^15.1.0", "tsd": "^0.25.0", "xo": "^0.53.1"}, "tsd": {"compilerOptions": {"module": "node16"}}}