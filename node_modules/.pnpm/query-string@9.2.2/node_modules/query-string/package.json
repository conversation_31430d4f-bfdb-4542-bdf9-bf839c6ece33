{"name": "query-string", "version": "9.2.2", "description": "Parse and stringify URL query strings", "license": "MIT", "repository": "sindresorhus/query-string", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"benchmark": "node benchmark.js", "test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts", "base.js", "base.d.ts"], "keywords": ["browser", "querystring", "query", "string", "qs", "param", "parameter", "url", "parse", "stringify", "encode", "decode", "searchparams", "filter"], "dependencies": {"decode-uri-component": "^0.4.1", "filter-obj": "^5.1.0", "split-on-first": "^3.0.0"}, "devDependencies": {"ava": "^6.1.1", "benchmark": "^2.1.4", "deep-equal": "^2.2.3", "fast-check": "^3.15.1", "tsd": "^0.30.7", "xo": "^0.57.0"}, "tsd": {"compilerOptions": {"module": "node16"}}}