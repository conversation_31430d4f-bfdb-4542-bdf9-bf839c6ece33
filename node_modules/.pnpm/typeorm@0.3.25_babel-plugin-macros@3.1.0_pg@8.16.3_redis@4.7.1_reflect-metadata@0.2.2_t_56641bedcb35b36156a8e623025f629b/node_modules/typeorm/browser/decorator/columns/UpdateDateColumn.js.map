{"version": 3, "sources": ["../browser/src/decorator/columns/UpdateDateColumn.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,sBAAsB,EAAE,MAAM,eAAe,CAAA;AAItD;;;GAGG;AACH,MAAM,UAAU,gBAAgB,CAAC,OAAuB;IACpD,OAAO,UAAU,MAAc,EAAE,YAAoB;QACjD,sBAAsB,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;YAClC,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAY;YAC1B,IAAI,EAAE,YAAY;YAClB,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;SACZ,CAAC,CAAA;IAC5B,CAAC,CAAA;AACL,CAAC", "file": "UpdateDateColumn.js", "sourcesContent": ["import { getMetadataArgsStorage } from \"../../globals\"\nimport { ColumnMetadataArgs } from \"../../metadata-args/ColumnMetadataArgs\"\nimport { ColumnOptions } from \"../options/ColumnOptions\"\n\n/**\n * This column will store an update date of the updated object.\n * This date is being updated each time you persist the object.\n */\nexport function UpdateDateColumn(options?: ColumnOptions): PropertyDecorator {\n    return function (object: Object, propertyName: string) {\n        getMetadataArgsStorage().columns.push({\n            target: object.constructor,\n            propertyName: propertyName,\n            mode: \"updateDate\",\n            options: options ? options : {},\n        } as ColumnMetadataArgs)\n    }\n}\n"], "sourceRoot": "../.."}