{"version": 3, "sources": ["../browser/src/decorator/columns/PrimaryColumn.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,sBAAsB,EAAE,MAAM,eAAe,CAAA;AACtD,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAA;AAC/E,OAAO,EAAE,kCAAkC,EAAE,MAAM,gDAAgD,CAAA;AA6BnG;;;;GAIG;AACH,MAAM,UAAU,aAAa,CACzB,aAAiD,EACjD,OAA8B;IAE9B,OAAO,UAAU,MAAc,EAAE,YAAoB;QACjD,uBAAuB;QACvB,IAAI,IAA4B,CAAA;QAChC,IACI,OAAO,aAAa,KAAK,QAAQ;YACjC,aAAa,KAAK,MAAM;YACxB,aAAa,KAAK,OAAO;YACzB,aAAa,KAAK,MAAM,EAC1B,CAAC;YACC,IAAI,GAAG,aAA2B,CAAA;QACtC,CAAC;aAAM,CAAC;YACJ,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAwB,aAAa,CAAC,CAAA;QACpE,CAAC;QACD,IAAI,CAAC,OAAO;YAAE,OAAO,GAAG,EAA0B,CAAA;QAElD,uDAAuD;QACvD,MAAM,mBAAmB,GACrB,OAAO,IAAK,OAAe,CAAC,WAAW;YACnC,CAAC,CAAE,OAAe,CAAC,WAAW,CACxB,aAAa,EACb,MAAM,EACN,YAAY,CACf;YACH,CAAC,CAAC,SAAS,CAAA;QACnB,IAAI,CAAC,IAAI,IAAI,mBAAmB;YAAE,IAAI,GAAG,mBAAmB,CAAA;QAE5D,yGAAyG;QACzG,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,IAAI;YAAE,OAAO,CAAC,IAAI,GAAG,IAAI,CAAA;QAE9C,yFAAyF;QACzF,IAAI,CAAC,OAAO,CAAC,IAAI;YACb,MAAM,IAAI,wBAAwB,CAAC,MAAM,EAAE,YAAY,CAAC,CAAA;QAE5D,wFAAwF;QACxF,IAAI,OAAO,CAAC,QAAQ;YAChB,MAAM,IAAI,kCAAkC,CAAC,MAAM,EAAE,YAAY,CAAC,CAAA;QAEtE,6CAA6C;QAC7C,OAAO,CAAC,OAAO,GAAG,IAAI,CAAA;QAEtB,4CAA4C;QAC5C,sBAAsB,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;YAClC,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAY;YAC1B,IAAI,EAAE,SAAS;YACf,OAAO,EAAE,OAAO;SACG,CAAC,CAAA;QAExB,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACpB,sBAAsB,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC;gBACtC,MAAM,EAAE,MAAM,CAAC,WAAW;gBAC1B,YAAY,EAAE,YAAY;gBAC1B,QAAQ,EACJ,OAAO,OAAO,CAAC,SAAS,KAAK,QAAQ;oBACjC,CAAC,CAAC,OAAO,CAAC,SAAS;oBACnB,CAAC,CAAC,WAAW;aACC,CAAC,CAAA;QAC/B,CAAC;IACL,CAAC,CAAA;AACL,CAAC", "file": "PrimaryColumn.js", "sourcesContent": ["import { getMetadataArgsStorage } from \"../../globals\"\nimport { ColumnTypeUndefinedError } from \"../../error/ColumnTypeUndefinedError\"\nimport { PrimaryColumnCannotBeNullableError } from \"../../error/PrimaryColumnCannotBeNullableError\"\nimport { ColumnMetadataArgs } from \"../../metadata-args/ColumnMetadataArgs\"\nimport { GeneratedMetadataArgs } from \"../../metadata-args/GeneratedMetadataArgs\"\nimport { ColumnOptions } from \"../options/ColumnOptions\"\nimport { ColumnType } from \"../../driver/types/ColumnTypes\"\n\n/**\n * Describes all primary key column's options.\n * If specified, the nullable field must be set to false.\n */\nexport type PrimaryColumnOptions = ColumnOptions & { nullable?: false }\n\n/**\n * Column decorator is used to mark a specific class property as a table column.\n * Only properties decorated with this decorator will be persisted to the database when entity be saved.\n * Primary columns also creates a PRIMARY KEY for this column in a db.\n */\nexport function PrimaryColumn(options?: PrimaryColumnOptions): PropertyDecorator\n\n/**\n * Column decorator is used to mark a specific class property as a table column.\n * Only properties decorated with this decorator will be persisted to the database when entity be saved.\n * Primary columns also creates a PRIMARY KEY for this column in a db.\n */\nexport function PrimaryColumn(\n    type?: ColumnType,\n    options?: PrimaryColumnOptions,\n): PropertyDecorator\n\n/**\n * Column decorator is used to mark a specific class property as a table column.\n * Only properties decorated with this decorator will be persisted to the database when entity be saved.\n * Primary columns also creates a PRIMARY KEY for this column in a db.\n */\nexport function PrimaryColumn(\n    typeOrOptions?: ColumnType | PrimaryColumnOptions,\n    options?: PrimaryColumnOptions,\n): PropertyDecorator {\n    return function (object: Object, propertyName: string) {\n        // normalize parameters\n        let type: ColumnType | undefined\n        if (\n            typeof typeOrOptions === \"string\" ||\n            typeOrOptions === String ||\n            typeOrOptions === Boolean ||\n            typeOrOptions === Number\n        ) {\n            type = typeOrOptions as ColumnType\n        } else {\n            options = Object.assign({}, <PrimaryColumnOptions>typeOrOptions)\n        }\n        if (!options) options = {} as PrimaryColumnOptions\n\n        // if type is not given explicitly then try to guess it\n        const reflectMetadataType =\n            Reflect && (Reflect as any).getMetadata\n                ? (Reflect as any).getMetadata(\n                      \"design:type\",\n                      object,\n                      propertyName,\n                  )\n                : undefined\n        if (!type && reflectMetadataType) type = reflectMetadataType\n\n        // check if there is no type in column options then set type from first function argument, or guessed one\n        if (!options.type && type) options.type = type\n\n        // if we still don't have a type then we need to give error to user that type is required\n        if (!options.type)\n            throw new ColumnTypeUndefinedError(object, propertyName)\n\n        // check if column is not nullable, because we cannot allow a primary key to be nullable\n        if (options.nullable)\n            throw new PrimaryColumnCannotBeNullableError(object, propertyName)\n\n        // explicitly set a primary to column options\n        options.primary = true\n\n        // create and register a new column metadata\n        getMetadataArgsStorage().columns.push({\n            target: object.constructor,\n            propertyName: propertyName,\n            mode: \"regular\",\n            options: options,\n        } as ColumnMetadataArgs)\n\n        if (options.generated) {\n            getMetadataArgsStorage().generations.push({\n                target: object.constructor,\n                propertyName: propertyName,\n                strategy:\n                    typeof options.generated === \"string\"\n                        ? options.generated\n                        : \"increment\",\n            } as GeneratedMetadataArgs)\n        }\n    }\n}\n"], "sourceRoot": "../.."}