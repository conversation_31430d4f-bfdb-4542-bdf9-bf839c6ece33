{"version": 3, "sources": ["../browser/src/decorator/columns/CreateDateColumn.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,sBAAsB,EAAE,MAAM,eAAe,CAAA;AAItD;;;;GAIG;AACH,MAAM,UAAU,gBAAgB,CAAC,OAAuB;IACpD,OAAO,UAAU,MAAc,EAAE,YAAoB;QACjD,sBAAsB,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;YAClC,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAY;YAC1B,IAAI,EAAE,YAAY;YAClB,OAAO,EAAE,OAAO,IAAI,EAAE;SACH,CAAC,CAAA;IAC5B,CAAC,CAAA;AACL,CAAC", "file": "CreateDateColumn.js", "sourcesContent": ["import { getMetadataArgsStorage } from \"../../globals\"\nimport { ColumnMetadataArgs } from \"../../metadata-args/ColumnMetadataArgs\"\nimport { ColumnOptions } from \"../options/ColumnOptions\"\n\n/**\n * This column will store a creation date of the inserted object.\n * Creation date is generated and inserted only once,\n * at the first time when you create an object, the value is inserted into the table, and is never touched again.\n */\nexport function CreateDateColumn(options?: ColumnOptions): PropertyDecorator {\n    return function (object: Object, propertyName: string) {\n        getMetadataArgsStorage().columns.push({\n            target: object.constructor,\n            propertyName: propertyName,\n            mode: \"createDate\",\n            options: options || {},\n        } as ColumnMetadataArgs)\n    }\n}\n"], "sourceRoot": "../.."}