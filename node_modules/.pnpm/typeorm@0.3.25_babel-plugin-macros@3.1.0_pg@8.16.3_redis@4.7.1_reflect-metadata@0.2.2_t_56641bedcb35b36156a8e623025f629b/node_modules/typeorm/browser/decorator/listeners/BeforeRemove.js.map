{"version": 3, "sources": ["../browser/src/decorator/listeners/BeforeRemove.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,sBAAsB,EAAE,MAAM,eAAe,CAAA;AACtD,OAAO,EAAE,kBAAkB,EAAE,MAAM,yCAAyC,CAAA;AAG5E;;GAEG;AACH,MAAM,UAAU,YAAY;IACxB,OAAO,UAAU,MAAc,EAAE,YAAoB;QACjD,sBAAsB,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC;YAC1C,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAY;YAC1B,IAAI,EAAE,kBAAkB,CAAC,aAAa;SACX,CAAC,CAAA;IACpC,CAAC,CAAA;AACL,CAAC", "file": "BeforeRemove.js", "sourcesContent": ["import { getMetadataArgsStorage } from \"../../globals\"\nimport { EventListenerTypes } from \"../../metadata/types/EventListenerTypes\"\nimport { EntityListenerMetadataArgs } from \"../../metadata-args/EntityListenerMetadataArgs\"\n\n/**\n * Calls a method on which this decorator is applied before this entity removal.\n */\nexport function BeforeRemove(): PropertyDecorator {\n    return function (object: Object, propertyName: string) {\n        getMetadataArgsStorage().entityListeners.push({\n            target: object.constructor,\n            propertyName: propertyName,\n            type: EventListenerTypes.BEFORE_REMOVE,\n        } as EntityListenerMetadataArgs)\n    }\n}\n"], "sourceRoot": "../.."}