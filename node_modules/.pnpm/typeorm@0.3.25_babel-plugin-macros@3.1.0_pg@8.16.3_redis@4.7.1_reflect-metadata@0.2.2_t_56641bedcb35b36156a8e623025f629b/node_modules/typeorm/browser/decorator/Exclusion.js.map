{"version": 3, "sources": ["../browser/src/decorator/Exclusion.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,sBAAsB,EAAE,MAAM,YAAY,CAAA;AAEnD,OAAO,EAAE,YAAY,EAAE,MAAM,UAAU,CAAA;AAqBvC;;;;GAIG;AACH,MAAM,UAAU,SAAS,CACrB,gBAAwB,EACxB,eAAwB;IAExB,MAAM,IAAI,GAAG,eAAe,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,SAAS,CAAA;IAC3D,MAAM,UAAU,GAAG,eAAe,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,gBAAgB,CAAA;IAEvE,IAAI,CAAC,UAAU;QAAE,MAAM,IAAI,YAAY,CAAC,kCAAkC,CAAC,CAAA;IAE3E,OAAO,UACH,WAA8B,EAC9B,YAA8B;QAE9B,sBAAsB,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC;YACrC,MAAM,EAAE,YAAY;gBAChB,CAAC,CAAC,WAAW,CAAC,WAAW;gBACzB,CAAC,CAAE,WAAwB;YAC/B,IAAI,EAAE,IAAI;YACV,UAAU,EAAE,UAAU;SACA,CAAC,CAAA;IAC/B,CAAC,CAAA;AACL,CAAC", "file": "Exclusion.js", "sourcesContent": ["import { getMetadataArgsStorage } from \"../globals\"\nimport { ExclusionMetadataArgs } from \"../metadata-args/ExclusionMetadataArgs\"\nimport { TypeORMError } from \"../error\"\n\n/**\n * Creates a database exclusion.\n * Can be used on entity.\n * Can create exclusions with composite columns when used on entity.\n */\nexport function Exclusion(\n    expression: string,\n): ClassDecorator & PropertyDecorator\n\n/**\n * Creates a database exclusion.\n * Can be used on entity.\n * Can create exclusions with composite columns when used on entity.\n */\nexport function Exclusion(\n    name: string,\n    expression: string,\n): ClassDecorator & PropertyDecorator\n\n/**\n * Creates a database exclusion.\n * Can be used on entity.\n * Can create exclusions with composite columns when used on entity.\n */\nexport function Exclusion(\n    nameOrExpression: string,\n    maybeExpression?: string,\n): ClassDecorator & PropertyDecorator {\n    const name = maybeExpression ? nameOrExpression : undefined\n    const expression = maybeExpression ? maybeExpression : nameOrExpression\n\n    if (!expression) throw new TypeORMError(`Exclusion expression is required`)\n\n    return function (\n        clsOrObject: Function | Object,\n        propertyName?: string | symbol,\n    ) {\n        getMetadataArgsStorage().exclusions.push({\n            target: propertyName\n                ? clsOrObject.constructor\n                : (clsOrObject as Function),\n            name: name,\n            expression: expression,\n        } as ExclusionMetadataArgs)\n    }\n}\n"], "sourceRoot": ".."}