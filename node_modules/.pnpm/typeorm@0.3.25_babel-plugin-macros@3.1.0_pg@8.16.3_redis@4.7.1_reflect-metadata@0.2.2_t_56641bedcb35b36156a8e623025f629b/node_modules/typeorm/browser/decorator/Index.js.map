{"version": 3, "sources": ["../browser/src/decorator/Index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,sBAAsB,EAAE,MAAM,YAAY,CAAA;AAGnD,OAAO,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAA;AAyEjD;;;;GAIG;AACH,MAAM,UAAU,KAAK,CACjB,qBAIkB,EAClB,oBAI4B,EAC5B,YAA2B;IAE3B,uBAAuB;IACvB,MAAM,IAAI,GACN,OAAO,qBAAqB,KAAK,QAAQ;QACrC,CAAC,CAAC,qBAAqB;QACvB,CAAC,CAAC,SAAS,CAAA;IACnB,MAAM,MAAM,GACR,OAAO,qBAAqB,KAAK,QAAQ;QACrC,CAAC,CAGE,oBAAoB;QACvB,CAAC,CAAE,qBAAkC,CAAA;IAC7C,IAAI,OAAO,GACP,WAAW,CAAC,QAAQ,CAAC,qBAAqB,CAAC;QAC3C,CAAC,KAAK,CAAC,OAAO,CAAC,qBAAqB,CAAC;QACjC,CAAC,CAAE,qBAAsC;QACzC,CAAC,CAAC,YAAY,CAAA;IACtB,IAAI,CAAC,OAAO;QACR,OAAO;YACH,WAAW,CAAC,QAAQ,CAAC,oBAAoB,CAAC;gBAC1C,CAAC,KAAK,CAAC,OAAO,CAAC,oBAAoB,CAAC;gBAChC,CAAC,CAAE,oBAAqC;gBACxC,CAAC,CAAC,YAAY,CAAA;IAE1B,OAAO,UACH,WAA8B,EAC9B,YAA8B;QAE9B,sBAAsB,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;YAClC,MAAM,EAAE,YAAY;gBAChB,CAAC,CAAC,WAAW,CAAC,WAAW;gBACzB,CAAC,CAAE,WAAwB;YAC/B,IAAI,EAAE,IAAI;YACV,OAAO,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,MAAM;YAC/C,WAAW,EACP,OAAO;gBACN,OAAkC,CAAC,WAAW,KAAK,KAAK;gBACrD,CAAC,CAAC,KAAK;gBACP,CAAC,CAAC,IAAI;YACd,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS;YAC1C,MAAM,EAAE,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK;YAChD,OAAO,EAAE,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK;YAClD,QAAQ,EAAE,OAAO,IAAI,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK;YACpD,YAAY,EAAE,OAAO,IAAI,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK;YAC5D,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;YAC5C,MAAM,EAAE,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK;YAChD,UAAU,EAAE,OAAO,IAAI,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK;YACxD,UAAU,EAAE,OAAO,IAAI,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK;YACxD,kBAAkB,EAAE,OAAO;gBACvB,CAAC,CAAC,OAAO,CAAC,kBAAkB;gBAC5B,CAAC,CAAC,SAAS;SACG,CAAC,CAAA;IAC3B,CAAC,CAAA;AACL,CAAC", "file": "Index.js", "sourcesContent": ["import { getMetadataArgsStorage } from \"../globals\"\nimport { IndexMetadataArgs } from \"../metadata-args/IndexMetadataArgs\"\nimport { IndexOptions } from \"./options/IndexOptions\"\nimport { ObjectUtils } from \"../util/ObjectUtils\"\n\n/**\n * Creates a database index.\n * Can be used on entity property or on entity.\n * Can create indices with composite columns when used on entity.\n */\nexport function Index(\n    options?: IndexOptions,\n): ClassDecorator & PropertyDecorator\n\n/**\n * Creates a database index.\n * Can be used on entity property or on entity.\n * Can create indices with composite columns when used on entity.\n */\nexport function Index(\n    name: string,\n    options?: IndexOptions,\n): ClassDecorator & PropertyDecorator\n\n/**\n * Creates a database index.\n * Can be used on entity property or on entity.\n * Can create indices with composite columns when used on entity.\n */\nexport function Index(\n    name: string,\n    options: { synchronize: false },\n): ClassDecorator & PropertyDecorator\n\n/**\n * Creates a database index.\n * Can be used on entity property or on entity.\n * Can create indices with composite columns when used on entity.\n */\nexport function Index(\n    name: string,\n    fields: string[],\n    options?: IndexOptions,\n): ClassDecorator & PropertyDecorator\n\n/**\n * Creates a database index.\n * Can be used on entity property or on entity.\n * Can create indices with composite columns when used on entity.\n */\nexport function Index(\n    fields: string[],\n    options?: IndexOptions,\n): ClassDecorator & PropertyDecorator\n\n/**\n * Creates a database index.\n * Can be used on entity property or on entity.\n * Can create indices with composite columns when used on entity.\n */\nexport function Index(\n    fields: (object?: any) => any[] | { [key: string]: number },\n    options?: IndexOptions,\n): ClassDecorator & PropertyDecorator\n\n/**\n * Creates a database index.\n * Can be used on entity property or on entity.\n * Can create indices with composite columns when used on entity.\n */\nexport function Index(\n    name: string,\n    fields: (object?: any) => any[] | { [key: string]: number },\n    options?: IndexOptions,\n): ClassDecorator & PropertyDecorator\n\n/**\n * Creates a database index.\n * Can be used on entity property or on entity.\n * Can create indices with composite columns when used on entity.\n */\nexport function Index(\n    nameOrFieldsOrOptions?:\n        | string\n        | string[]\n        | ((object: any) => any[] | { [key: string]: number })\n        | IndexOptions,\n    maybeFieldsOrOptions?:\n        | ((object?: any) => any[] | { [key: string]: number })\n        | IndexOptions\n        | string[]\n        | { synchronize: false },\n    maybeOptions?: IndexOptions,\n): ClassDecorator & PropertyDecorator {\n    // normalize parameters\n    const name =\n        typeof nameOrFieldsOrOptions === \"string\"\n            ? nameOrFieldsOrOptions\n            : undefined\n    const fields =\n        typeof nameOrFieldsOrOptions === \"string\"\n            ? <\n                  | ((object?: any) => any[] | { [key: string]: number })\n                  | string[]\n              >maybeFieldsOrOptions\n            : (nameOrFieldsOrOptions as string[])\n    let options =\n        ObjectUtils.isObject(nameOrFieldsOrOptions) &&\n        !Array.isArray(nameOrFieldsOrOptions)\n            ? (nameOrFieldsOrOptions as IndexOptions)\n            : maybeOptions\n    if (!options)\n        options =\n            ObjectUtils.isObject(maybeFieldsOrOptions) &&\n            !Array.isArray(maybeFieldsOrOptions)\n                ? (maybeFieldsOrOptions as IndexOptions)\n                : maybeOptions\n\n    return function (\n        clsOrObject: Function | Object,\n        propertyName?: string | symbol,\n    ) {\n        getMetadataArgsStorage().indices.push({\n            target: propertyName\n                ? clsOrObject.constructor\n                : (clsOrObject as Function),\n            name: name,\n            columns: propertyName ? [propertyName] : fields,\n            synchronize:\n                options &&\n                (options as { synchronize: false }).synchronize === false\n                    ? false\n                    : true,\n            where: options ? options.where : undefined,\n            unique: options && options.unique ? true : false,\n            spatial: options && options.spatial ? true : false,\n            fulltext: options && options.fulltext ? true : false,\n            nullFiltered: options && options.nullFiltered ? true : false,\n            parser: options ? options.parser : undefined,\n            sparse: options && options.sparse ? true : false,\n            background: options && options.background ? true : false,\n            concurrent: options && options.concurrent ? true : false,\n            expireAfterSeconds: options\n                ? options.expireAfterSeconds\n                : undefined,\n        } as IndexMetadataArgs)\n    }\n}\n"], "sourceRoot": ".."}