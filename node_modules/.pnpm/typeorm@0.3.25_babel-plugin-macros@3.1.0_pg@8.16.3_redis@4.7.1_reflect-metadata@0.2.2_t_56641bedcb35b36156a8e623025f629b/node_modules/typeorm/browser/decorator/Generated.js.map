{"version": 3, "sources": ["../browser/src/decorator/Generated.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,sBAAsB,EAAE,MAAM,YAAY,CAAA;AAGnD;;;;;;;;GAQG;AACH,MAAM,UAAU,SAAS,CACrB,WAA2C,WAAW;IAEtD,OAAO,UAAU,MAAc,EAAE,YAAoB;QACjD,sBAAsB,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC;YACtC,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAY;YAC1B,QAAQ,EAAE,QAAQ;SACI,CAAC,CAAA;IAC/B,CAAC,CAAA;AACL,CAAC", "file": "Generated.js", "sourcesContent": ["import { getMetadataArgsStorage } from \"../globals\"\nimport { GeneratedMetadataArgs } from \"../metadata-args/GeneratedMetadataArgs\"\n\n/**\n * Marks a column to generate a value on entity insertion.\n * There are three types of generation strategy - increment, uuid and rowid (cockroachdb only).\n * Increment uses a number which increases by one on each insertion.\n * Uuid generates a special UUID token.\n * Rowid supports only in CockroachDB and uses `unique_rowid()` function\n *\n * Note, some databases do not support non-primary generation columns.\n */\nexport function Generated(\n    strategy: \"increment\" | \"uuid\" | \"rowid\" = \"increment\",\n): PropertyDecorator {\n    return function (object: Object, propertyName: string) {\n        getMetadataArgsStorage().generations.push({\n            target: object.constructor,\n            propertyName: propertyName,\n            strategy: strategy,\n        } as GeneratedMetadataArgs)\n    }\n}\n"], "sourceRoot": ".."}