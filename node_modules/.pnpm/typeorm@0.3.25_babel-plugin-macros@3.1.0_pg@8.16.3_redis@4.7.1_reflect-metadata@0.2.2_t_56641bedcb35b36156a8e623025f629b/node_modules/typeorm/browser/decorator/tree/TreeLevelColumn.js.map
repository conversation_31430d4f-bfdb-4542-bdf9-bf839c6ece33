{"version": 3, "sources": ["../browser/src/decorator/tree/TreeLevelColumn.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,sBAAsB,EAAE,MAAM,eAAe,CAAA;AAGtD;;GAEG;AACH,MAAM,UAAU,eAAe;IAC3B,OAAO,UAAU,MAAc,EAAE,YAAoB;QACjD,sBAAsB,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;YAClC,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAY;YAC1B,IAAI,EAAE,WAAW;YACjB,OAAO,EAAE,EAAE;SACQ,CAAC,CAAA;IAC5B,CAAC,CAAA;AACL,CAAC", "file": "TreeLevelColumn.js", "sourcesContent": ["import { getMetadataArgsStorage } from \"../../globals\"\nimport { ColumnMetadataArgs } from \"../../metadata-args/ColumnMetadataArgs\"\n\n/**\n * Creates a \"level\"/\"length\" column to the table that holds a closure table.\n */\nexport function TreeLevelColumn(): PropertyDecorator {\n    return function (object: Object, propertyName: string) {\n        getMetadataArgsStorage().columns.push({\n            target: object.constructor,\n            propertyName: propertyName,\n            mode: \"treeLevel\",\n            options: {},\n        } as ColumnMetadataArgs)\n    }\n}\n"], "sourceRoot": "../.."}