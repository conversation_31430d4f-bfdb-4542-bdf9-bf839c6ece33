{"version": 3, "sources": ["../browser/src/decorator/options/ColumnWithWidthOptions.ts"], "names": [], "mappings": "", "file": "ColumnWithWidthOptions.js", "sourcesContent": ["/**\n * Options for columns that can define a length of the column type.\n */\nexport interface ColumnWithWidthOptions {\n    /**\n     * Column type's display width. Used only on some column types in MySQL.\n     * For example, INT(4) specifies an INT with a display width of four digits.\n     */\n    width?: number\n\n    /**\n     * Puts ZEROFILL attribute on to numeric column. Works only for MySQL.\n     * If you specify ZEROFILL for a numeric column, MySQL automatically adds the UNSIGNED attribute to this column\n     */\n    zerofill?: boolean\n\n    /**\n     * Puts UNSIGNED attribute on to numeric column. Works only for MySQL.\n     */\n    unsigned?: boolean\n}\n"], "sourceRoot": "../.."}