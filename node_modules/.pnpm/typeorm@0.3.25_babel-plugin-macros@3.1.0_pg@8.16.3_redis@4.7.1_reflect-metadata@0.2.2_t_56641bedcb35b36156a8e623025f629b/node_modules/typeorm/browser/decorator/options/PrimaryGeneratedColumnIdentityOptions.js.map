{"version": 3, "sources": ["../browser/src/decorator/options/PrimaryGeneratedColumnIdentityOptions.ts"], "names": [], "mappings": "", "file": "PrimaryGeneratedColumnIdentityOptions.js", "sourcesContent": ["import { PrimaryGeneratedColumnType } from \"../../driver/types/ColumnTypes\"\n\n/**\n * Describes all options for PrimaryGeneratedColumn decorator with identity generation strategy.\n */\nexport interface PrimaryGeneratedColumnIdentityOptions {\n    /**\n     * Column type. Must be one of the value from the ColumnTypes class.\n     */\n    type?: PrimaryGeneratedColumnType\n\n    /**\n     * Column name in the database.\n     */\n    name?: string\n\n    /**\n     * Column comment. Not supported by all database types.\n     */\n    comment?: string\n\n    /**\n     * Identity column type. Supports only in Postgres 10+.\n     */\n    generatedIdentity?: \"ALWAYS\" | \"BY DEFAULT\"\n\n    /**\n     * Name of the primary key constraint.\n     */\n    primaryKeyConstraintName?: string\n}\n"], "sourceRoot": "../.."}