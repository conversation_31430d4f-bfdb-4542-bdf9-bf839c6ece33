{"version": 3, "sources": ["../browser/src/decorator/options/JoinTableMultipleColumnsOptions.ts"], "names": [], "mappings": "", "file": "JoinTableMultipleColumnsOptions.js", "sourcesContent": ["import { JoinColumnOptions } from \"./JoinColumnOptions\"\n\n/**\n * Describes all join table with multiple column options.\n */\nexport interface JoinTableMultipleColumnsOptions {\n    /**\n     * Name of the table that will be created to store values of the both tables (join table).\n     * By default is auto generated.\n     */\n    name?: string\n\n    /**\n     * First column of the join table.\n     */\n    joinColumns?: JoinColumnOptions[]\n\n    /**\n     * Second (inverse) column of the join table.\n     */\n    inverseJoinColumns?: JoinColumnOptions[]\n\n    /**\n     * Database where join table will be created.\n     * Works only in some databases (like mysql and mssql).\n     */\n    database?: string\n\n    /**\n     * Schema where join table will be created.\n     * Works only in some databases (like postgres and mssql).\n     */\n    schema?: string\n\n    /**\n     * Indicates if schema synchronization is enabled or disabled junction table.\n     * If it will be set to false then schema sync will and migrations ignores junction table.\n     * By default schema synchronization is enabled.\n     */\n    readonly synchronize?: boolean\n}\n"], "sourceRoot": "../.."}