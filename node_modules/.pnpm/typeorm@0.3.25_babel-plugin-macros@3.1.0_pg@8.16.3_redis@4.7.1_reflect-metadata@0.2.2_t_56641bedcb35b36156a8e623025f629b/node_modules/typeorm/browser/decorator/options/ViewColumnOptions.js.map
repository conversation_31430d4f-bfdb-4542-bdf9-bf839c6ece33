{"version": 3, "sources": ["../browser/src/decorator/options/ViewColumnOptions.ts"], "names": [], "mappings": "", "file": "ViewColumnOptions.js", "sourcesContent": ["import { ValueTransformer } from \"./ValueTransformer\"\n\n/**\n * Describes all view column's options.\n */\nexport interface ViewColumnOptions {\n    /**\n     * Column name in the database.\n     */\n    name?: string\n\n    /**\n     * Specifies a value transformer(s) that is to be used to unmarshal\n     * this column when reading from the database.\n     */\n    transformer?: ValueTransformer | ValueTransformer[]\n}\n"], "sourceRoot": "../.."}