{"version": 3, "sources": ["../browser/src/decorator/options/ColumnHstoreOptions.ts"], "names": [], "mappings": "", "file": "ColumnHstoreOptions.js", "sourcesContent": ["/**\n * Column options for enum-typed columns.\n */\nexport interface ColumnHstoreOptions {\n    /**\n     * Return type of HSTORE column.\n     * Returns value as string or as object.\n     */\n    hstoreType?: string\n}\n"], "sourceRoot": "../.."}