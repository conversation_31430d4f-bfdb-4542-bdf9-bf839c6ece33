{"version": 3, "sources": ["../browser/src/decorator/options/ColumnNumericOptions.ts"], "names": [], "mappings": "", "file": "ColumnNumericOptions.js", "sourcesContent": ["/**\n * Options for numeric column types where user can specify scale and precision.\n */\nexport interface ColumnNumericOptions {\n    /**\n     * The precision for a decimal (exact numeric) column (applies only for decimal column), which is the maximum\n     * number of digits that are stored for the values.\n     */\n    precision?: number\n\n    /**\n     * The scale for a decimal (exact numeric) column (applies only for decimal column), which represents the number\n     * of digits to the right of the decimal point and must not be greater than precision.\n     */\n    scale?: number\n\n    /**\n     * Puts ZEROFILL attribute on to numeric column. Works only for MySQL.\n     * If you specify ZEROFILL for a numeric column, MySQL automatically adds the UNSIGNED attribute to the column\n     */\n    zerofill?: boolean\n\n    /**\n     * Puts UNSIGNED attribute on to numeric column. Works only for MySQL.\n     */\n    unsigned?: boolean\n}\n"], "sourceRoot": "../.."}