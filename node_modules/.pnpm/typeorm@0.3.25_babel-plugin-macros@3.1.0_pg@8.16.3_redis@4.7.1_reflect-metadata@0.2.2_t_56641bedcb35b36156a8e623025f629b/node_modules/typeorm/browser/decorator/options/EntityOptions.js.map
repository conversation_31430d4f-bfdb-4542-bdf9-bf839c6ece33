{"version": 3, "sources": ["../browser/src/decorator/options/EntityOptions.ts"], "names": [], "mappings": "", "file": "EntityOptions.js", "sourcesContent": ["import { OrderByCondition } from \"../../find-options/OrderByCondition\"\n\n/**\n * Describes all entity's options.\n */\nexport interface EntityOptions {\n    /**\n     * Table name.\n     * If not specified then naming strategy will generate table name from entity name.\n     */\n    name?: string\n\n    /**\n     * Specifies a default order by used for queries from this table when no explicit order by is specified.\n     */\n    orderBy?: OrderByCondition | ((object: any) => OrderByCondition | any)\n\n    /**\n     * Table's database engine type (like \"InnoDB\", \"MyISAM\", etc).\n     * It is used only during table creation.\n     * If you update this value and table is already created, it will not change table's engine type.\n     * Note that not all databases support this option.\n     */\n    engine?: string\n\n    /**\n     * Database name. Used in Mysql and Sql Server.\n     */\n    database?: string\n\n    /**\n     * Schema name. Used in Postgres and Sql Server.\n     */\n    schema?: string\n\n    /**\n     * Indicates if schema synchronization is enabled or disabled for this entity.\n     * If it will be set to false then schema sync will and migrations ignore this entity.\n     * By default schema synchronization is enabled for all entities.\n     */\n    synchronize?: boolean\n\n    /**\n     * If set to 'true' this option disables Sqlite's default behaviour of secretly creating\n     * an integer primary key column named 'rowid' on table creation.\n     * @see https://www.sqlite.org/withoutrowid.html.\n     */\n    withoutRowid?: boolean\n\n    /**\n     * Table comment. Not supported by all database types.\n     */\n    comment?: string\n}\n"], "sourceRoot": "../.."}