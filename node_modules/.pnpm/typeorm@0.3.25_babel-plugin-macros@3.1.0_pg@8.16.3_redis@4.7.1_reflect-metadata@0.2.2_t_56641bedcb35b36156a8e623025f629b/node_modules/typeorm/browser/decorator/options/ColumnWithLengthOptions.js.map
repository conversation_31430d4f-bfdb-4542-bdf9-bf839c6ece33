{"version": 3, "sources": ["../browser/src/decorator/options/ColumnWithLengthOptions.ts"], "names": [], "mappings": "", "file": "ColumnWithLengthOptions.js", "sourcesContent": ["/**\n * Options for columns that can define a length of the column type.\n */\nexport interface ColumnWithLengthOptions {\n    /**\n     * Column type's length.\n     * For example type = \"varchar\" and length = \"100\" means ORM will create a column with type varchar(100).\n     */\n    length?: string | number\n}\n"], "sourceRoot": "../.."}