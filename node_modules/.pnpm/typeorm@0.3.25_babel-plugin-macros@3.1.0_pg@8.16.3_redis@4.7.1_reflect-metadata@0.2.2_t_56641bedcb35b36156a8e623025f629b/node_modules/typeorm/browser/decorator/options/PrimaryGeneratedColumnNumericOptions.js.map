{"version": 3, "sources": ["../browser/src/decorator/options/PrimaryGeneratedColumnNumericOptions.ts"], "names": [], "mappings": "", "file": "PrimaryGeneratedColumnNumericOptions.js", "sourcesContent": ["import { PrimaryGeneratedColumnType } from \"../../driver/types/ColumnTypes\"\n\n/**\n * Describes all options for PrimaryGeneratedColumn decorator with numeric generation strategy.\n */\nexport interface PrimaryGeneratedColumnNumericOptions {\n    /**\n     * Column type. Must be one of the value from the ColumnTypes class.\n     */\n    type?: PrimaryGeneratedColumnType\n\n    /**\n     * Column name in the database.\n     */\n    name?: string\n\n    /**\n     * Column comment. Not supported by all database types.\n     */\n    comment?: string\n\n    /**\n     * Puts ZEROFILL attribute on to numeric column. Works only for MySQL.\n     * If you specify ZEROFILL for a numeric column, MySQL automatically adds the UNSIGNED attribute to the column\n     */\n    zerofill?: boolean\n\n    /**\n     * Puts UNSIGNED attribute on to numeric column. Works only for MySQL.\n     */\n    unsigned?: boolean\n\n    /**\n     * Name of the primary key constraint.\n     */\n    primaryKeyConstraintName?: string\n}\n"], "sourceRoot": "../.."}