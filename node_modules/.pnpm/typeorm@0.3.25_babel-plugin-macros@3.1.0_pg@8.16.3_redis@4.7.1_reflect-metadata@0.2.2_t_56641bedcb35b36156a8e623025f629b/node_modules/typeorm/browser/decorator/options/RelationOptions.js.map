{"version": 3, "sources": ["../browser/src/decorator/options/RelationOptions.ts"], "names": [], "mappings": "", "file": "RelationOptions.js", "sourcesContent": ["import { DeferrableType } from \"../../metadata/types/DeferrableType\"\nimport { OnDeleteType } from \"../../metadata/types/OnDeleteType\"\nimport { OnUpdateType } from \"../../metadata/types/OnUpdateType\"\n\n/**\n * Describes all relation's options.\n */\nexport interface RelationOptions {\n    /**\n     * Sets cascades options for the given relation.\n     * If set to true then it means that related object can be allowed to be inserted or updated in the database.\n     * You can separately restrict cascades to insertion or updation using following syntax:\n     *\n     * cascade: [\"insert\", \"update\", \"remove\", \"soft-remove\", \"recover\"] // include or exclude one of them\n     */\n    cascade?:\n        | boolean\n        | (\"insert\" | \"update\" | \"remove\" | \"soft-remove\" | \"recover\")[]\n\n    /**\n     * Indicates if relation column value can be nullable or not.\n     */\n    nullable?: boolean\n\n    /**\n     * Database cascade action on delete.\n     */\n    onDelete?: OnDeleteType\n\n    /**\n     * Database cascade action on update.\n     */\n    onUpdate?: OnUpdateType\n\n    /**\n     * Indicate if foreign key constraints can be deferred.\n     */\n    deferrable?: DeferrableType\n\n    /**\n     * Indicates whether foreign key constraints will be created for join columns.\n     * Can be used only for many-to-one and owner one-to-one relations.\n     * Defaults to true.\n     */\n    createForeignKeyConstraints?: boolean\n\n    /**\n     * Set this relation to be lazy. Note: lazy relations are promises. When you call them they return promise\n     * which resolve relation result then. If your property's type is Promise then this relation is set to lazy automatically.\n     */\n    lazy?: boolean\n\n    /**\n     * Set this relation to be eager.\n     * Eager relations are always loaded automatically when relation's owner entity is loaded using find* methods.\n     * Only using QueryBuilder prevents loading eager relations.\n     * Eager flag cannot be set from both sides of relation - you can eager load only one side of the relationship.\n     */\n    eager?: boolean\n\n    /**\n     * Indicates if persistence is enabled for the relation.\n     * By default its enabled, but if you want to avoid any changes in the relation to be reflected in the database you can disable it.\n     * If its disabled you can only change a relation from inverse side of a relation or using relation query builder functionality.\n     * This is useful for performance optimization since its disabling avoid multiple extra queries during entity save.\n     */\n    persistence?: boolean\n\n    /**\n     * When a parent is saved (with cascading but) without a child row that still exists in database, this will control what shall happen to them.\n     * delete will remove these rows from database.\n     * nullify will remove the relation key.\n     * disable will keep the relation intact. Removal of related item is only possible through its own repo.\n     */\n    orphanedRowAction?: \"nullify\" | \"delete\" | \"soft-delete\" | \"disable\"\n}\n"], "sourceRoot": "../.."}