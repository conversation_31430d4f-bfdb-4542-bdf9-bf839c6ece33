{"version": 3, "sources": ["../browser/src/entity-manager/MongoEntityManager.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAA;AAM/C,OAAO,EAAE,2BAA2B,EAAE,MAAM,0DAA0D,CAAA;AAEtG,OAAO,EAAE,gBAAgB,EAAE,MAAM,kCAAkC,CAAA;AACnE,OAAO,EAAE,aAAa,EAAE,MAAM,2BAA2B,CAAA;AAEzD,OAAO,EAAE,YAAY,EAAE,MAAM,sCAAsC,CAAA;AACnE,OAAO,EAAE,YAAY,EAAE,MAAM,sCAAsC,CAAA;AACnE,OAAO,EAAE,YAAY,EAAE,MAAM,sCAAsC,CAAA;AAoDnE,OAAO,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAA;AAGjD;;;;;GAKG;AACH,MAAM,OAAO,kBAAmB,SAAQ,aAAa;IAGjD,IAAI,gBAAgB;QAChB,OAAQ,IAAI,CAAC,UAAU,CAAC,MAAsB;aACzC,WAA+B,CAAA;IACxC,CAAC;IAED,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAY,UAAsB;QAC9B,KAAK,CAAC,UAAU,CAAC,CAAA;QAZZ,mBAAa,GAAG,MAAM,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAA;IAazD,CAAC;IAED,4EAA4E;IAC5E,qBAAqB;IACrB,4EAA4E;IAE5E;;OAEG;IACH;;OAEG;IACH,KAAK,CAAC,IAAI,CACN,iBAAuC,EACvC,mBAG6B;QAE7B,MAAM,KAAK,GACP,IAAI,CAAC,gDAAgD,CACjD,mBAAmB,CACtB,CAAA;QACL,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAClC,iBAAiB,EACjB,KAAuB,CAC1B,CAAA;QACD,MAAM,gBAAgB,GAClB,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC,gBAAgB,CAAA;QACnE,IAAI,gBAAgB,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,EAAE,CAAC;YAC1D,IAAI,mBAAmB,CAAC,MAAM;gBAC1B,MAAM,CAAC,OAAO,CACV,IAAI,CAAC,yCAAyC,CAC1C,mBAAmB,CAAC,MAAM,CAC7B,CACJ,CAAA;YACL,IAAI,mBAAmB,CAAC,IAAI;gBAAE,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA;YACnE,IAAI,mBAAmB,CAAC,IAAI;gBAAE,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA;YACpE,IAAI,mBAAmB,CAAC,KAAK;gBACzB,MAAM,CAAC,IAAI,CACP,IAAI,CAAC,sCAAsC,CACvC,mBAAmB,CAAC,KAAK,CAC5B,CACJ,CAAA;YACL,IAAI,gBAAgB,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,CAAC;gBACvD,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,gBAAgB,EAAE,KAAK,CAAC,CAAA;YAC3D,CAAC;QACL,CAAC;aAAM,IAAI,gBAAgB,EAAE,CAAC;YAC1B,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,gBAAgB,EAAE,KAAK,CAAC,CAAA;QAC3D,CAAC;QACD,OAAO,MAAM,CAAC,OAAO,EAAE,CAAA;IAC3B,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,YAAY,CACd,iBAAuC,EACvC,OAAsC;QAEtC,OAAO,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAA;IAC/D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAChB,iBAAuC,EACvC,KAAU;QAEV,OAAO,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAA;IAC7D,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,SAAS,CACX,iBAAuC,EACvC,GAAU,EACV,mBAA+D;QAE/D,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAA;QAC/D,MAAM,KAAK,GACP,IAAI,CAAC,gDAAgD,CACjD,mBAAmB,CACtB,IAAI,EAAE,CAAA;QACX,MAAM,gBAAgB,GAAG,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAA;QAC/D,KAAK,CAAC,KAAK,CAAC,GAAG;YACX,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE;gBAChB,IAAI,OAAO,EAAE,KAAK,QAAQ,EAAE,CAAC;oBACzB,OAAO,IAAI,gBAAgB,CAAC,EAAE,CAAC,CAAA;gBACnC,CAAC;gBAED,IAAI,OAAO,EAAE,KAAK,QAAQ,EAAE,CAAC;oBACzB,IAAI,EAAE,YAAY,gBAAgB,EAAE,CAAC;wBACjC,OAAO,EAAE,CAAA;oBACb,CAAC;oBAED,MAAM,YAAY,GAAG,QAAQ,CAAC,cAAe,CAAC,YAAY,CAAA;oBAE1D,IAAI,EAAE,CAAC,YAAY,CAAC,YAAY,gBAAgB,EAAE,CAAC;wBAC/C,OAAO,EAAE,CAAC,YAAY,CAAC,CAAA;oBAC3B,CAAC;gBACL,CAAC;YACL,CAAC,CAAC;SACL,CAAA;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAClC,iBAAiB,EACjB,KAAuB,CAC1B,CAAA;QACD,IAAI,gBAAgB,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,EAAE,CAAC;YAC1D,IAAI,mBAAmB,CAAC,MAAM;gBAC1B,MAAM,CAAC,OAAO,CACV,IAAI,CAAC,yCAAyC,CAC1C,mBAAmB,CAAC,MAAM,CAC7B,CACJ,CAAA;YACL,IAAI,mBAAmB,CAAC,IAAI;gBAAE,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA;YACnE,IAAI,mBAAmB,CAAC,IAAI;gBAAE,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA;YACpE,IAAI,mBAAmB,CAAC,KAAK;gBACzB,MAAM,CAAC,IAAI,CACP,IAAI,CAAC,sCAAsC,CACvC,mBAAmB,CAAC,KAAK,CAC5B,CACJ,CAAA;QACT,CAAC;QACD,OAAO,MAAM,CAAC,OAAO,EAAE,CAAA;IAC3B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO,CACT,iBAAuC,EACvC,OAAoC;QAEpC,OAAO,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAA;IAC1D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CACX,iBAAuC,EACvC,KAAU;QAEV,OAAO,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAA;IACxD,CAAC;IAED;;;;;;;;OAQG;IACH,KAAK,CAAC,WAAW,CACb,iBAAuC,EACvC,EAAqC;QAErC,OAAO,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAA;IACrD,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,MAAM,CACR,MAA4B,EAC5B,MAEsC;QAEtC,4CAA4C;QAC5C,MAAM,MAAM,GAAG,IAAI,YAAY,EAAE,CAAA;QACjC,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YACxB,MAAM,CAAC,GAAG,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;YAClD,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,GAAQ,EAAE,EAAE;gBACrD,MAAM,UAAU,GAAG,MAAM,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA;gBAC9C,MAAM,CAAC,aAAa,CAAC,IAAI,CACrB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,kBAAkB,CACrC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC,EACnC,UAAU,CACZ,CACL,CAAA;gBACD,MAAM,CAAC,WAAW,CAAC,IAAI,CACnB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,kBAAkB,CACrC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC,EACnC,UAAU,CACZ,CACL,CAAA;YACL,CAAC,CAAC,CAAA;QACN,CAAC;aAAM,CAAC;YACJ,MAAM,CAAC,GAAG,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;YACjD,MAAM,CAAC,aAAa,CAAC,IAAI,CACrB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,kBAAkB,CACrC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC,EACnC,MAAM,CAAC,GAAG,CAAC,UAAU,CACvB,CACL,CAAA;YACD,MAAM,CAAC,WAAW,CAAC,IAAI,CACnB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,kBAAkB,CACrC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC,EACnC,MAAM,CAAC,GAAG,CAAC,UAAU,CACvB,CACL,CAAA;QACL,CAAC;QAED,OAAO,MAAM,CAAA;IACjB,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,MAAM,CACR,MAA4B,EAC5B,QASmB,EACnB,aAA6C;QAE7C,MAAM,MAAM,GAAG,IAAI,YAAY,EAAE,CAAA;QAEjC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC1B,MAAM,aAAa,GAAG,MAAM,OAAO,CAAC,GAAG,CAClC,QAAkB,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,EAAE;gBACrC,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,YAAY,EAAE,aAAa,CAAC,CAAA;YAC3D,CAAC,CAAC,CACL,CAAA;YAED,MAAM,CAAC,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;YAC5C,MAAM,CAAC,QAAQ,GAAG,aAAa;iBAC1B,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC;iBAC3B,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAA;YAC/B,MAAM,CAAC,aAAa,GAAG,aAAa,CAAC,MAAM,CACvC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,aAAa,CAAC,EACnC,EAAqB,CACxB,CAAA;QACL,CAAC;aAAM,CAAC;YACJ,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA;YACpD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,UAAU,CACrC,MAAM,EACN,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAC7C,EAAE,IAAI,EAAE,aAAa,EAAE,CAC1B,CAAA;YAED,MAAM,CAAC,GAAG,GAAG,WAAW,CAAA;YACxB,MAAM,CAAC,QAAQ,GAAG,WAAW,CAAC,aAAa,CAAA;QAC/C,CAAC;QAED,OAAO,MAAM,CAAA;IACjB,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,MAAM,CACR,MAA4B,EAC5B,QASqB;QAErB,MAAM,MAAM,GAAG,IAAI,YAAY,EAAE,CAAA;QAEjC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC1B,MAAM,aAAa,GAAG,MAAM,OAAO,CAAC,GAAG,CAClC,QAAkB,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,EAAE;gBACrC,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,YAAY,CAAC,CAAA;YAC5C,CAAC,CAAC,CACL,CAAA;YAED,MAAM,CAAC,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;YAC5C,MAAM,CAAC,QAAQ,GAAG,aAAa;iBAC1B,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC;iBAC3B,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAA;QACnC,CAAC;aAAM,CAAC;YACJ,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,UAAU,CACrC,MAAM,EACN,IAAI,CAAC,oBAAoB,CACrB,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC,EACnC,QAAQ,CACX,CACJ,CAAA;YAED,MAAM,CAAC,GAAG,GAAG,WAAW,CAAA;YACxB,MAAM,CAAC,QAAQ,GAAG,WAAW,CAAC,YAAY,CAAA;QAC9C,CAAC;QAED,OAAO,MAAM,CAAA;IACjB,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,YAAY,CACR,iBAAuC,EACvC,QAAuB,EAAE;QAEzB,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAA;QAC/D,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,EAAE,KAAK,CAAC,CAAA;IAClE,CAAC;IAED;;;OAGG;IACH,kBAAkB,CACd,iBAAuC,EACvC,QAAuB,EAAE;QAEzB,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAA;QAC/D,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAA;QAC1D,IAAI,CAAC,iCAAiC,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAA;QACxD,OAAO,MAAM,CAAA;IACjB,CAAC;IAED;;OAEG;IACH,SAAS,CACL,iBAAuC,EACvC,QAAoB,EACpB,OAA0B;QAE1B,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAA;QAC/D,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAClC,QAAQ,CAAC,SAAS,EAClB,QAAQ,EACR,OAAO,CACV,CAAA;IACL,CAAC;IAED;;;OAGG;IACH,eAAe,CACX,iBAAuC,EACvC,QAAoB,EACpB,OAA0B;QAE1B,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAA;QAC/D,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAC1C,QAAQ,CAAC,SAAS,EAClB,QAAQ,EACR,OAAO,CACV,CAAA;QACD,IAAI,CAAC,iCAAiC,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAA;QACxD,OAAO,MAAM,CAAA;IACjB,CAAC;IAED;;OAEG;IACH,SAAS,CACL,iBAAuC,EACvC,UAA6C,EAC7C,OAA0B;QAE1B,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAA;QAC/D,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAClC,QAAQ,CAAC,SAAS,EAClB,UAAU,EACV,OAAO,CACV,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CACD,iBAAuC,EACvC,QAA0B,EAAE,EAC5B,UAAwB,EAAE;QAE1B,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAA;QAC/D,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,EAAE,KAAK,EAAE,OAAO,CAAC,CAAA;IAC1E,CAAC;IAED;;OAEG;IACH,cAAc,CACV,iBAAuC,EACvC,QAA0B,EAAE,EAC5B,UAAiC,EAAE;QAEnC,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAA;QAC/D,OAAO,IAAI,CAAC,gBAAgB,CAAC,cAAc,CACvC,QAAQ,CAAC,SAAS,EAClB,KAAK,EACL,OAAO,CACV,CAAA;IACL,CAAC;IAED;;OAEG;IACH,OAAO,CACH,iBAAuC,EACvC,KAAqB,EACrB,OAAsB;QAEtB,OAAO,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAA;IACxD,CAAC;IAED;;OAEG;IACH,qBAAqB,CACjB,iBAAuC,EACvC,WAA+B,EAC/B,OAA8B;QAE9B,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAA;QAC/D,OAAO,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,CAC9C,QAAQ,CAAC,SAAS,EAClB,WAAW,EACX,OAAO,CACV,CAAA;IACL,CAAC;IAED;;;;OAIG;IACH,uBAAuB,CACnB,iBAAuC,EACvC,UAA8B;QAE9B,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAA;QAC/D,OAAO,IAAI,CAAC,gBAAgB,CAAC,uBAAuB,CAChD,QAAQ,CAAC,SAAS,EAClB,UAAU,CACb,CAAA;IACL,CAAC;IAED;;OAEG;IACH,UAAU,CACN,iBAAuC,EACvC,KAAuB,EACvB,UAAyB,EAAE;QAE3B,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAA;QAC/D,OAAO,IAAI,CAAC,gBAAgB,CAAC,UAAU,CACnC,QAAQ,CAAC,SAAS,EAClB,KAAK,EACL,OAAO,CACV,CAAA;IACL,CAAC;IAED;;OAEG;IACH,SAAS,CACL,iBAAuC,EACvC,KAAuB,EACvB,UAAyB,EAAE;QAE3B,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAA;QAC/D,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAClC,QAAQ,CAAC,SAAS,EAClB,KAAK,EACL,OAAO,CACV,CAAA;IACL,CAAC;IAED;;OAEG;IACH,QAAQ,CACJ,iBAAuC,EACvC,GAAW,EACX,KAAuB,EACvB,OAAiC;QAEjC,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAA;QAC/D,OAAO,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CACjC,QAAQ,CAAC,SAAS,EAClB,GAAG,EACH,KAAK,EACL,OAAO,CACV,CAAA;IACL,CAAC;IAED;;OAEG;IACH,mBAAmB,CACf,iBAAuC,EACvC,SAAiB,EACjB,OAAiC;QAEjC,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAA;QAC/D,OAAO,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAC5C,QAAQ,CAAC,SAAS,EAClB,SAAS,EACT,OAAO,CACV,CAAA;IACL,CAAC;IAED;;OAEG;IACH,qBAAqB,CACjB,iBAAuC;QAEvC,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAA;QAC/D,OAAO,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAA;IAC1E,CAAC;IAED;;OAEG;IACH,gBAAgB,CACZ,iBAAuC,EACvC,KAAoB,EACpB,OAAiC;QAEjC,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAA;QAC/D,OAAO,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CACzC,QAAQ,CAAC,SAAS,EAClB,KAAK,EACL,OAAO,CACV,CAAA;IACL,CAAC;IAED;;OAEG;IACH,iBAAiB,CACb,iBAAuC,EACvC,KAAuB,EACvB,WAAqB,EACrB,OAAkC;QAElC,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAA;QAC/D,OAAO,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAC1C,QAAQ,CAAC,SAAS,EAClB,KAAK,EACL,WAAW,EACX,OAAO,CACV,CAAA;IACL,CAAC;IAED;;OAEG;IACH,gBAAgB,CACZ,iBAAuC,EACvC,KAAuB,EACvB,MAA8B,EAC9B,OAAiC;QAEjC,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAA;QAC/D,OAAO,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CACzC,QAAQ,CAAC,SAAS,EAClB,KAAK,EACL,MAAM,EACN,OAAO,CACV,CAAA;IACL,CAAC;IAED;;OAEG;IACH,iBAAiB,CACb,iBAAuC;QAEvC,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAA;QAC/D,OAAO,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAA;IACtE,CAAC;IAED;;OAEG;IACH,qBAAqB,CACjB,iBAAuC,EACvC,OAA0B;QAE1B,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAA;QAC/D,OAAO,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,CAC9C,QAAQ,CAAC,SAAS,EAClB,OAAO,CACV,CAAA;IACL,CAAC;IAED;;OAEG;IACH,0BAA0B,CACtB,iBAAuC,EACvC,OAAiC;QAEjC,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAA;QAC/D,OAAO,IAAI,CAAC,gBAAgB,CAAC,0BAA0B,CACnD,QAAQ,CAAC,SAAS,EAClB,OAAO,CACV,CAAA;IACL,CAAC;IAED;;OAEG;IACH,uBAAuB,CACnB,iBAAuC,EACvC,OAA0B;QAE1B,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAA;QAC/D,OAAO,IAAI,CAAC,gBAAgB,CAAC,uBAAuB,CAChD,QAAQ,CAAC,SAAS,EAClB,OAAO,CACV,CAAA;IACL,CAAC;IAED;;OAEG;IACH,yBAAyB,CACrB,iBAAuC,EACvC,OAA0B;QAE1B,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAA;QAC/D,OAAO,IAAI,CAAC,gBAAgB,CAAC,yBAAyB,CAClD,QAAQ,CAAC,SAAS,EAClB,OAAO,CACV,CAAA;IACL,CAAC;IAED;;OAEG;IACH,UAAU,CACN,iBAAuC,EACvC,IAA4B,EAC5B,OAA0B;QAE1B,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAA;QAC/D,OAAO,IAAI,CAAC,gBAAgB,CAAC,UAAU,CACnC,QAAQ,CAAC,SAAS,EAClB,IAAI,EACJ,OAAO,CACV,CAAA;IACL,CAAC;IAED;;OAEG;IACH,SAAS,CACL,iBAAuC,EACvC,GAAyB,EACzB,OAA0B;QAE1B,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAA;QAC/D,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,QAAQ,CAAC,SAAS,EAAE,GAAG,EAAE,OAAO,CAAC,CAAA;IAC5E,CAAC;IAED;;OAEG;IACH,QAAQ,CAAS,iBAAuC;QACpD,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAA;QAC/D,OAAO,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAA;IAC7D,CAAC;IAED;;OAEG;IACH,qBAAqB,CACjB,iBAAuC,EACvC,OAA4B;QAE5B,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAA;QAC/D,OAAO,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,CAC9C,QAAQ,CAAC,SAAS,EAClB,OAAO,CACV,CAAA;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CACF,iBAAuC,EACvC,OAAe,EACf,OAAuB;QAEvB,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAA;QAC/D,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAC/B,QAAQ,CAAC,SAAS,EAClB,OAAO,EACP,OAAO,CACV,CAAA;IACL,CAAC;IAED;;OAEG;IACH,UAAU,CACN,iBAAuC,EACvC,KAAuB,EACvB,GAAa,EACb,OAAwB;QAExB,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAA;QAC/D,OAAO,IAAI,CAAC,gBAAgB,CAAC,UAAU,CACnC,QAAQ,CAAC,SAAS,EAClB,KAAK,EACL,GAAG,EACH,OAAO,CACV,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CACD,iBAAuC,EACvC,OAA0B;QAE1B,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAA;QAC/D,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,EAAE,OAAO,CAAC,CAAA;IACnE,CAAC;IAED,KAAK,CACD,iBAAuC,EACvC,QAAqB,EACrB,OAA6B;QAE7B,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAA;QAC/D,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAC9B,QAAQ,CAAC,SAAS,EAClB,QAAQ,EACR,OAAO,CACV,CAAA;IACL,CAAC;IAED;;OAEG;IACH,UAAU,CACN,iBAAuC,EACvC,KAAuB,EACvB,MAA8B,EAC9B,OAAuB;QAEvB,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAA;QAC/D,OAAO,IAAI,CAAC,gBAAgB,CAAC,UAAU,CACnC,QAAQ,CAAC,SAAS,EAClB,KAAK,EACL,MAAM,EACN,OAAO,CACV,CAAA;IACL,CAAC;IAED;;OAEG;IACH,SAAS,CACL,iBAAuC,EACvC,KAAuB,EACvB,MAA8B,EAC9B,OAAuB;QAEvB,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAA;QAC/D,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAClC,QAAQ,CAAC,SAAS,EAClB,KAAK,EACL,MAAM,EACN,OAAO,CACV,CAAA;IACL,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;OAEG;IACO,gDAAgD,CACtD,mBAKe;QAEf,IAAI,CAAC,mBAAmB;YAAE,OAAO,SAAS,CAAA;QAE1C,IAAI,gBAAgB,CAAC,iBAAiB,CAAS,mBAAmB,CAAC;YAC/D,gFAAgF;YAChF,iCAAiC;YACjC,OAAO,OAAO,mBAAmB,CAAC,KAAK,KAAK,QAAQ;gBAChD,CAAC,CAAC,EAAE;gBACJ,CAAC,CAAC,mBAAmB,CAAC,KAAK,CAAA;QAEnC,OAAO,mBAAmB,CAAA;IAC9B,CAAC;IAED;;OAEG;IACO,+CAA+C,CACrD,mBAGe;QAEf,IAAI,CAAC,mBAAmB;YAAE,OAAO,SAAS,CAAA;QAE1C,IAAI,gBAAgB,CAAC,gBAAgB,CAAS,mBAAmB,CAAC;YAC9D,gFAAgF;YAChF,iCAAiC;YACjC,OAAO,OAAO,mBAAmB,CAAC,KAAK,KAAK,QAAQ;gBAChD,CAAC,CAAC,EAAE;gBACJ,CAAC,CAAC,mBAAmB,CAAC,KAAK,CAAA;QAEnC,OAAO,mBAAmB,CAAA;IAC9B,CAAC;IAED;;OAEG;IACO,sCAAsC,CAAC,KAAoB;QACjE,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,aAAa,EAAE,GAAG,EAAE,EAAE;YACpD,QAAQ,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC;gBACjB,KAAK,MAAM;oBACP,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAA;oBACvB,MAAK;gBACT,KAAK,KAAK;oBACN,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;oBACtB,MAAK;gBACT;oBACI,aAAa,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAA;YACvC,CAAC;YACD,OAAO,aAAa,CAAA;QACxB,CAAC,EAAE,EAAmB,CAAC,CAAA;IAC3B,CAAC;IAED;;OAEG;IACO,yCAAyC,CAC/C,OAAgE;QAEhE,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;YACzB,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,eAAe,EAAE,GAAG,EAAE,EAAE;gBAC3C,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;gBACxB,OAAO,eAAe,CAAA;YAC1B,CAAC,EAAE,EAAS,CAAC,CAAA;QACjB,CAAC;aAAM,CAAC;YACJ,kBAAkB;YAClB,OAAO,EAAE,CAAA;QACb,CAAC;IACL,CAAC;IAED;;OAEG;IACO,oBAAoB,CAC1B,QAAwB,EACxB,KAAU;QAEV,MAAM,gBAAgB,GAAG,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAA;QAE/D,2CAA2C;QAC3C,oDAAoD;QACpD,IAAI,gBAAgB,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YAClC,OAAO;gBACH,GAAG,EAAE,IAAI,gBAAgB,CAAC,KAAK,CAAC;aACnC,CAAA;QACL,CAAC;QAED,mEAAmE;QACnE,uGAAuG;QACvG,IAAI,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YAC9B,OAAO,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE;gBAC7C,MAAM,WAAW,GAAG,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;gBAChD,IAAI,WAAW,KAAK,SAAS;oBACzB,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,WAAW,CAAA;gBAC5C,OAAO,KAAK,CAAA;YAChB,CAAC,EAAE,EAAS,CAAC,CAAA;QACjB,CAAC;QAED,uDAAuD;QACvD,oGAAoG;QACpG,wCAAwC;QACxC,OAAO;YACH,GAAG,EAAE,IAAI,gBAAgB,CAAC,KAAK,CAAC;SACnC,CAAA;IACL,CAAC;IAED;;OAEG;IACO,iCAAiC,CACvC,QAAwB,EACxB,MAAsD;QAEtD,MAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAExC;QAAC,MAAc,CAAC,iBAAiB,CAAC,GAAG,MAAM,CAAC,OAAO,CAAA;QACpD,MAAM,CAAC,OAAO,GAAG,KAAK,IAAI,EAAE,CACtB,MAAc,CAAC,iBAAiB,CAAsB,EAAE,CAAC,IAAI,CAC3D,KAAK,EAAE,OAAiB,EAAE,EAAE;YACxB,MAAM,WAAW,GAAG,IAAI,2BAA2B,EAAE,CAAA;YACrD,MAAM,QAAQ,GAAG,WAAW,CAAC,YAAY,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAA;YAC5D,0BAA0B;YAC1B,MAAM,WAAW,CAAC,WAAW,CAAC,SAAS,CACnC,MAAM,EACN,QAAQ,EACR,QAAQ,CACX,CAAA;YACD,OAAO,QAAQ,CAAA;QACnB,CAAC,CACJ,CACJ;QAAC,MAAc,CAAC,aAAa,CAAC,GAAG,MAAM,CAAC,IAAI,CAAA;QAC7C,MAAM,CAAC,IAAI,GAAG,KAAK,IAAI,EAAE,CACnB,MAAc,CAAC,aAAa,CAAsB,EAAE,CAAC,IAAI,CACvD,KAAK,EAAE,MAAc,EAAE,EAAE;YACrB,IAAI,CAAC,MAAM,EAAE,CAAC;gBACV,OAAO,MAAM,CAAA;YACjB,CAAC;YACD,MAAM,WAAW,GAAG,IAAI,2BAA2B,EAAE,CAAA;YACrD,MAAM,MAAM,GAAG,WAAW,CAAC,SAAS,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAA;YACtD,0BAA0B;YAC1B,MAAM,WAAW,CAAC,WAAW,CAAC,SAAS,CAAC,MAAM,EAAE,QAAQ,EAAE;gBACtD,MAAM;aACT,CAAC,CAAA;YACF,OAAO,MAAM,CAAA;QACjB,CAAC,CACJ,CAAA;IACT,CAAC;IAES,iBAAiB,CACvB,MAA0B,EAC1B,gBAAgC,EAChC,KAAqB;QAErB,MAAM,EAAE,GAAG,EAAE,GAAG,SAAS,EAAE,GAAG,KAAK,IAAI,EAAE,CAAA;QACzC,MAAM,CAAC,MAAM,CAAC;YACV,GAAG,EAAE;gBACD,EAAE,CAAC,gBAAgB,CAAC,YAAY,CAAC,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE;gBAClD,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;aACrC;YACD,GAAG,SAAS;SACf,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,cAAc,CAC1B,iBAAuC,EACvC,mBAAyB,EACzB,YAA0C;QAE1C,MAAM,gBAAgB,GAAG,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAA;QAC/D,MAAM,EAAE,GACJ,mBAAmB,YAAY,gBAAgB;YAC/C,OAAO,mBAAmB,KAAK,QAAQ;YACnC,CAAC,CAAC,mBAAmB;YACrB,CAAC,CAAC,SAAS,CAAA;QACnB,MAAM,0BAA0B,GAAG,CAC/B,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,mBAAmB,CACnC,CAAA;QACR,MAAM,KAAK,GACP,IAAI,CAAC,+CAA+C,CAChD,0BAA0B,CAC7B,IAAI,EAAE,CAAA;QACX,IAAI,EAAE,EAAE,CAAC;YACL,KAAK,CAAC,KAAK,CAAC;gBACR,EAAE,YAAY,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,gBAAgB,CAAC,EAAE,CAAC,CAAA;QACtE,CAAC;QACD,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAS,iBAAiB,EAAE,KAAK,CAAC,CAAA;QACxE,MAAM,gBAAgB,GAClB,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC,gBAAgB,CAAA;QACnE,IAAI,gBAAgB,CAAC,gBAAgB,CAAC,0BAA0B,CAAC,EAAE,CAAC;YAChE,IAAI,0BAA0B,CAAC,MAAM;gBACjC,MAAM,CAAC,OAAO,CACV,IAAI,CAAC,yCAAyC,CAC1C,0BAA0B,CAAC,MAAM,CACpC,CACJ,CAAA;YACL,IAAI,0BAA0B,CAAC,KAAK;gBAChC,MAAM,CAAC,IAAI,CACP,IAAI,CAAC,sCAAsC,CACvC,0BAA0B,CAAC,KAAK,CACnC,CACJ,CAAA;YACL,IAAI,gBAAgB,IAAI,CAAC,0BAA0B,CAAC,WAAW,EAAE,CAAC;gBAC9D,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,gBAAgB,EAAE,KAAK,CAAC,CAAA;YAC3D,CAAC;QACL,CAAC;aAAM,IAAI,gBAAgB,EAAE,CAAC;YAC1B,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,gBAAgB,EAAE,KAAK,CAAC,CAAA;QAC3D,CAAC;QAED,+CAA+C;QAC/C,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAA;QAC9C,OAAO,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;IAC/C,CAAC;IAES,KAAK,CAAC,WAAW,CACvB,iBAAuC,EACvC,mBAGW;QAEX,MAAM,KAAK,GACP,IAAI,CAAC,gDAAgD,CACjD,mBAAmB,CACtB,CAAA;QACL,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAS,iBAAiB,EAAE,KAAK,CAAC,CAAA;QACxE,MAAM,gBAAgB,GAClB,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC,gBAAgB,CAAA;QAEnE,IAAI,gBAAgB,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,EAAE,CAAC;YAC1D,IAAI,mBAAmB,CAAC,MAAM;gBAC1B,MAAM,CAAC,OAAO,CACV,IAAI,CAAC,yCAAyC,CAC1C,mBAAmB,CAAC,MAAM,CAC7B,CACJ,CAAA;YACL,IAAI,mBAAmB,CAAC,IAAI;gBAAE,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA;YACnE,IAAI,mBAAmB,CAAC,IAAI;gBAAE,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA;YACpE,IAAI,mBAAmB,CAAC,KAAK;gBACzB,MAAM,CAAC,IAAI,CACP,IAAI,CAAC,sCAAsC,CACvC,mBAAmB,CAAC,KAAK,CAC5B,CACJ,CAAA;YACL,IAAI,gBAAgB,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,CAAC;gBACvD,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,gBAAgB,EAAE,KAAK,CAAC,CAAA;YAC3D,CAAC;QACL,CAAC;aAAM,IAAI,gBAAgB,EAAE,CAAC;YAC1B,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,gBAAgB,EAAE,KAAK,CAAC,CAAA;QAC3D,CAAC;QACD,OAAO,MAAM,CAAC,OAAO,EAAE,CAAA;IAC3B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CACrB,iBAAuC,EACvC,mBAAoE;QAEpE,MAAM,KAAK,GACP,IAAI,CAAC,gDAAgD,CACjD,mBAAmB,CACtB,CAAA;QACL,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAA;QACtE,MAAM,gBAAgB,GAClB,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC,gBAAgB,CAAA;QAEnE,IAAI,gBAAgB,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,EAAE,CAAC;YAC1D,IAAI,mBAAmB,CAAC,MAAM;gBAC1B,MAAM,CAAC,OAAO,CACV,IAAI,CAAC,yCAAyC,CAC1C,mBAAmB,CAAC,MAAM,CAC7B,CACJ,CAAA;YACL,IAAI,mBAAmB,CAAC,IAAI;gBAAE,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA;YACnE,IAAI,mBAAmB,CAAC,IAAI;gBAAE,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAA;YACpE,IAAI,mBAAmB,CAAC,KAAK;gBACzB,MAAM,CAAC,IAAI,CACP,IAAI,CAAC,sCAAsC,CACvC,mBAAmB,CAAC,KAAK,CAC5B,CACJ,CAAA;YACL,IAAI,gBAAgB,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,CAAC;gBACvD,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,gBAAgB,EAAE,KAAK,CAAC,CAAA;YAC3D,CAAC;QACL,CAAC;aAAM,IAAI,gBAAgB,EAAE,CAAC;YAC1B,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,gBAAgB,EAAE,KAAK,CAAC,CAAA;QAC3D,CAAC;QACD,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAM;YAC5C,MAAM,CAAC,OAAO,EAAE;YAChB,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,KAAK,CAAC;SACvC,CAAC,CAAA;QACF,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAA;IACrC,CAAC;CACJ", "file": "MongoEntityManager.js", "sourcesContent": ["import { EntityManager } from \"./EntityManager\"\nimport { EntityTarget } from \"../common/EntityTarget\"\n\nimport { ObjectLiteral } from \"../common/ObjectLiteral\"\nimport { MongoQueryRunner } from \"../driver/mongodb/MongoQueryRunner\"\nimport { MongoDriver } from \"../driver/mongodb/MongoDriver\"\nimport { DocumentToEntityTransformer } from \"../query-builder/transformer/DocumentToEntityTransformer\"\nimport { FindManyOptions } from \"../find-options/FindManyOptions\"\nimport { FindOptionsUtils } from \"../find-options/FindOptionsUtils\"\nimport { PlatformTools } from \"../platform/PlatformTools\"\nimport { QueryDeepPartialEntity } from \"../query-builder/QueryPartialEntity\"\nimport { InsertResult } from \"../query-builder/result/InsertResult\"\nimport { UpdateResult } from \"../query-builder/result/UpdateResult\"\nimport { DeleteResult } from \"../query-builder/result/DeleteResult\"\nimport { EntityMetadata } from \"../metadata/EntityMetadata\"\n\nimport {\n    BulkWriteResult,\n    AggregationCursor,\n    Collection,\n    FindCursor,\n    Document,\n    AggregateOptions,\n    AnyBulkWriteOperation,\n    BulkWriteOptions,\n    Filter,\n    CountOptions,\n    IndexSpecification,\n    CreateIndexesOptions,\n    IndexDescription,\n    DeleteResult as DeleteResultMongoDb,\n    DeleteOptions,\n    CommandOperationOptions,\n    FindOneAndDeleteOptions,\n    FindOneAndReplaceOptions,\n    UpdateFilter,\n    FindOneAndUpdateOptions,\n    RenameOptions,\n    ReplaceOptions,\n    UpdateResult as UpdateResultMongoDb,\n    CollStats,\n    CollStatsOptions,\n    ChangeStreamOptions,\n    ChangeStream,\n    UpdateOptions,\n    ListIndexesOptions,\n    ListIndexesCursor,\n    OptionalId,\n    InsertOneOptions,\n    InsertOneResult,\n    InsertManyResult,\n    UnorderedBulkOperation,\n    OrderedBulkOperation,\n    IndexInformationOptions,\n    ObjectId,\n    FilterOperators,\n    CountDocumentsOptions,\n} from \"../driver/mongodb/typings\"\nimport { DataSource } from \"../data-source/DataSource\"\nimport { MongoFindManyOptions } from \"../find-options/mongodb/MongoFindManyOptions\"\nimport { MongoFindOneOptions } from \"../find-options/mongodb/MongoFindOneOptions\"\nimport {\n    FindOptionsSelect,\n    FindOptionsSelectByString,\n} from \"../find-options/FindOptionsSelect\"\nimport { ObjectUtils } from \"../util/ObjectUtils\"\nimport { ColumnMetadata } from \"../metadata/ColumnMetadata\"\n\n/**\n * Entity manager supposed to work with any entity, automatically find its repository and call its methods,\n * whatever entity type are you passing.\n *\n * This implementation is used for MongoDB driver which has some specifics in its EntityManager.\n */\nexport class MongoEntityManager extends EntityManager {\n    readonly \"@instanceof\" = Symbol.for(\"MongoEntityManager\")\n\n    get mongoQueryRunner(): MongoQueryRunner {\n        return (this.connection.driver as MongoDriver)\n            .queryRunner as MongoQueryRunner\n    }\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(connection: DataSource) {\n        super(connection)\n    }\n\n    // -------------------------------------------------------------------------\n    // Overridden Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Finds entities that match given find options.\n     */\n    /**\n     * Finds entities that match given find options or conditions.\n     */\n    async find<Entity>(\n        entityClassOrName: EntityTarget<Entity>,\n        optionsOrConditions?:\n            | FindManyOptions<Entity>\n            | Partial<Entity>\n            | FilterOperators<Entity>,\n    ): Promise<Entity[]> {\n        const query =\n            this.convertFindManyOptionsOrConditionsToMongodbQuery(\n                optionsOrConditions,\n            )\n        const cursor = this.createEntityCursor<Entity>(\n            entityClassOrName,\n            query as Filter<Entity>,\n        )\n        const deleteDateColumn =\n            this.connection.getMetadata(entityClassOrName).deleteDateColumn\n        if (FindOptionsUtils.isFindManyOptions(optionsOrConditions)) {\n            if (optionsOrConditions.select)\n                cursor.project(\n                    this.convertFindOptionsSelectToProjectCriteria(\n                        optionsOrConditions.select,\n                    ),\n                )\n            if (optionsOrConditions.skip) cursor.skip(optionsOrConditions.skip)\n            if (optionsOrConditions.take) cursor.limit(optionsOrConditions.take)\n            if (optionsOrConditions.order)\n                cursor.sort(\n                    this.convertFindOptionsOrderToOrderCriteria(\n                        optionsOrConditions.order,\n                    ),\n                )\n            if (deleteDateColumn && !optionsOrConditions.withDeleted) {\n                this.filterSoftDeleted(cursor, deleteDateColumn, query)\n            }\n        } else if (deleteDateColumn) {\n            this.filterSoftDeleted(cursor, deleteDateColumn, query)\n        }\n        return cursor.toArray()\n    }\n\n    /**\n     * Finds entities that match given find options or conditions.\n     * Also counts all entities that match given conditions,\n     * but ignores pagination settings (from and take options).\n     */\n    async findAndCount<Entity>(\n        entityClassOrName: EntityTarget<Entity>,\n        options?: MongoFindManyOptions<Entity>,\n    ): Promise<[Entity[], number]> {\n        return this.executeFindAndCount(entityClassOrName, options)\n    }\n\n    /**\n     * Finds entities that match given where conditions.\n     */\n    async findAndCountBy<Entity>(\n        entityClassOrName: EntityTarget<Entity>,\n        where: any,\n    ): Promise<[Entity[], number]> {\n        return this.executeFindAndCount(entityClassOrName, where)\n    }\n\n    /**\n     * Finds entities by ids.\n     * Optionally find options can be applied.\n     *\n     * @deprecated use `findBy` method instead.\n     */\n    async findByIds<Entity>(\n        entityClassOrName: EntityTarget<Entity>,\n        ids: any[],\n        optionsOrConditions?: FindManyOptions<Entity> | Partial<Entity>,\n    ): Promise<Entity[]> {\n        const metadata = this.connection.getMetadata(entityClassOrName)\n        const query =\n            this.convertFindManyOptionsOrConditionsToMongodbQuery(\n                optionsOrConditions,\n            ) || {}\n        const objectIdInstance = PlatformTools.load(\"mongodb\").ObjectId\n        query[\"_id\"] = {\n            $in: ids.map((id) => {\n                if (typeof id === \"string\") {\n                    return new objectIdInstance(id)\n                }\n\n                if (typeof id === \"object\") {\n                    if (id instanceof objectIdInstance) {\n                        return id\n                    }\n\n                    const propertyName = metadata.objectIdColumn!.propertyName\n\n                    if (id[propertyName] instanceof objectIdInstance) {\n                        return id[propertyName]\n                    }\n                }\n            }),\n        }\n\n        const cursor = this.createEntityCursor<Entity>(\n            entityClassOrName,\n            query as Filter<Entity>,\n        )\n        if (FindOptionsUtils.isFindManyOptions(optionsOrConditions)) {\n            if (optionsOrConditions.select)\n                cursor.project(\n                    this.convertFindOptionsSelectToProjectCriteria(\n                        optionsOrConditions.select,\n                    ),\n                )\n            if (optionsOrConditions.skip) cursor.skip(optionsOrConditions.skip)\n            if (optionsOrConditions.take) cursor.limit(optionsOrConditions.take)\n            if (optionsOrConditions.order)\n                cursor.sort(\n                    this.convertFindOptionsOrderToOrderCriteria(\n                        optionsOrConditions.order,\n                    ),\n                )\n        }\n        return cursor.toArray()\n    }\n\n    /**\n     * Finds first entity that matches given conditions and/or find options.\n     */\n    async findOne<Entity>(\n        entityClassOrName: EntityTarget<Entity>,\n        options: MongoFindOneOptions<Entity>,\n    ): Promise<Entity | null> {\n        return this.executeFindOne(entityClassOrName, options)\n    }\n\n    /**\n     * Finds first entity that matches given WHERE conditions.\n     */\n    async findOneBy<Entity>(\n        entityClassOrName: EntityTarget<Entity>,\n        where: any,\n    ): Promise<Entity | null> {\n        return this.executeFindOne(entityClassOrName, where)\n    }\n\n    /**\n     * Finds entity that matches given id.\n     *\n     * @deprecated use `findOneBy` method instead in conjunction with `In` operator, for example:\n     *\n     * .findOneBy({\n     *     id: 1 // where \"id\" is your primary column name\n     * })\n     */\n    async findOneById<Entity>(\n        entityClassOrName: EntityTarget<Entity>,\n        id: string | number | Date | ObjectId,\n    ): Promise<Entity | null> {\n        return this.executeFindOne(entityClassOrName, id)\n    }\n\n    /**\n     * Inserts a given entity into the database.\n     * Unlike save method executes a primitive operation without cascades, relations and other operations included.\n     * Executes fast and efficient INSERT query.\n     * Does not check if entity exist in the database, so query will fail if duplicate entity is being inserted.\n     * You can execute bulk inserts using this method.\n     */\n    async insert<Entity>(\n        target: EntityTarget<Entity>,\n        entity:\n            | QueryDeepPartialEntity<Entity>\n            | QueryDeepPartialEntity<Entity>[],\n    ): Promise<InsertResult> {\n        // todo: convert entity to its database name\n        const result = new InsertResult()\n        if (Array.isArray(entity)) {\n            result.raw = await this.insertMany(target, entity)\n            Object.keys(result.raw.insertedIds).forEach((key: any) => {\n                const insertedId = result.raw.insertedIds[key]\n                result.generatedMaps.push(\n                    this.connection.driver.createGeneratedMap(\n                        this.connection.getMetadata(target),\n                        insertedId,\n                    )!,\n                )\n                result.identifiers.push(\n                    this.connection.driver.createGeneratedMap(\n                        this.connection.getMetadata(target),\n                        insertedId,\n                    )!,\n                )\n            })\n        } else {\n            result.raw = await this.insertOne(target, entity)\n            result.generatedMaps.push(\n                this.connection.driver.createGeneratedMap(\n                    this.connection.getMetadata(target),\n                    result.raw.insertedId,\n                )!,\n            )\n            result.identifiers.push(\n                this.connection.driver.createGeneratedMap(\n                    this.connection.getMetadata(target),\n                    result.raw.insertedId,\n                )!,\n            )\n        }\n\n        return result\n    }\n\n    /**\n     * Updates entity partially. Entity can be found by a given conditions.\n     * Unlike save method executes a primitive operation without cascades, relations and other operations included.\n     * Executes fast and efficient UPDATE query.\n     * Does not check if entity exist in the database.\n     */\n    async update<Entity>(\n        target: EntityTarget<Entity>,\n        criteria:\n            | string\n            | string[]\n            | number\n            | number[]\n            | Date\n            | Date[]\n            | ObjectId\n            | ObjectId[]\n            | ObjectLiteral,\n        partialEntity: QueryDeepPartialEntity<Entity>,\n    ): Promise<UpdateResult> {\n        const result = new UpdateResult()\n\n        if (Array.isArray(criteria)) {\n            const updateResults = await Promise.all(\n                (criteria as any[]).map((criteriaItem) => {\n                    return this.update(target, criteriaItem, partialEntity)\n                }),\n            )\n\n            result.raw = updateResults.map((r) => r.raw)\n            result.affected = updateResults\n                .map((r) => r.affected || 0)\n                .reduce((c, r) => c + r, 0)\n            result.generatedMaps = updateResults.reduce(\n                (c, r) => c.concat(r.generatedMaps),\n                [] as ObjectLiteral[],\n            )\n        } else {\n            const metadata = this.connection.getMetadata(target)\n            const mongoResult = await this.updateMany(\n                target,\n                this.convertMixedCriteria(metadata, criteria),\n                { $set: partialEntity },\n            )\n\n            result.raw = mongoResult\n            result.affected = mongoResult.modifiedCount\n        }\n\n        return result\n    }\n\n    /**\n     * Deletes entities by a given conditions.\n     * Unlike save method executes a primitive operation without cascades, relations and other operations included.\n     * Executes fast and efficient DELETE query.\n     * Does not check if entity exist in the database.\n     */\n    async delete<Entity>(\n        target: EntityTarget<Entity>,\n        criteria:\n            | string\n            | string[]\n            | number\n            | number[]\n            | Date\n            | Date[]\n            | ObjectId\n            | ObjectId[]\n            | ObjectLiteral[],\n    ): Promise<DeleteResult> {\n        const result = new DeleteResult()\n\n        if (Array.isArray(criteria)) {\n            const deleteResults = await Promise.all(\n                (criteria as any[]).map((criteriaItem) => {\n                    return this.delete(target, criteriaItem)\n                }),\n            )\n\n            result.raw = deleteResults.map((r) => r.raw)\n            result.affected = deleteResults\n                .map((r) => r.affected || 0)\n                .reduce((c, r) => c + r, 0)\n        } else {\n            const mongoResult = await this.deleteMany(\n                target,\n                this.convertMixedCriteria(\n                    this.connection.getMetadata(target),\n                    criteria,\n                ),\n            )\n\n            result.raw = mongoResult\n            result.affected = mongoResult.deletedCount\n        }\n\n        return result\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates a cursor for a query that can be used to iterate over results from MongoDB.\n     */\n    createCursor<Entity, T = any>(\n        entityClassOrName: EntityTarget<Entity>,\n        query: ObjectLiteral = {},\n    ): FindCursor<T> {\n        const metadata = this.connection.getMetadata(entityClassOrName)\n        return this.mongoQueryRunner.cursor(metadata.tableName, query)\n    }\n\n    /**\n     * Creates a cursor for a query that can be used to iterate over results from MongoDB.\n     * This returns modified version of cursor that transforms each result into Entity model.\n     */\n    createEntityCursor<Entity>(\n        entityClassOrName: EntityTarget<Entity>,\n        query: ObjectLiteral = {},\n    ): FindCursor<Entity> {\n        const metadata = this.connection.getMetadata(entityClassOrName)\n        const cursor = this.createCursor(entityClassOrName, query)\n        this.applyEntityTransformationToCursor(metadata, cursor)\n        return cursor\n    }\n\n    /**\n     * Execute an aggregation framework pipeline against the collection.\n     */\n    aggregate<Entity, R = any>(\n        entityClassOrName: EntityTarget<Entity>,\n        pipeline: Document[],\n        options?: AggregateOptions,\n    ): AggregationCursor<R> {\n        const metadata = this.connection.getMetadata(entityClassOrName)\n        return this.mongoQueryRunner.aggregate(\n            metadata.tableName,\n            pipeline,\n            options,\n        )\n    }\n\n    /**\n     * Execute an aggregation framework pipeline against the collection.\n     * This returns modified version of cursor that transforms each result into Entity model.\n     */\n    aggregateEntity<Entity>(\n        entityClassOrName: EntityTarget<Entity>,\n        pipeline: Document[],\n        options?: AggregateOptions,\n    ): AggregationCursor<Entity> {\n        const metadata = this.connection.getMetadata(entityClassOrName)\n        const cursor = this.mongoQueryRunner.aggregate(\n            metadata.tableName,\n            pipeline,\n            options,\n        )\n        this.applyEntityTransformationToCursor(metadata, cursor)\n        return cursor\n    }\n\n    /**\n     * Perform a bulkWrite operation without a fluent API.\n     */\n    bulkWrite<Entity>(\n        entityClassOrName: EntityTarget<Entity>,\n        operations: AnyBulkWriteOperation<Document>[],\n        options?: BulkWriteOptions,\n    ): Promise<BulkWriteResult> {\n        const metadata = this.connection.getMetadata(entityClassOrName)\n        return this.mongoQueryRunner.bulkWrite(\n            metadata.tableName,\n            operations,\n            options,\n        )\n    }\n\n    /**\n     * Count number of matching documents in the db to a query.\n     */\n    count<Entity>(\n        entityClassOrName: EntityTarget<Entity>,\n        query: Filter<Document> = {},\n        options: CountOptions = {},\n    ): Promise<number> {\n        const metadata = this.connection.getMetadata(entityClassOrName)\n        return this.mongoQueryRunner.count(metadata.tableName, query, options)\n    }\n\n    /**\n     * Count number of matching documents in the db to a query.\n     */\n    countDocuments<Entity>(\n        entityClassOrName: EntityTarget<Entity>,\n        query: Filter<Document> = {},\n        options: CountDocumentsOptions = {},\n    ): Promise<number> {\n        const metadata = this.connection.getMetadata(entityClassOrName)\n        return this.mongoQueryRunner.countDocuments(\n            metadata.tableName,\n            query,\n            options,\n        )\n    }\n\n    /**\n     * Count number of matching documents in the db to a query.\n     */\n    countBy<Entity>(\n        entityClassOrName: EntityTarget<Entity>,\n        query?: ObjectLiteral,\n        options?: CountOptions,\n    ): Promise<number> {\n        return this.count(entityClassOrName, query, options)\n    }\n\n    /**\n     * Creates an index on the db and collection.\n     */\n    createCollectionIndex<Entity>(\n        entityClassOrName: EntityTarget<Entity>,\n        fieldOrSpec: IndexSpecification,\n        options?: CreateIndexesOptions,\n    ): Promise<string> {\n        const metadata = this.connection.getMetadata(entityClassOrName)\n        return this.mongoQueryRunner.createCollectionIndex(\n            metadata.tableName,\n            fieldOrSpec,\n            options,\n        )\n    }\n\n    /**\n     * Creates multiple indexes in the collection, this method is only supported for MongoDB 2.6 or higher.\n     * Earlier version of MongoDB will throw a command not supported error.\n     * Index specifications are defined at http://docs.mongodb.org/manual/reference/command/createIndexes/.\n     */\n    createCollectionIndexes<Entity>(\n        entityClassOrName: EntityTarget<Entity>,\n        indexSpecs: IndexDescription[],\n    ): Promise<string[]> {\n        const metadata = this.connection.getMetadata(entityClassOrName)\n        return this.mongoQueryRunner.createCollectionIndexes(\n            metadata.tableName,\n            indexSpecs,\n        )\n    }\n\n    /**\n     * Delete multiple documents on MongoDB.\n     */\n    deleteMany<Entity>(\n        entityClassOrName: EntityTarget<Entity>,\n        query: Filter<Document>,\n        options: DeleteOptions = {},\n    ): Promise<DeleteResultMongoDb> {\n        const metadata = this.connection.getMetadata(entityClassOrName)\n        return this.mongoQueryRunner.deleteMany(\n            metadata.tableName,\n            query,\n            options,\n        )\n    }\n\n    /**\n     * Delete a document on MongoDB.\n     */\n    deleteOne<Entity>(\n        entityClassOrName: EntityTarget<Entity>,\n        query: Filter<Document>,\n        options: DeleteOptions = {},\n    ): Promise<DeleteResultMongoDb> {\n        const metadata = this.connection.getMetadata(entityClassOrName)\n        return this.mongoQueryRunner.deleteOne(\n            metadata.tableName,\n            query,\n            options,\n        )\n    }\n\n    /**\n     * The distinct command returns returns a list of distinct values for the given key across a collection.\n     */\n    distinct<Entity>(\n        entityClassOrName: EntityTarget<Entity>,\n        key: string,\n        query: Filter<Document>,\n        options?: CommandOperationOptions,\n    ): Promise<any> {\n        const metadata = this.connection.getMetadata(entityClassOrName)\n        return this.mongoQueryRunner.distinct(\n            metadata.tableName,\n            key,\n            query,\n            options,\n        )\n    }\n\n    /**\n     * Drops an index from this collection.\n     */\n    dropCollectionIndex<Entity>(\n        entityClassOrName: EntityTarget<Entity>,\n        indexName: string,\n        options?: CommandOperationOptions,\n    ): Promise<any> {\n        const metadata = this.connection.getMetadata(entityClassOrName)\n        return this.mongoQueryRunner.dropCollectionIndex(\n            metadata.tableName,\n            indexName,\n            options,\n        )\n    }\n\n    /**\n     * Drops all indexes from the collection.\n     */\n    dropCollectionIndexes<Entity>(\n        entityClassOrName: EntityTarget<Entity>,\n    ): Promise<any> {\n        const metadata = this.connection.getMetadata(entityClassOrName)\n        return this.mongoQueryRunner.dropCollectionIndexes(metadata.tableName)\n    }\n\n    /**\n     * Find a document and delete it in one atomic operation, requires a write lock for the duration of the operation.\n     */\n    findOneAndDelete<Entity>(\n        entityClassOrName: EntityTarget<Entity>,\n        query: ObjectLiteral,\n        options?: FindOneAndDeleteOptions,\n    ): Promise<Document | null> {\n        const metadata = this.connection.getMetadata(entityClassOrName)\n        return this.mongoQueryRunner.findOneAndDelete(\n            metadata.tableName,\n            query,\n            options,\n        )\n    }\n\n    /**\n     * Find a document and replace it in one atomic operation, requires a write lock for the duration of the operation.\n     */\n    findOneAndReplace<Entity>(\n        entityClassOrName: EntityTarget<Entity>,\n        query: Filter<Document>,\n        replacement: Document,\n        options?: FindOneAndReplaceOptions,\n    ): Promise<Document | null> {\n        const metadata = this.connection.getMetadata(entityClassOrName)\n        return this.mongoQueryRunner.findOneAndReplace(\n            metadata.tableName,\n            query,\n            replacement,\n            options,\n        )\n    }\n\n    /**\n     * Find a document and update it in one atomic operation, requires a write lock for the duration of the operation.\n     */\n    findOneAndUpdate<Entity>(\n        entityClassOrName: EntityTarget<Entity>,\n        query: Filter<Document>,\n        update: UpdateFilter<Document>,\n        options?: FindOneAndUpdateOptions,\n    ): Promise<Document | null> {\n        const metadata = this.connection.getMetadata(entityClassOrName)\n        return this.mongoQueryRunner.findOneAndUpdate(\n            metadata.tableName,\n            query,\n            update,\n            options,\n        )\n    }\n\n    /**\n     * Retrieve all the indexes on the collection.\n     */\n    collectionIndexes<Entity>(\n        entityClassOrName: EntityTarget<Entity>,\n    ): Promise<Document> {\n        const metadata = this.connection.getMetadata(entityClassOrName)\n        return this.mongoQueryRunner.collectionIndexes(metadata.tableName)\n    }\n\n    /**\n     * Retrieve all the indexes on the collection.\n     */\n    collectionIndexExists<Entity>(\n        entityClassOrName: EntityTarget<Entity>,\n        indexes: string | string[],\n    ): Promise<boolean> {\n        const metadata = this.connection.getMetadata(entityClassOrName)\n        return this.mongoQueryRunner.collectionIndexExists(\n            metadata.tableName,\n            indexes,\n        )\n    }\n\n    /**\n     * Retrieves this collections index info.\n     */\n    collectionIndexInformation<Entity>(\n        entityClassOrName: EntityTarget<Entity>,\n        options?: IndexInformationOptions,\n    ): Promise<any> {\n        const metadata = this.connection.getMetadata(entityClassOrName)\n        return this.mongoQueryRunner.collectionIndexInformation(\n            metadata.tableName,\n            options,\n        )\n    }\n\n    /**\n     * Initiate an In order bulk write operation, operations will be serially executed in the order they are added, creating a new operation for each switch in types.\n     */\n    initializeOrderedBulkOp<Entity>(\n        entityClassOrName: EntityTarget<Entity>,\n        options?: BulkWriteOptions,\n    ): OrderedBulkOperation {\n        const metadata = this.connection.getMetadata(entityClassOrName)\n        return this.mongoQueryRunner.initializeOrderedBulkOp(\n            metadata.tableName,\n            options,\n        )\n    }\n\n    /**\n     * Initiate a Out of order batch write operation. All operations will be buffered into insert/update/remove commands executed out of order.\n     */\n    initializeUnorderedBulkOp<Entity>(\n        entityClassOrName: EntityTarget<Entity>,\n        options?: BulkWriteOptions,\n    ): UnorderedBulkOperation {\n        const metadata = this.connection.getMetadata(entityClassOrName)\n        return this.mongoQueryRunner.initializeUnorderedBulkOp(\n            metadata.tableName,\n            options,\n        )\n    }\n\n    /**\n     * Inserts an array of documents into MongoDB.\n     */\n    insertMany<Entity>(\n        entityClassOrName: EntityTarget<Entity>,\n        docs: OptionalId<Document>[],\n        options?: BulkWriteOptions,\n    ): Promise<InsertManyResult> {\n        const metadata = this.connection.getMetadata(entityClassOrName)\n        return this.mongoQueryRunner.insertMany(\n            metadata.tableName,\n            docs,\n            options,\n        )\n    }\n\n    /**\n     * Inserts a single document into MongoDB.\n     */\n    insertOne<Entity>(\n        entityClassOrName: EntityTarget<Entity>,\n        doc: OptionalId<Document>,\n        options?: InsertOneOptions,\n    ): Promise<InsertOneResult> {\n        const metadata = this.connection.getMetadata(entityClassOrName)\n        return this.mongoQueryRunner.insertOne(metadata.tableName, doc, options)\n    }\n\n    /**\n     * Returns if the collection is a capped collection.\n     */\n    isCapped<Entity>(entityClassOrName: EntityTarget<Entity>): Promise<any> {\n        const metadata = this.connection.getMetadata(entityClassOrName)\n        return this.mongoQueryRunner.isCapped(metadata.tableName)\n    }\n\n    /**\n     * Get the list of all indexes information for the collection.\n     */\n    listCollectionIndexes<Entity>(\n        entityClassOrName: EntityTarget<Entity>,\n        options?: ListIndexesOptions,\n    ): ListIndexesCursor {\n        const metadata = this.connection.getMetadata(entityClassOrName)\n        return this.mongoQueryRunner.listCollectionIndexes(\n            metadata.tableName,\n            options,\n        )\n    }\n\n    /**\n     * Reindex all indexes on the collection Warning: reIndex is a blocking operation (indexes are rebuilt in the foreground) and will be slow for large collections.\n     */\n    rename<Entity>(\n        entityClassOrName: EntityTarget<Entity>,\n        newName: string,\n        options?: RenameOptions,\n    ): Promise<Collection<Document>> {\n        const metadata = this.connection.getMetadata(entityClassOrName)\n        return this.mongoQueryRunner.rename(\n            metadata.tableName,\n            newName,\n            options,\n        )\n    }\n\n    /**\n     * Replace a document on MongoDB.\n     */\n    replaceOne<Entity>(\n        entityClassOrName: EntityTarget<Entity>,\n        query: Filter<Document>,\n        doc: Document,\n        options?: ReplaceOptions,\n    ): Promise<Document | UpdateResultMongoDb> {\n        const metadata = this.connection.getMetadata(entityClassOrName)\n        return this.mongoQueryRunner.replaceOne(\n            metadata.tableName,\n            query,\n            doc,\n            options,\n        )\n    }\n\n    /**\n     * Get all the collection statistics.\n     */\n    stats<Entity>(\n        entityClassOrName: EntityTarget<Entity>,\n        options?: CollStatsOptions,\n    ): Promise<CollStats> {\n        const metadata = this.connection.getMetadata(entityClassOrName)\n        return this.mongoQueryRunner.stats(metadata.tableName, options)\n    }\n\n    watch<Entity>(\n        entityClassOrName: EntityTarget<Entity>,\n        pipeline?: Document[],\n        options?: ChangeStreamOptions,\n    ): ChangeStream {\n        const metadata = this.connection.getMetadata(entityClassOrName)\n        return this.mongoQueryRunner.watch(\n            metadata.tableName,\n            pipeline,\n            options,\n        )\n    }\n\n    /**\n     * Update multiple documents on MongoDB.\n     */\n    updateMany<Entity>(\n        entityClassOrName: EntityTarget<Entity>,\n        query: Filter<Document>,\n        update: UpdateFilter<Document>,\n        options?: UpdateOptions,\n    ): Promise<Document | UpdateResultMongoDb> {\n        const metadata = this.connection.getMetadata(entityClassOrName)\n        return this.mongoQueryRunner.updateMany(\n            metadata.tableName,\n            query,\n            update,\n            options,\n        )\n    }\n\n    /**\n     * Update a single document on MongoDB.\n     */\n    updateOne<Entity>(\n        entityClassOrName: EntityTarget<Entity>,\n        query: Filter<Document>,\n        update: UpdateFilter<Document>,\n        options?: UpdateOptions,\n    ): Promise<Document | UpdateResultMongoDb> {\n        const metadata = this.connection.getMetadata(entityClassOrName)\n        return this.mongoQueryRunner.updateOne(\n            metadata.tableName,\n            query,\n            update,\n            options,\n        )\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Converts FindManyOptions to mongodb query.\n     */\n    protected convertFindManyOptionsOrConditionsToMongodbQuery<Entity>(\n        optionsOrConditions:\n            | MongoFindManyOptions<Entity>\n            | Partial<Entity>\n            | FilterOperators<Entity>\n            | any[]\n            | undefined,\n    ): ObjectLiteral | undefined {\n        if (!optionsOrConditions) return undefined\n\n        if (FindOptionsUtils.isFindManyOptions<Entity>(optionsOrConditions))\n            // If where condition is passed as a string which contains sql we have to ignore\n            // as mongo is not a sql database\n            return typeof optionsOrConditions.where === \"string\"\n                ? {}\n                : optionsOrConditions.where\n\n        return optionsOrConditions\n    }\n\n    /**\n     * Converts FindOneOptions to mongodb query.\n     */\n    protected convertFindOneOptionsOrConditionsToMongodbQuery<Entity>(\n        optionsOrConditions:\n            | MongoFindOneOptions<Entity>\n            | Partial<Entity>\n            | undefined,\n    ): ObjectLiteral | undefined {\n        if (!optionsOrConditions) return undefined\n\n        if (FindOptionsUtils.isFindOneOptions<Entity>(optionsOrConditions))\n            // If where condition is passed as a string which contains sql we have to ignore\n            // as mongo is not a sql database\n            return typeof optionsOrConditions.where === \"string\"\n                ? {}\n                : optionsOrConditions.where\n\n        return optionsOrConditions\n    }\n\n    /**\n     * Converts FindOptions into mongodb order by criteria.\n     */\n    protected convertFindOptionsOrderToOrderCriteria(order: ObjectLiteral) {\n        return Object.keys(order).reduce((orderCriteria, key) => {\n            switch (order[key]) {\n                case \"DESC\":\n                    orderCriteria[key] = -1\n                    break\n                case \"ASC\":\n                    orderCriteria[key] = 1\n                    break\n                default:\n                    orderCriteria[key] = order[key]\n            }\n            return orderCriteria\n        }, {} as ObjectLiteral)\n    }\n\n    /**\n     * Converts FindOptions into mongodb select by criteria.\n     */\n    protected convertFindOptionsSelectToProjectCriteria(\n        selects: FindOptionsSelect<any> | FindOptionsSelectByString<any>,\n    ) {\n        if (Array.isArray(selects)) {\n            return selects.reduce((projectCriteria, key) => {\n                projectCriteria[key] = 1\n                return projectCriteria\n            }, {} as any)\n        } else {\n            // todo: implement\n            return {}\n        }\n    }\n\n    /**\n     * Ensures given id is an id for query.\n     */\n    protected convertMixedCriteria(\n        metadata: EntityMetadata,\n        idMap: any,\n    ): ObjectLiteral {\n        const objectIdInstance = PlatformTools.load(\"mongodb\").ObjectId\n\n        // check first if it's ObjectId compatible:\n        // string, number, Buffer, ObjectId or ObjectId-like\n        if (objectIdInstance.isValid(idMap)) {\n            return {\n                _id: new objectIdInstance(idMap),\n            }\n        }\n\n        // if it's some other type of object build a query from the columns\n        // this check needs to be after the ObjectId check, because a valid ObjectId is also an Object instance\n        if (ObjectUtils.isObject(idMap)) {\n            return metadata.columns.reduce((query, column) => {\n                const columnValue = column.getEntityValue(idMap)\n                if (columnValue !== undefined)\n                    query[column.databasePath] = columnValue\n                return query\n            }, {} as any)\n        }\n\n        // last resort: try to convert it to an ObjectId anyway\n        // most likely it will fail, but we want to be backwards compatible and keep the same thrown Errors.\n        // it can still pass with null/undefined\n        return {\n            _id: new objectIdInstance(idMap),\n        }\n    }\n\n    /**\n     * Overrides cursor's toArray and next methods to convert results to entity automatically.\n     */\n    protected applyEntityTransformationToCursor<Entity extends ObjectLiteral>(\n        metadata: EntityMetadata,\n        cursor: FindCursor<Entity> | AggregationCursor<Entity>,\n    ) {\n        const queryRunner = this.mongoQueryRunner\n\n        ;(cursor as any)[\"__to_array_func\"] = cursor.toArray\n        cursor.toArray = async () =>\n            ((cursor as any)[\"__to_array_func\"] as CallableFunction)().then(\n                async (results: Entity[]) => {\n                    const transformer = new DocumentToEntityTransformer()\n                    const entities = transformer.transformAll(results, metadata)\n                    // broadcast \"load\" events\n                    await queryRunner.broadcaster.broadcast(\n                        \"Load\",\n                        metadata,\n                        entities,\n                    )\n                    return entities\n                },\n            )\n        ;(cursor as any)[\"__next_func\"] = cursor.next\n        cursor.next = async () =>\n            ((cursor as any)[\"__next_func\"] as CallableFunction)().then(\n                async (result: Entity) => {\n                    if (!result) {\n                        return result\n                    }\n                    const transformer = new DocumentToEntityTransformer()\n                    const entity = transformer.transform(result, metadata)\n                    // broadcast \"load\" events\n                    await queryRunner.broadcaster.broadcast(\"Load\", metadata, [\n                        entity,\n                    ])\n                    return entity\n                },\n            )\n    }\n\n    protected filterSoftDeleted<Entity>(\n        cursor: FindCursor<Entity>,\n        deleteDateColumn: ColumnMetadata,\n        query?: ObjectLiteral,\n    ) {\n        const { $or, ...restQuery } = query ?? {}\n        cursor.filter({\n            $or: [\n                { [deleteDateColumn.propertyName]: { $eq: null } },\n                ...(Array.isArray($or) ? $or : []),\n            ],\n            ...restQuery,\n        })\n    }\n\n    /**\n     * Finds first entity that matches given conditions and/or find options.\n     */\n    protected async executeFindOne<Entity>(\n        entityClassOrName: EntityTarget<Entity>,\n        optionsOrConditions?: any,\n        maybeOptions?: MongoFindOneOptions<Entity>,\n    ): Promise<Entity | null> {\n        const objectIdInstance = PlatformTools.load(\"mongodb\").ObjectId\n        const id =\n            optionsOrConditions instanceof objectIdInstance ||\n            typeof optionsOrConditions === \"string\"\n                ? optionsOrConditions\n                : undefined\n        const findOneOptionsOrConditions = (\n            id ? maybeOptions : optionsOrConditions\n        ) as any\n        const query =\n            this.convertFindOneOptionsOrConditionsToMongodbQuery(\n                findOneOptionsOrConditions,\n            ) || {}\n        if (id) {\n            query[\"_id\"] =\n                id instanceof objectIdInstance ? id : new objectIdInstance(id)\n        }\n        const cursor = this.createEntityCursor<Entity>(entityClassOrName, query)\n        const deleteDateColumn =\n            this.connection.getMetadata(entityClassOrName).deleteDateColumn\n        if (FindOptionsUtils.isFindOneOptions(findOneOptionsOrConditions)) {\n            if (findOneOptionsOrConditions.select)\n                cursor.project(\n                    this.convertFindOptionsSelectToProjectCriteria(\n                        findOneOptionsOrConditions.select,\n                    ),\n                )\n            if (findOneOptionsOrConditions.order)\n                cursor.sort(\n                    this.convertFindOptionsOrderToOrderCriteria(\n                        findOneOptionsOrConditions.order,\n                    ),\n                )\n            if (deleteDateColumn && !findOneOptionsOrConditions.withDeleted) {\n                this.filterSoftDeleted(cursor, deleteDateColumn, query)\n            }\n        } else if (deleteDateColumn) {\n            this.filterSoftDeleted(cursor, deleteDateColumn, query)\n        }\n\n        // const result = await cursor.limit(1).next();\n        const result = await cursor.limit(1).toArray()\n        return result.length > 0 ? result[0] : null\n    }\n\n    protected async executeFind<Entity>(\n        entityClassOrName: EntityTarget<Entity>,\n        optionsOrConditions?:\n            | MongoFindManyOptions<Entity>\n            | Partial<Entity>\n            | any[],\n    ): Promise<Entity[]> {\n        const query =\n            this.convertFindManyOptionsOrConditionsToMongodbQuery(\n                optionsOrConditions,\n            )\n        const cursor = this.createEntityCursor<Entity>(entityClassOrName, query)\n        const deleteDateColumn =\n            this.connection.getMetadata(entityClassOrName).deleteDateColumn\n\n        if (FindOptionsUtils.isFindManyOptions(optionsOrConditions)) {\n            if (optionsOrConditions.select)\n                cursor.project(\n                    this.convertFindOptionsSelectToProjectCriteria(\n                        optionsOrConditions.select,\n                    ),\n                )\n            if (optionsOrConditions.skip) cursor.skip(optionsOrConditions.skip)\n            if (optionsOrConditions.take) cursor.limit(optionsOrConditions.take)\n            if (optionsOrConditions.order)\n                cursor.sort(\n                    this.convertFindOptionsOrderToOrderCriteria(\n                        optionsOrConditions.order,\n                    ),\n                )\n            if (deleteDateColumn && !optionsOrConditions.withDeleted) {\n                this.filterSoftDeleted(cursor, deleteDateColumn, query)\n            }\n        } else if (deleteDateColumn) {\n            this.filterSoftDeleted(cursor, deleteDateColumn, query)\n        }\n        return cursor.toArray()\n    }\n\n    /**\n     * Finds entities that match given find options or conditions.\n     */\n    async executeFindAndCount<Entity>(\n        entityClassOrName: EntityTarget<Entity>,\n        optionsOrConditions?: MongoFindManyOptions<Entity> | Partial<Entity>,\n    ): Promise<[Entity[], number]> {\n        const query =\n            this.convertFindManyOptionsOrConditionsToMongodbQuery(\n                optionsOrConditions,\n            )\n        const cursor = await this.createEntityCursor(entityClassOrName, query)\n        const deleteDateColumn =\n            this.connection.getMetadata(entityClassOrName).deleteDateColumn\n\n        if (FindOptionsUtils.isFindManyOptions(optionsOrConditions)) {\n            if (optionsOrConditions.select)\n                cursor.project(\n                    this.convertFindOptionsSelectToProjectCriteria(\n                        optionsOrConditions.select,\n                    ),\n                )\n            if (optionsOrConditions.skip) cursor.skip(optionsOrConditions.skip)\n            if (optionsOrConditions.take) cursor.limit(optionsOrConditions.take)\n            if (optionsOrConditions.order)\n                cursor.sort(\n                    this.convertFindOptionsOrderToOrderCriteria(\n                        optionsOrConditions.order,\n                    ),\n                )\n            if (deleteDateColumn && !optionsOrConditions.withDeleted) {\n                this.filterSoftDeleted(cursor, deleteDateColumn, query)\n            }\n        } else if (deleteDateColumn) {\n            this.filterSoftDeleted(cursor, deleteDateColumn, query)\n        }\n        const [results, count] = await Promise.all<any>([\n            cursor.toArray(),\n            this.count(entityClassOrName, query),\n        ])\n        return [results, parseInt(count)]\n    }\n}\n"], "sourceRoot": ".."}