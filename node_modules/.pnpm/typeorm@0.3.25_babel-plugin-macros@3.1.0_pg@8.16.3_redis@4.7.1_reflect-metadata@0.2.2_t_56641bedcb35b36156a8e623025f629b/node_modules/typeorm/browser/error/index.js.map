{"version": 3, "sources": ["../browser/src/error/index.ts"], "names": [], "mappings": "AAAA,cAAc,yCAAyC,CAAA;AACvD,cAAc,mCAAmC,CAAA;AACjD,cAAc,iCAAiC,CAAA;AAC/C,cAAc,sCAAsC,CAAA;AACpD,cAAc,sCAAsC,CAAA;AACpD,cAAc,2BAA2B,CAAA;AACzC,cAAc,gCAAgC,CAAA;AAC9C,cAAc,8BAA8B,CAAA;AAC5C,cAAc,8BAA8B,CAAA;AAC5C,cAAc,4BAA4B,CAAA;AAC1C,cAAc,mCAAmC,CAAA;AACjD,cAAc,iCAAiC,CAAA;AAC/C,cAAc,8BAA8B,CAAA;AAC5C,cAAc,kCAAkC,CAAA;AAChD,cAAc,uBAAuB,CAAA;AACrC,cAAc,+BAA+B,CAAA;AAC7C,cAAc,qBAAqB,CAAA;AACnC,cAAc,sCAAsC,CAAA;AACpD,cAAc,kCAAkC,CAAA;AAChD,cAAc,sCAAsC,CAAA;AACpD,cAAc,gDAAgD,CAAA;AAC9D,cAAc,2CAA2C,CAAA;AACzD,cAAc,uCAAuC,CAAA;AACrD,cAAc,0CAA0C,CAAA;AACxD,cAAc,gCAAgC,CAAA;AAC9C,cAAc,kCAAkC,CAAA;AAChD,cAAc,0BAA0B,CAAA;AACxC,cAAc,uCAAuC,CAAA;AACrD,cAAc,mCAAmC,CAAA;AACjD,cAAc,0BAA0B,CAAA;AACxC,cAAc,6BAA6B,CAAA;AAC3C,cAAc,+BAA+B,CAAA;AAC7C,cAAc,sBAAsB,CAAA;AACpC,cAAc,kCAAkC,CAAA;AAChD,cAAc,2CAA2C,CAAA;AACzD,cAAc,2BAA2B,CAAA;AACzC,cAAc,oCAAoC,CAAA;AAClD,cAAc,4BAA4B,CAAA;AAC1C,cAAc,mCAAmC,CAAA;AACjD,cAAc,0CAA0C,CAAA;AACxD,cAAc,2BAA2B,CAAA;AACzC,cAAc,8BAA8B,CAAA;AAC5C,cAAc,2CAA2C,CAAA;AACzD,cAAc,0BAA0B,CAAA;AACxC,cAAc,6BAA6B,CAAA;AAC3C,cAAc,4BAA4B,CAAA;AAC1C,cAAc,yBAAyB,CAAA;AACvC,cAAc,oBAAoB,CAAA;AAClC,cAAc,qCAAqC,CAAA;AACnD,cAAc,4CAA4C,CAAA;AAC1D,cAAc,2CAA2C,CAAA;AACzD,cAAc,iCAAiC,CAAA;AAC/C,cAAc,gCAAgC,CAAA;AAC9C,cAAc,oCAAoC,CAAA;AAClD,cAAc,4BAA4B,CAAA;AAC1C,cAAc,mCAAmC,CAAA;AACjD,cAAc,uCAAuC,CAAA;AACrD,cAAc,kCAAkC,CAAA;AAChD,cAAc,2BAA2B,CAAA;AACzC,cAAc,gBAAgB,CAAA;AAC9B,cAAc,yCAAyC,CAAA", "file": "index.js", "sourcesContent": ["export * from \"./CannotReflectMethodParameterTypeError\"\nexport * from \"./AlreadyHasActiveConnectionError\"\nexport * from \"./SubjectWithoutIdentifierError\"\nexport * from \"./CannotConnectAlreadyConnectedError\"\nexport * from \"./LockNotSupportedOnGivenDriverError\"\nexport * from \"./ConnectionIsNotSetError\"\nexport * from \"./CannotCreateEntityIdMapError\"\nexport * from \"./MetadataAlreadyExistsError\"\nexport * from \"./CannotDetermineEntityError\"\nexport * from \"./UpdateValuesMissingError\"\nexport * from \"./TreeRepositoryNotSupportedError\"\nexport * from \"./CustomRepositoryNotFoundError\"\nexport * from \"./TransactionNotStartedError\"\nexport * from \"./TransactionAlreadyStartedError\"\nexport * from \"./EntityNotFoundError\"\nexport * from \"./EntityMetadataNotFoundError\"\nexport * from \"./MustBeEntityError\"\nexport * from \"./OptimisticLockVersionMismatchError\"\nexport * from \"./LimitOnUpdateNotSupportedError\"\nexport * from \"./PrimaryColumnCannotBeNullableError\"\nexport * from \"./CustomRepositoryCannotInheritRepositoryError\"\nexport * from \"./QueryRunnerProviderAlreadyReleasedError\"\nexport * from \"./CannotAttachTreeChildrenEntityError\"\nexport * from \"./CustomRepositoryDoesNotHaveEntityError\"\nexport * from \"./MissingDeleteDateColumnError\"\nexport * from \"./NoConnectionForRepositoryError\"\nexport * from \"./CircularRelationsError\"\nexport * from \"./ReturningStatementNotSupportedError\"\nexport * from \"./UsingJoinTableIsNotAllowedError\"\nexport * from \"./MissingJoinColumnError\"\nexport * from \"./MissingPrimaryColumnError\"\nexport * from \"./EntityPropertyNotFoundError\"\nexport * from \"./MissingDriverError\"\nexport * from \"./DriverPackageNotInstalledError\"\nexport * from \"./CannotGetEntityManagerNotConnectedError\"\nexport * from \"./ConnectionNotFoundError\"\nexport * from \"./NoVersionOrUpdateDateColumnError\"\nexport * from \"./InsertValuesMissingError\"\nexport * from \"./OptimisticLockCanNotBeUsedError\"\nexport * from \"./MetadataWithSuchNameAlreadyExistsError\"\nexport * from \"./DriverOptionNotSetError\"\nexport * from \"./FindRelationsNotFoundError\"\nexport * from \"./PessimisticLockTransactionRequiredError\"\nexport * from \"./RepositoryNotTreeError\"\nexport * from \"./DataTypeNotSupportedError\"\nexport * from \"./InitializedRelationError\"\nexport * from \"./MissingJoinTableError\"\nexport * from \"./QueryFailedError\"\nexport * from \"./NoNeedToReleaseEntityManagerError\"\nexport * from \"./UsingJoinColumnOnlyOnOneSideAllowedError\"\nexport * from \"./UsingJoinTableOnlyOnOneSideAllowedError\"\nexport * from \"./SubjectRemovedAndUpdatedError\"\nexport * from \"./PersistedEntityNotFoundError\"\nexport * from \"./UsingJoinColumnIsNotAllowedError\"\nexport * from \"./ColumnTypeUndefinedError\"\nexport * from \"./QueryRunnerAlreadyReleasedError\"\nexport * from \"./OffsetWithoutLimitNotSupportedError\"\nexport * from \"./CannotExecuteNotConnectedError\"\nexport * from \"./NoConnectionOptionError\"\nexport * from \"./TypeORMError\"\nexport * from \"./ForbiddenTransactionModeOverrideError\"\n"], "sourceRoot": ".."}