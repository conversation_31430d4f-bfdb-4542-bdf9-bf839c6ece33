{"version": 3, "sources": ["../browser/src/connection/ConnectionMetadataBuilder.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,4BAA4B,EAAE,MAAM,wCAAwC,CAAA;AACrF,OAAO,EAAE,QAAQ,EAAE,MAAM,kBAAkB,CAAA;AAC3C,OAAO,EAAE,gBAAgB,EAAE,MAAM,cAAc,CAAA;AAE/C,OAAO,EAAE,sBAAsB,EAAE,MAAM,YAAY,CAAA;AACnD,OAAO,EAAE,qBAAqB,EAAE,MAAM,2CAA2C,CAAA;AACjF,OAAO,EAAE,uBAAuB,EAAE,MAAM,0CAA0C,CAAA;AAKlF,OAAO,EAAE,eAAe,EAAE,MAAM,yBAAyB,CAAA;AAEzD;;GAEG;AACH,MAAM,OAAO,yBAAyB;IAClC,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAsB,UAAsB;QAAtB,eAAU,GAAV,UAAU,CAAY;IAAG,CAAC;IAEhD,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,KAAK,CAAC,eAAe,CACjB,UAAiC;QAEjC,MAAM,CAAC,gBAAgB,EAAE,oBAAoB,CAAC,GAC1C,QAAQ,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAA;QAC/C,MAAM,mBAAmB,GAAG;YACxB,GAAG,gBAAgB;YACnB,GAAG,CAAC,MAAM,4BAA4B,CAClC,IAAI,CAAC,UAAU,CAAC,MAAM,EACtB,oBAAoB,CACvB,CAAC;SACL,CAAA;QACD,OAAO,mBAAmB,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,EAAE,CAC9C,gBAAgB,CAAqB,cAAc,CAAC,CACvD,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAClB,WAAkC;QAElC,MAAM,CAAC,iBAAiB,EAAE,qBAAqB,CAAC,GAC5C,QAAQ,CAAC,sBAAsB,CAAC,WAAW,IAAI,EAAE,CAAC,CAAA;QACtD,MAAM,oBAAoB,GAAG;YACzB,GAAG,iBAAiB;YACpB,GAAG,CAAC,MAAM,4BAA4B,CAClC,IAAI,CAAC,UAAU,CAAC,MAAM,EACtB,qBAAqB,CACxB,CAAC;SACL,CAAA;QACD,OAAO,sBAAsB,EAAE;aAC1B,iBAAiB,CAAC,oBAAoB,CAAC;aACvC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CACd,gBAAgB,CACZ,QAAQ,CAAC,MAAM,CAClB,CACJ,CAAA;IACT,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CACtB,QAAmD;QAEnD,iEAAiE;QAEjE,MAAM,CAAC,sBAAsB,EAAE,iBAAiB,CAAC,GAC7C,QAAQ,CAAC,sBAAsB,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAA;QACnD,MAAM,aAAa,GAAe,sBAAsB,CAAC,MAAM,CAC3D,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,eAAe,CAAC,cAAc,CAAC,WAAW,CAAC,CACzD,CAAA;QACR,MAAM,aAAa,GACf,sBAAsB,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,EAAE,CAC1C,eAAe,CAAC,cAAc,CAAC,WAAW,CAAC,CACvC,CAAA;QAEZ,MAAM,gBAAgB,GAAG;YACrB,GAAG,aAAa;YAChB,GAAG,CAAC,MAAM,4BAA4B,CAClC,IAAI,CAAC,UAAU,CAAC,MAAM,EACtB,iBAAiB,CACpB,CAAC;SACL,CAAA;QACD,gBAAgB,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,EAAE;YACrC,oDAAoD;YACpD,IAAI,eAAe,CAAC,cAAc,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC9C,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YACnC,CAAC;QACL,CAAC,CAAC,CAAA;QACF,MAAM,wBAAwB,GAAG,IAAI,qBAAqB,CACtD,IAAI,CAAC,UAAU,EACf,sBAAsB,EAAE,CAC3B,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAA;QAEzB,MAAM,6BAA6B,GAC/B,IAAI,uBAAuB,EAAE,CAAC,SAAS,CAAC,aAAa,CAAC,CAAA;QAC1D,MAAM,qBAAqB,GAAG,IAAI,qBAAqB,CACnD,IAAI,CAAC,UAAU,EACf,6BAA6B,CAChC,CAAC,KAAK,EAAE,CAAA;QAET,OAAO,CAAC,GAAG,wBAAwB,EAAE,GAAG,qBAAqB,CAAC,CAAA;IAClE,CAAC;CACJ", "file": "ConnectionMetadataBuilder.js", "sourcesContent": ["import { importClassesFromDirectories } from \"../util/DirectoryExportedClassesLoader\"\nimport { OrmUtils } from \"../util/OrmUtils\"\nimport { getFromContainer } from \"../container\"\nimport { MigrationInterface } from \"../migration/MigrationInterface\"\nimport { getMetadataArgsStorage } from \"../globals\"\nimport { EntityMetadataBuilder } from \"../metadata-builder/EntityMetadataBuilder\"\nimport { EntitySchemaTransformer } from \"../entity-schema/EntitySchemaTransformer\"\nimport { DataSource } from \"../data-source/DataSource\"\nimport { EntitySchema } from \"../entity-schema/EntitySchema\"\nimport { EntityMetadata } from \"../metadata/EntityMetadata\"\nimport { EntitySubscriberInterface } from \"../subscriber/EntitySubscriberInterface\"\nimport { InstanceChecker } from \"../util/InstanceChecker\"\n\n/**\n * Builds migration instances, subscriber instances and entity metadatas for the given classes.\n */\nexport class ConnectionMetadataBuilder {\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(protected connection: DataSource) {}\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Builds migration instances for the given classes or directories.\n     */\n    async buildMigrations(\n        migrations: (Function | string)[],\n    ): Promise<MigrationInterface[]> {\n        const [migrationClasses, migrationDirectories] =\n            OrmUtils.splitClassesAndStrings(migrations)\n        const allMigrationClasses = [\n            ...migrationClasses,\n            ...(await importClassesFromDirectories(\n                this.connection.logger,\n                migrationDirectories,\n            )),\n        ]\n        return allMigrationClasses.map((migrationClass) =>\n            getFromContainer<MigrationInterface>(migrationClass),\n        )\n    }\n\n    /**\n     * Builds subscriber instances for the given classes or directories.\n     */\n    async buildSubscribers(\n        subscribers: (Function | string)[],\n    ): Promise<EntitySubscriberInterface<any>[]> {\n        const [subscriberClasses, subscriberDirectories] =\n            OrmUtils.splitClassesAndStrings(subscribers || [])\n        const allSubscriberClasses = [\n            ...subscriberClasses,\n            ...(await importClassesFromDirectories(\n                this.connection.logger,\n                subscriberDirectories,\n            )),\n        ]\n        return getMetadataArgsStorage()\n            .filterSubscribers(allSubscriberClasses)\n            .map((metadata) =>\n                getFromContainer<EntitySubscriberInterface<any>>(\n                    metadata.target,\n                ),\n            )\n    }\n\n    /**\n     * Builds entity metadatas for the given classes or directories.\n     */\n    async buildEntityMetadatas(\n        entities: (Function | EntitySchema<any> | string)[],\n    ): Promise<EntityMetadata[]> {\n        // todo: instead we need to merge multiple metadata args storages\n\n        const [entityClassesOrSchemas, entityDirectories] =\n            OrmUtils.splitClassesAndStrings(entities || [])\n        const entityClasses: Function[] = entityClassesOrSchemas.filter(\n            (entityClass) => !InstanceChecker.isEntitySchema(entityClass),\n        ) as any\n        const entitySchemas: EntitySchema<any>[] =\n            entityClassesOrSchemas.filter((entityClass) =>\n                InstanceChecker.isEntitySchema(entityClass),\n            ) as any\n\n        const allEntityClasses = [\n            ...entityClasses,\n            ...(await importClassesFromDirectories(\n                this.connection.logger,\n                entityDirectories,\n            )),\n        ]\n        allEntityClasses.forEach((entityClass) => {\n            // if we have entity schemas loaded from directories\n            if (InstanceChecker.isEntitySchema(entityClass)) {\n                entitySchemas.push(entityClass)\n            }\n        })\n        const decoratorEntityMetadatas = new EntityMetadataBuilder(\n            this.connection,\n            getMetadataArgsStorage(),\n        ).build(allEntityClasses)\n\n        const metadataArgsStorageFromSchema =\n            new EntitySchemaTransformer().transform(entitySchemas)\n        const schemaEntityMetadatas = new EntityMetadataBuilder(\n            this.connection,\n            metadataArgsStorageFromSchema,\n        ).build()\n\n        return [...decoratorEntityMetadatas, ...schemaEntityMetadatas]\n    }\n}\n"], "sourceRoot": ".."}