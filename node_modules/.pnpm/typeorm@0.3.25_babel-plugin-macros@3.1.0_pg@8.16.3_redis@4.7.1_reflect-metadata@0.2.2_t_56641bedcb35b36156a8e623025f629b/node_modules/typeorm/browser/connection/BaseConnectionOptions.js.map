{"version": 3, "sources": ["../browser/src/connection/BaseConnectionOptions.ts"], "names": [], "mappings": "", "file": "BaseConnectionOptions.js", "sourcesContent": ["import { BaseDataSourceOptions } from \"../data-source/BaseDataSourceOptions\"\n\n/**\n * BaseConnectionOptions is set of connection options shared by all database types.\n *\n * @deprecated\n */\nexport type BaseConnectionOptions = BaseDataSourceOptions\n"], "sourceRoot": ".."}