{"version": 3, "sources": ["../browser/src/entity-schema/EntitySchemaInheritanceOptions.ts"], "names": [], "mappings": "", "file": "EntitySchemaInheritanceOptions.js", "sourcesContent": ["import { ColumnOptions } from \"../decorator/options/ColumnOptions\"\n\nexport interface EntitySchemaInheritanceOptions {\n    /**\n     * Inheritance pattern.\n     */\n    pattern?: \"STI\"\n\n    /**\n     * Inheritance discriminator column.\n     */\n    column?: string | ColumnOptions\n}\n"], "sourceRoot": ".."}