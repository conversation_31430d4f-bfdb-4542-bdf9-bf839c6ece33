{"version": 3, "sources": ["../browser/src/driver/spanner/SpannerDriver.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,8BAA8B,EAAE,MAAM,4CAA4C,CAAA;AAC3F,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAA;AAGzD,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAA;AAChD,OAAO,EAAE,aAAa,EAAE,MAAM,8BAA8B,CAAA;AAE5D,OAAO,EAAE,kBAAkB,EAAE,MAAM,yCAAyC,CAAA;AAM5E,OAAO,EAAE,cAAc,EAAE,MAAM,+BAA+B,CAAA;AAC9D,OAAO,EAAE,QAAQ,EAAE,MAAM,qBAAqB,CAAA;AAC9C,OAAO,EAAE,sBAAsB,EAAE,MAAM,mCAAmC,CAAA;AAE1E,OAAO,EAAE,KAAK,EAAE,MAAM,kCAAkC,CAAA;AACxD,OAAO,EAAE,IAAI,EAAE,MAAM,gCAAgC,CAAA;AACrD,OAAO,EAAE,eAAe,EAAE,MAAM,4CAA4C,CAAA;AAI5E;;GAEG;AACH,MAAM,OAAO,aAAa;IAyJtB,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAY,UAAsB;QAtHlC;;WAEG;QACH,iBAAY,GAAY,KAAK,CAAA;QAE7B;;WAEG;QACH,gBAAW,GAAG,IAAI,CAAA;QAElB;;WAEG;QACH,uBAAkB,GAAG,MAAe,CAAA;QAEpC;;;;WAIG;QACH,uBAAkB,GAAiB;YAC/B,MAAM;YACN,OAAO;YACP,SAAS;YACT,SAAS;YACT,QAAQ;YACR,MAAM;YACN,OAAO;YACP,MAAM;YACN,WAAW;YACX,OAAO;SACV,CAAA;QAED;;WAEG;QACH,yBAAoB,GAAiB,EAAE,CAAA;QAEvC;;WAEG;QACH,iBAAY,GAAiB,EAAE,CAAA;QAE/B;;WAEG;QACH,0BAAqB,GAAiB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA;QAEzD;;WAEG;QACH,yBAAoB,GAAiB,EAAE,CAAA;QAEvC;;WAEG;QACH,6BAAwB,GAAiB,EAAE,CAAA;QAE3C;;WAEG;QACH,yBAAoB,GAAiB,EAAE,CAAA;QAEvC;;;WAGG;QACH,oBAAe,GAAsB;YACjC,UAAU,EAAE,WAAW;YACvB,iBAAiB,EAAE,EAAE;YACrB,UAAU,EAAE,WAAW;YACvB,iBAAiB,EAAE,EAAE;YACrB,UAAU,EAAE,WAAW;YACvB,kBAAkB,EAAE,IAAI;YACxB,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,OAAO;YAClB,WAAW,EAAE,OAAO;YACpB,aAAa,EAAE,QAAQ;YACvB,kBAAkB,EAAE,OAAO;YAC3B,OAAO,EAAE,QAAQ;YACjB,eAAe,EAAE,QAAQ;YACzB,SAAS,EAAE,OAAO;YAClB,aAAa,EAAE,OAAO;YACtB,UAAU,EAAE,QAAQ;YACpB,WAAW,EAAE,QAAQ;YACrB,YAAY,EAAE,QAAQ;YACtB,gBAAgB,EAAE,QAAQ;YAC1B,cAAc,EAAE,QAAQ;YACxB,aAAa,EAAE,QAAQ;YACvB,YAAY,EAAE,QAAQ;YACtB,aAAa,EAAE,QAAQ;SAC1B,CAAA;QAED;;WAEG;QACH,qBAAgB,GAAW,QAAQ,CAAA;QAEnC;;;WAGG;QACH,qBAAgB,GAAqB,EAAE,CAAA;QAEvC;;;WAGG;QACH,mBAAc,GAAG,EAAE,CAAA;QAEnB,oBAAe,GAAoB;YAC/B,OAAO,EAAE,IAAI;SAChB,CAAA;QAOG,IAAI,CAAC,UAAU,GAAG,UAAU,CAAA;QAC5B,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,OAAmC,CAAA;QAC7D,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAA;QAE3D,qBAAqB;QACrB,IAAI,CAAC,gBAAgB,EAAE,CAAA;IAC3B,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,KAAK,CAAC,OAAO;QACT,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA;QAC9D,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA;IAC3E,CAAC;IAED;;OAEG;IACH,YAAY;QACR,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACZ,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAA;IACjC,CAAC;IAED;;OAEG;IACH,mBAAmB;QACf,OAAO,IAAI,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;IAClD,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,IAAqB;QACnC,OAAO,IAAI,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;IAC7C,CAAC;IAED;;;OAGG;IACH,yBAAyB,CACrB,GAAW,EACX,UAAyB,EACzB,gBAA+B;QAE/B,MAAM,iBAAiB,GAAU,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,GAAG,CAC9D,CAAC,GAAG,EAAE,EAAE,CAAC,gBAAgB,CAAC,GAAG,CAAC,CACjC,CAAA;QACD,IAAI,CAAC,UAAU,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM;YAC9C,OAAO,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAA;QAEnC,MAAM,iBAAiB,GAAG,IAAI,GAAG,EAAkB,CAAA;QACnD,GAAG,GAAG,GAAG,CAAC,OAAO,CACb,6BAA6B,EAC7B,CAAC,IAAI,EAAE,OAAe,EAAE,GAAW,EAAU,EAAE;YAC3C,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC;gBAClC,OAAO,IAAI,CAAA;YACf,CAAC;YAED,IAAI,iBAAiB,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC7B,OAAO,IAAI,CAAC,gBAAgB,GAAG,iBAAiB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;YAC7D,CAAC;YAED,MAAM,KAAK,GAAQ,UAAU,CAAC,GAAG,CAAC,CAAA;YAElC,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;gBACjB,OAAO,IAAI,CAAA;YACf,CAAC;YAED,IAAI,OAAO,EAAE,CAAC;gBACV,OAAO,KAAK;qBACP,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE;oBACZ,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;oBACzB,OAAO,IAAI,CAAC,eAAe,CACvB,GAAG,EACH,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAC/B,CAAA;gBACL,CAAC,CAAC;qBACD,IAAI,CAAC,IAAI,CAAC,CAAA;YACnB,CAAC;YAED,IAAI,KAAK,YAAY,QAAQ,EAAE,CAAC;gBAC5B,OAAO,KAAK,EAAE,CAAA;YAClB,CAAC;YAED,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YAC7B,iBAAiB,CAAC,GAAG,CAAC,GAAG,EAAE,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;YACxD,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;QAClE,CAAC,CACJ,CAAA,CAAC,kEAAkE;QAEpE,GAAG,GAAG,GAAG,CAAC,OAAO,CACb,4CAA4C,EAC5C,CACI,IAAI,EACJ,gBAAwB,EACxB,eAAuB,EACvB,OAAe,EACf,GAAW,EACL,EAAE;YACR,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC;gBAClC,OAAO,IAAI,CAAA;YACf,CAAC;YAED,MAAM,KAAK,GAAQ,UAAU,CAAC,GAAG,CAAC,CAAA;YAClC,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;gBACjB,OAAO,UAAU,CAAA;YACrB,CAAC;YAED,OAAO,IAAI,CAAA;QACf,CAAC,CACJ,CAAA;QACD,OAAO,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAA;IACnC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,UAAkB;QACrB,OAAO,KAAK,UAAU,IAAI,CAAA;IAC9B,CAAC;IAED;;;OAGG;IACH,cAAc,CACV,SAAiB,EACjB,MAAe,EACf,QAAiB;QAEjB,MAAM,SAAS,GAAG,CAAC,SAAS,CAAC,CAAA;QAE7B,IAAI,QAAQ,EAAE,CAAC;YACX,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;QAC/B,CAAC;QAED,OAAO,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IAC9B,CAAC;IAED;;OAEG;IACH,cAAc,CACV,MAAgE;QAEhE,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAA;QACpC,MAAM,YAAY,GAAQ,SAAS,CAAA;QAEnC,IAAI,MAAM,YAAY,KAAK,IAAI,MAAM,YAAY,IAAI,EAAE,CAAC;YACpD,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;YAE/C,OAAO;gBACH,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,IAAI,cAAc;gBAC9D,MAAM,EAAE,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,IAAI,YAAY;gBACtD,SAAS,EAAE,MAAM,CAAC,SAAS;aAC9B,CAAA;QACL,CAAC;QAED,IAAI,MAAM,YAAY,eAAe,EAAE,CAAC;YACpC,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAA;YAE9D,OAAO;gBACH,QAAQ,EACJ,MAAM,CAAC,kBAAkB;oBACzB,MAAM,CAAC,QAAQ;oBACf,cAAc;gBAClB,MAAM,EACF,MAAM,CAAC,gBAAgB,IAAI,MAAM,CAAC,MAAM,IAAI,YAAY;gBAC5D,SAAS,EAAE,MAAM,CAAC,SAAS;aAC9B,CAAA;QACL,CAAC;QAED,IAAI,MAAM,YAAY,cAAc,EAAE,CAAC;YACnC,2CAA2C;YAE3C,OAAO;gBACH,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,cAAc;gBAC3C,MAAM,EAAE,MAAM,CAAC,MAAM,IAAI,YAAY;gBACrC,SAAS,EAAE,MAAM,CAAC,SAAS;aAC9B,CAAA;QACL,CAAC;QAED,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QAE/B,OAAO;YACH,QAAQ,EACJ,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,cAAc;YAC/D,MAAM,EAAE,YAAY;YACpB,SAAS,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;SACpD,CAAA;IACL,CAAC;IAED;;OAEG;IACH,sBAAsB,CAAC,KAAU,EAAE,cAA8B;QAC7D,IAAI,cAAc,CAAC,WAAW;YAC1B,KAAK,GAAG,sBAAsB,CAAC,WAAW,CACtC,cAAc,CAAC,WAAW,EAC1B,KAAK,CACR,CAAA;QAEL,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS;YAAE,OAAO,KAAK,CAAA;QAEvD,IAAI,cAAc,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YACpC,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;YAChE,OAAO,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAA;QAChD,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACxC,OAAO,SAAS,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAA;QACjD,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACxC,OAAO,KAAK,CAAA;QAChB,CAAC;aAAM,IACH,cAAc,CAAC,IAAI,KAAK,WAAW;YACnC,cAAc,CAAC,IAAI,KAAK,IAAI,EAC9B,CAAC;YACC,OAAO,SAAS,CAAC,eAAe,CAAC,KAAK,CAAC,CAAA;QAC3C,CAAC;QAED,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,KAAU,EAAE,cAA8B;QAC3D,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS;YACrC,OAAO,cAAc,CAAC,WAAW;gBAC7B,CAAC,CAAC,sBAAsB,CAAC,aAAa,CAChC,cAAc,CAAC,WAAW,EAC1B,KAAK,CACR;gBACH,CAAC,CAAC,KAAK,CAAA;QAEf,IAAI,cAAc,CAAC,IAAI,KAAK,OAAO,IAAI,cAAc,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACpE,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAA;QAChC,CAAC;aAAM,IACH,cAAc,CAAC,IAAI,KAAK,WAAW;YACnC,cAAc,CAAC,IAAI,KAAK,IAAI,EAC9B,CAAC;YACC,KAAK,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,CAAA;QAC3B,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YAC3C,KAAK,GAAG,KAAK,CAAC,KAAK,CAAA;QACvB,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACxC,KAAK,GAAG,SAAS,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAA;QAClD,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACxC,KAAK,GAAG,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAA;QACjE,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACxC,8BAA8B;YAC9B,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAA;QACpD,CAAC;QAED,IAAI,cAAc,CAAC,WAAW;YAC1B,KAAK,GAAG,sBAAsB,CAAC,aAAa,CACxC,cAAc,CAAC,WAAW,EAC1B,KAAK,CACR,CAAA;QAEL,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,MAKb;QACG,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACzB,OAAO,OAAO,CAAA;QAClB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YAC1D,OAAO,QAAQ,CAAA;QACnB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;YAC9B,OAAO,WAAW,CAAA;QACtB,CAAC;aAAM,IAAK,MAAM,CAAC,IAAY,KAAK,MAAM,EAAE,CAAC;YACzC,OAAO,OAAO,CAAA;QAClB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YACjC,OAAO,MAAM,CAAA;QACjB,CAAC;aAAM,CAAC;YACJ,OAAQ,MAAM,CAAC,IAAe,IAAI,EAAE,CAAA;QACxC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACH,gBAAgB,CAAC,cAA8B;QAC3C,OAAO,cAAc,CAAC,OAAO,KAAK,EAAE;YAChC,CAAC,CAAC,IAAI,cAAc,CAAC,OAAO,GAAG;YAC/B,CAAC,CAAC,GAAG,cAAc,CAAC,OAAO,EAAE,CAAA;IACrC,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,MAAsB;QACpC,OAAO,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CACrC,CAAC,GAAG,EAAE,EAAE,CACJ,GAAG,CAAC,QAAQ;YACZ,GAAG,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC;YACxB,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM,CAChC,CAAA;IACL,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,MAAoC;QAChD,IAAI,MAAM,CAAC,MAAM;YAAE,OAAO,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAA;QAClD,IAAI,MAAM,CAAC,kBAAkB,KAAK,MAAM;YAAE,OAAO,IAAI,CAAA;QAErD,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;YAClB,KAAK,MAAM,CAAC;YACZ,KAAK,QAAQ,CAAC;YACd,KAAK,OAAO;gBACR,OAAO,KAAK,CAAA;YAChB;gBACI,OAAO,EAAE,CAAA;QACjB,CAAC;IACL,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,MAAmB;QAC9B,IAAI,IAAI,GAAG,MAAM,CAAC,IAAI,CAAA;QAEtB,8GAA8G;QAC9G,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,CAAC;YAC/B,IAAI,IAAI,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,GAAG,CAAA;QAC/C,CAAC;aAAM,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;YACtB,IAAI,IAAI,IAAI,MAAM,CAAC,KAAK,GAAG,CAAA;QAC/B,CAAC;aAAM,IACH,MAAM,CAAC,SAAS,KAAK,IAAI;YACzB,MAAM,CAAC,SAAS,KAAK,SAAS;YAC9B,MAAM,CAAC,KAAK,KAAK,IAAI;YACrB,MAAM,CAAC,KAAK,KAAK,SAAS,EAC5B,CAAC;YACC,IAAI,IAAI,IAAI,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,KAAK,GAAG,CAAA;QACnD,CAAC;aAAM,IACH,MAAM,CAAC,SAAS,KAAK,IAAI;YACzB,MAAM,CAAC,SAAS,KAAK,SAAS,EAChC,CAAC;YACC,IAAI,IAAI,IAAI,MAAM,CAAC,SAAS,GAAG,CAAA;QACnC,CAAC;QAED,IAAI,MAAM,CAAC,OAAO;YAAE,IAAI,GAAG,SAAS,IAAI,GAAG,CAAA;QAE3C,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;;;OAIG;IACH,sBAAsB;QAClB,OAAO,IAAI,CAAC,gBAAgB,CAAA;IAChC,CAAC;IAED;;;;OAIG;IACH,qBAAqB;QACjB,OAAO,IAAI,CAAC,gBAAgB,CAAA;IAChC,CAAC;IAED;;OAEG;IACH,kBAAkB,CACd,QAAwB,EACxB,YAAiB,EACjB,WAAmB;QAEnB,IAAI,CAAC,YAAY,EAAE,CAAC;YAChB,OAAO,SAAS,CAAA;QACpB,CAAC;QAED,IAAI,YAAY,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YACtC,OAAO,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;gBACjD,MAAM,MAAM,GAAG,QAAQ,CAAC,0BAA0B,CAAC,GAAG,CAAC,CAAA;gBACvD,IAAI,MAAM,EAAE,CAAC;oBACT,QAAQ,CAAC,SAAS,CACd,GAAG,EACH,MAAM,CAAC,cAAc,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAC3C,CAAA;oBACD,8KAA8K;gBAClL,CAAC;gBACD,OAAO,GAAG,CAAA;YACd,CAAC,EAAE,EAAmB,CAAC,CAAA;QAC3B,CAAC;QAED,MAAM,YAAY,GAAG,QAAQ,CAAC,gBAAgB,CAAC,MAAM,CACjD,CAAC,GAAG,EAAE,eAAe,EAAE,EAAE;YACrB,IAAI,KAAU,CAAA;YACd,IACI,eAAe,CAAC,kBAAkB,KAAK,WAAW;gBAClD,YAAY,CAAC,QAAQ,EACvB,CAAC;gBACC,qEAAqE;gBACrE,qEAAqE;gBACrE,KAAK,GAAG,YAAY,CAAC,QAAQ,GAAG,WAAW,CAAA;gBAC3C,8DAA8D;gBAC9D,sEAAsE;gBACtE,uDAAuD;YAC3D,CAAC;YAED,OAAO,QAAQ,CAAC,SAAS,CACrB,GAAG,EACH,eAAe,CAAC,cAAc,CAAC,KAAK,CAAC,CACxC,CAAA;QACL,CAAC,EACD,EAAmB,CACtB,CAAA;QAED,OAAO,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS,CAAA;IAC1E,CAAC;IAED;;;OAGG;IACH,kBAAkB,CACd,YAA2B,EAC3B,eAAiC;QAEjC,OAAO,eAAe,CAAC,MAAM,CAAC,CAAC,cAAc,EAAE,EAAE;YAC7C,MAAM,WAAW,GAAG,YAAY,CAAC,IAAI,CACjC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,cAAc,CAAC,YAAY,CAChD,CAAA;YACD,IAAI,CAAC,WAAW;gBAAE,OAAO,KAAK,CAAA,CAAC,4DAA4D;YAE3F,MAAM,eAAe,GACjB,WAAW,CAAC,IAAI,KAAK,cAAc,CAAC,YAAY;gBAChD,WAAW,CAAC,IAAI,KAAK,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC;gBACvD,WAAW,CAAC,MAAM,KAAK,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC;gBAC3D,WAAW,CAAC,YAAY,KAAK,cAAc,CAAC,YAAY;gBACxD,WAAW,CAAC,aAAa,KAAK,cAAc,CAAC,aAAa;gBAC1D,WAAW,CAAC,SAAS,KAAK,cAAc,CAAC,SAAS;gBAClD,CAAC,IAAI,CAAC,qBAAqB,CAAC,cAAc,EAAE,WAAW,CAAC;gBACxD,WAAW,CAAC,QAAQ,KAAK,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAA;YAEnE,gBAAgB;YAChB,yBAAyB;YACzB,qEAAqE;YACrE,mBAAmB;YACnB,mBAAmB;YACnB,4BAA4B;YAC5B,uCAAuC;YACvC,QAAQ;YACR,mBAAmB;YACnB,mBAAmB;YACnB,4BAA4B;YAC5B,8CAA8C;YAC9C,QAAQ;YACR,mBAAmB;YACnB,qBAAqB;YACrB,8BAA8B;YAC9B,gDAAgD;YAChD,QAAQ;YACR,mBAAmB;YACnB,2BAA2B;YAC3B,oCAAoC;YACpC,uCAAuC;YACvC,QAAQ;YACR,mBAAmB;YACnB,4BAA4B;YAC5B,qCAAqC;YACrC,wCAAwC;YACxC,QAAQ;YACR,mBAAmB;YACnB,wBAAwB;YACxB,iCAAiC;YACjC,oCAAoC;YACpC,QAAQ;YACR,mBAAmB;YACnB,yBAAyB;YACzB,kCAAkC;YAClC,qCAAqC;YACrC,QAAQ;YACR,mBAAmB;YACnB,uBAAuB;YACvB,gCAAgC;YAChC,kDAAkD;YAClD,QAAQ;YACR,gEAAgE;YAChE,IAAI;YAEJ,OAAO,eAAe,CAAA;QAC1B,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACH,uBAAuB;QACnB,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;OAEG;IACH,yBAAyB;QACrB,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;OAEG;IACH,6BAA6B;QACzB,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,aAAqB,EAAE,KAAa;QAChD,OAAO,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAA;IACxC,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;OAEG;IACO,gBAAgB;QACtB,IAAI,CAAC;YACD,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;YAEhE,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;gBAC3B,IAAI,CAAC,OAAO,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC;oBAC3B,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS;oBACjC,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW;iBACxC,CAAC,CAAA;gBAEF,OAAM;YACV,CAAC;YAED,IAAI,CAAC,OAAO,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC;gBAC3B,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS;aACpC,CAAC,CAAA;QACN,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACT,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;YAChB,MAAM,IAAI,8BAA8B,CACpC,SAAS,EACT,uBAAuB,CAC1B,CAAA;QACL,CAAC;IACL,CAAC;IAED,qBAAqB,CACjB,cAA8B,EAC9B,WAAwB;QAExB,2EAA2E;QAC3E,IAAI,cAAc,CAAC,aAAa,EAAE,CAAC;YAC/B,OAAO,IAAI,CAAA;QACf,CAAC;QAED,OAAO,cAAc,CAAC,UAAU,KAAK,WAAW,CAAC,UAAU,CAAA;IAC/D,CAAC;IAED;;OAEG;IACO,oBAAoB,CAC1B,mBAAuC,EACvC,aAAiC;QAEjC,IACI,OAAO,mBAAmB,KAAK,QAAQ;YACvC,OAAO,aAAa,KAAK,QAAQ,EACnC,CAAC;YACC,qGAAqG;YACrG,0EAA0E;YAC1E,mBAAmB,GAAG,mBAAmB,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAA;YACjE,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAA;QACzD,CAAC;QAED,OAAO,mBAAmB,KAAK,aAAa,CAAA;IAChD,CAAC;IAED;;;OAGG;IACO,yBAAyB,CAAC,KAAc;QAC9C,IAAI,CAAC,KAAK;YAAE,OAAO,KAAK,CAAA;QAExB,sCAAsC;QACtC,MAAM,kBAAkB,GACpB,KAAK,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;YACvD,KAAK,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAA;QAE7C,IAAI,kBAAkB,EAAE,CAAC;YACrB,gCAAgC;YAChC,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;YACxC,OAAO,SAAS;gBACZ,CAAC,CAAC,oBAAoB,SAAS,CAAC,CAAC,CAAC,EAAE;gBACpC,CAAC,CAAC,mBAAmB,CAAA;QAC7B,CAAC;aAAM,CAAC;YACJ,OAAO,KAAK,CAAA;QAChB,CAAC;IACL,CAAC;IAED;;OAEG;IACO,aAAa,CAAC,OAAgB;QACpC,IAAI,CAAC,OAAO;YAAE,OAAO,OAAO,CAAA;QAE5B,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAA,CAAC,wCAAwC;QAEjF,OAAO,OAAO,CAAA;IAClB,CAAC;CACJ", "file": "SpannerDriver.js", "sourcesContent": ["import { Driver } from \"../Driver\"\nimport { DriverPackageNotInstalledError } from \"../../error/DriverPackageNotInstalledError\"\nimport { SpannerQueryRunner } from \"./SpannerQueryRunner\"\nimport { ObjectLiteral } from \"../../common/ObjectLiteral\"\nimport { ColumnMetadata } from \"../../metadata/ColumnMetadata\"\nimport { DateUtils } from \"../../util/DateUtils\"\nimport { PlatformTools } from \"../../platform/PlatformTools\"\nimport { Connection } from \"../../connection/Connection\"\nimport { RdbmsSchemaBuilder } from \"../../schema-builder/RdbmsSchemaBuilder\"\nimport { SpannerConnectionOptions } from \"./SpannerConnectionOptions\"\nimport { MappedColumnTypes } from \"../types/MappedColumnTypes\"\nimport { ColumnType } from \"../types/ColumnTypes\"\nimport { DataTypeDefaults } from \"../types/DataTypeDefaults\"\nimport { TableColumn } from \"../../schema-builder/table/TableColumn\"\nimport { EntityMetadata } from \"../../metadata/EntityMetadata\"\nimport { OrmUtils } from \"../../util/OrmUtils\"\nimport { ApplyValueTransformers } from \"../../util/ApplyValueTransformers\"\nimport { ReplicationMode } from \"../types/ReplicationMode\"\nimport { Table } from \"../../schema-builder/table/Table\"\nimport { View } from \"../../schema-builder/view/View\"\nimport { TableForeignKey } from \"../../schema-builder/table/TableForeignKey\"\nimport { CteCapabilities } from \"../types/CteCapabilities\"\nimport { UpsertType } from \"../types/UpsertType\"\n\n/**\n * Organizes communication with Spanner DBMS.\n */\nexport class SpannerDriver implements Driver {\n    // -------------------------------------------------------------------------\n    // Public Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Connection used by driver.\n     */\n    connection: Connection\n\n    /**\n     * Cloud Spanner underlying library.\n     */\n    spanner: any\n\n    /**\n     * Cloud Spanner instance.\n     */\n    instance: any\n\n    /**\n     * Cloud Spanner database.\n     */\n    instanceDatabase: any\n\n    /**\n     * Database name.\n     */\n    database?: string\n\n    // -------------------------------------------------------------------------\n    // Public Implemented Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Connection options.\n     */\n    options: SpannerConnectionOptions\n\n    /**\n     * Indicates if replication is enabled.\n     */\n    isReplicated: boolean = false\n\n    /**\n     * Indicates if tree tables are supported by this driver.\n     */\n    treeSupport = true\n\n    /**\n     * Represent transaction support by this driver\n     */\n    transactionSupport = \"none\" as const\n\n    /**\n     * Gets list of supported column data types by a driver.\n     *\n     * @see https://cloud.google.com/spanner/docs/reference/standard-sql/data-types\n     */\n    supportedDataTypes: ColumnType[] = [\n        \"bool\",\n        \"int64\",\n        \"float64\",\n        \"numeric\",\n        \"string\",\n        \"json\",\n        \"bytes\",\n        \"date\",\n        \"timestamp\",\n        \"array\",\n    ]\n\n    /**\n     * Returns type of upsert supported by driver if any\n     */\n    supportedUpsertTypes: UpsertType[] = []\n\n    /**\n     * Gets list of spatial column data types.\n     */\n    spatialTypes: ColumnType[] = []\n\n    /**\n     * Gets list of column data types that support length by a driver.\n     */\n    withLengthColumnTypes: ColumnType[] = [\"string\", \"bytes\"]\n\n    /**\n     * Gets list of column data types that support length by a driver.\n     */\n    withWidthColumnTypes: ColumnType[] = []\n\n    /**\n     * Gets list of column data types that support precision by a driver.\n     */\n    withPrecisionColumnTypes: ColumnType[] = []\n\n    /**\n     * Gets list of column data types that supports scale by a driver.\n     */\n    withScaleColumnTypes: ColumnType[] = []\n\n    /**\n     * ORM has special columns and we need to know what database column types should be for those columns.\n     * Column types are driver dependant.\n     */\n    mappedDataTypes: MappedColumnTypes = {\n        createDate: \"timestamp\",\n        createDateDefault: \"\",\n        updateDate: \"timestamp\",\n        updateDateDefault: \"\",\n        deleteDate: \"timestamp\",\n        deleteDateNullable: true,\n        version: \"int64\",\n        treeLevel: \"int64\",\n        migrationId: \"int64\",\n        migrationName: \"string\",\n        migrationTimestamp: \"int64\",\n        cacheId: \"string\",\n        cacheIdentifier: \"string\",\n        cacheTime: \"int64\",\n        cacheDuration: \"int64\",\n        cacheQuery: \"string\",\n        cacheResult: \"string\",\n        metadataType: \"string\",\n        metadataDatabase: \"string\",\n        metadataSchema: \"string\",\n        metadataTable: \"string\",\n        metadataName: \"string\",\n        metadataValue: \"string\",\n    }\n\n    /**\n     * The prefix used for the parameters\n     */\n    parametersPrefix: string = \"@param\"\n\n    /**\n     * Default values of length, precision and scale depends on column data type.\n     * Used in the cases when length/precision/scale is not specified by user.\n     */\n    dataTypeDefaults: DataTypeDefaults = {}\n\n    /**\n     * Max length allowed by MySQL for aliases.\n     * @see https://dev.mysql.com/doc/refman/5.5/en/identifiers.html\n     */\n    maxAliasLength = 63\n\n    cteCapabilities: CteCapabilities = {\n        enabled: true,\n    }\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(connection: Connection) {\n        this.connection = connection\n        this.options = connection.options as SpannerConnectionOptions\n        this.isReplicated = this.options.replication ? true : false\n\n        // load mysql package\n        this.loadDependencies()\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Performs connection to the database.\n     */\n    async connect(): Promise<void> {\n        this.instance = this.spanner.instance(this.options.instanceId)\n        this.instanceDatabase = this.instance.database(this.options.databaseId)\n    }\n\n    /**\n     * Makes any action after connection (e.g. create extensions in Postgres driver).\n     */\n    afterConnect(): Promise<void> {\n        return Promise.resolve()\n    }\n\n    /**\n     * Closes connection with the database.\n     */\n    async disconnect(): Promise<void> {\n        this.instanceDatabase.close()\n    }\n\n    /**\n     * Creates a schema builder used to build and sync a schema.\n     */\n    createSchemaBuilder() {\n        return new RdbmsSchemaBuilder(this.connection)\n    }\n\n    /**\n     * Creates a query runner used to execute database queries.\n     */\n    createQueryRunner(mode: ReplicationMode) {\n        return new SpannerQueryRunner(this, mode)\n    }\n\n    /**\n     * Replaces parameters in the given sql with special escaping character\n     * and an array of parameter names to be passed to a query.\n     */\n    escapeQueryWithParameters(\n        sql: string,\n        parameters: ObjectLiteral,\n        nativeParameters: ObjectLiteral,\n    ): [string, any[]] {\n        const escapedParameters: any[] = Object.keys(nativeParameters).map(\n            (key) => nativeParameters[key],\n        )\n        if (!parameters || !Object.keys(parameters).length)\n            return [sql, escapedParameters]\n\n        const parameterIndexMap = new Map<string, number>()\n        sql = sql.replace(\n            /:(\\.\\.\\.)?([A-Za-z0-9_.]+)/g,\n            (full, isArray: string, key: string): string => {\n                if (!parameters.hasOwnProperty(key)) {\n                    return full\n                }\n\n                if (parameterIndexMap.has(key)) {\n                    return this.parametersPrefix + parameterIndexMap.get(key)\n                }\n\n                const value: any = parameters[key]\n\n                if (value === null) {\n                    return full\n                }\n\n                if (isArray) {\n                    return value\n                        .map((v: any) => {\n                            escapedParameters.push(v)\n                            return this.createParameter(\n                                key,\n                                escapedParameters.length - 1,\n                            )\n                        })\n                        .join(\", \")\n                }\n\n                if (value instanceof Function) {\n                    return value()\n                }\n\n                escapedParameters.push(value)\n                parameterIndexMap.set(key, escapedParameters.length - 1)\n                return this.createParameter(key, escapedParameters.length - 1)\n            },\n        ) // todo: make replace only in value statements, otherwise problems\n\n        sql = sql.replace(\n            /([ ]+)?=([ ]+)?:(\\.\\.\\.)?([A-Za-z0-9_.]+)/g,\n            (\n                full,\n                emptySpaceBefore: string,\n                emptySpaceAfter: string,\n                isArray: string,\n                key: string,\n            ): string => {\n                if (!parameters.hasOwnProperty(key)) {\n                    return full\n                }\n\n                const value: any = parameters[key]\n                if (value === null) {\n                    return \" IS NULL\"\n                }\n\n                return full\n            },\n        )\n        return [sql, escapedParameters]\n    }\n\n    /**\n     * Escapes a column name.\n     */\n    escape(columnName: string): string {\n        return `\\`${columnName}\\``\n    }\n\n    /**\n     * Build full table name with database name, schema name and table name.\n     * E.g. myDB.mySchema.myTable\n     */\n    buildTableName(\n        tableName: string,\n        schema?: string,\n        database?: string,\n    ): string {\n        const tablePath = [tableName]\n\n        if (database) {\n            tablePath.unshift(database)\n        }\n\n        return tablePath.join(\".\")\n    }\n\n    /**\n     * Parse a target table name or other types and return a normalized table definition.\n     */\n    parseTableName(\n        target: EntityMetadata | Table | View | TableForeignKey | string,\n    ): { database?: string; schema?: string; tableName: string } {\n        const driverDatabase = this.database\n        const driverSchema: any = undefined\n\n        if (target instanceof Table || target instanceof View) {\n            const parsed = this.parseTableName(target.name)\n\n            return {\n                database: target.database || parsed.database || driverDatabase,\n                schema: target.schema || parsed.schema || driverSchema,\n                tableName: parsed.tableName,\n            }\n        }\n\n        if (target instanceof TableForeignKey) {\n            const parsed = this.parseTableName(target.referencedTableName)\n\n            return {\n                database:\n                    target.referencedDatabase ||\n                    parsed.database ||\n                    driverDatabase,\n                schema:\n                    target.referencedSchema || parsed.schema || driverSchema,\n                tableName: parsed.tableName,\n            }\n        }\n\n        if (target instanceof EntityMetadata) {\n            // EntityMetadata tableName is never a path\n\n            return {\n                database: target.database || driverDatabase,\n                schema: target.schema || driverSchema,\n                tableName: target.tableName,\n            }\n        }\n\n        const parts = target.split(\".\")\n\n        return {\n            database:\n                (parts.length > 1 ? parts[0] : undefined) || driverDatabase,\n            schema: driverSchema,\n            tableName: parts.length > 1 ? parts[1] : parts[0],\n        }\n    }\n\n    /**\n     * Prepares given value to a value to be persisted, based on its column type and metadata.\n     */\n    preparePersistentValue(value: any, columnMetadata: ColumnMetadata): any {\n        if (columnMetadata.transformer)\n            value = ApplyValueTransformers.transformTo(\n                columnMetadata.transformer,\n                value,\n            )\n\n        if (value === null || value === undefined) return value\n\n        if (columnMetadata.type === \"numeric\") {\n            const lib = this.options.driver || PlatformTools.load(\"spanner\")\n            return lib.Spanner.numeric(value.toString())\n        } else if (columnMetadata.type === \"date\") {\n            return DateUtils.mixedDateToDateString(value)\n        } else if (columnMetadata.type === \"json\") {\n            return value\n        } else if (\n            columnMetadata.type === \"timestamp\" ||\n            columnMetadata.type === Date\n        ) {\n            return DateUtils.mixedDateToDate(value)\n        }\n\n        return value\n    }\n\n    /**\n     * Prepares given value to a value to be persisted, based on its column type or metadata.\n     */\n    prepareHydratedValue(value: any, columnMetadata: ColumnMetadata): any {\n        if (value === null || value === undefined)\n            return columnMetadata.transformer\n                ? ApplyValueTransformers.transformFrom(\n                      columnMetadata.transformer,\n                      value,\n                  )\n                : value\n\n        if (columnMetadata.type === Boolean || columnMetadata.type === \"bool\") {\n            value = value ? true : false\n        } else if (\n            columnMetadata.type === \"timestamp\" ||\n            columnMetadata.type === Date\n        ) {\n            value = new Date(value)\n        } else if (columnMetadata.type === \"numeric\") {\n            value = value.value\n        } else if (columnMetadata.type === \"date\") {\n            value = DateUtils.mixedDateToDateString(value)\n        } else if (columnMetadata.type === \"json\") {\n            value = typeof value === \"string\" ? JSON.parse(value) : value\n        } else if (columnMetadata.type === Number) {\n            // convert to number if number\n            value = !isNaN(+value) ? parseInt(value) : value\n        }\n\n        if (columnMetadata.transformer)\n            value = ApplyValueTransformers.transformFrom(\n                columnMetadata.transformer,\n                value,\n            )\n\n        return value\n    }\n\n    /**\n     * Creates a database type from a given column metadata.\n     */\n    normalizeType(column: {\n        type: ColumnType\n        length?: number | string\n        precision?: number | null\n        scale?: number\n    }): string {\n        if (column.type === Number) {\n            return \"int64\"\n        } else if (column.type === String || column.type === \"uuid\") {\n            return \"string\"\n        } else if (column.type === Date) {\n            return \"timestamp\"\n        } else if ((column.type as any) === Buffer) {\n            return \"bytes\"\n        } else if (column.type === Boolean) {\n            return \"bool\"\n        } else {\n            return (column.type as string) || \"\"\n        }\n    }\n\n    /**\n     * Normalizes \"default\" value of the column.\n     *\n     * Spanner does not support default values.\n     */\n    normalizeDefault(columnMetadata: ColumnMetadata): string | undefined {\n        return columnMetadata.default === \"\"\n            ? `\"${columnMetadata.default}\"`\n            : `${columnMetadata.default}`\n    }\n\n    /**\n     * Normalizes \"isUnique\" value of the column.\n     */\n    normalizeIsUnique(column: ColumnMetadata): boolean {\n        return column.entityMetadata.indices.some(\n            (idx) =>\n                idx.isUnique &&\n                idx.columns.length === 1 &&\n                idx.columns[0] === column,\n        )\n    }\n\n    /**\n     * Returns default column lengths, which is required on column creation.\n     */\n    getColumnLength(column: ColumnMetadata | TableColumn): string {\n        if (column.length) return column.length.toString()\n        if (column.generationStrategy === \"uuid\") return \"36\"\n\n        switch (column.type) {\n            case String:\n            case \"string\":\n            case \"bytes\":\n                return \"max\"\n            default:\n                return \"\"\n        }\n    }\n\n    /**\n     * Creates column type definition including length, precision and scale\n     */\n    createFullType(column: TableColumn): string {\n        let type = column.type\n\n        // used 'getColumnLength()' method, because Spanner requires column length for `string` and `bytes` data types\n        if (this.getColumnLength(column)) {\n            type += `(${this.getColumnLength(column)})`\n        } else if (column.width) {\n            type += `(${column.width})`\n        } else if (\n            column.precision !== null &&\n            column.precision !== undefined &&\n            column.scale !== null &&\n            column.scale !== undefined\n        ) {\n            type += `(${column.precision},${column.scale})`\n        } else if (\n            column.precision !== null &&\n            column.precision !== undefined\n        ) {\n            type += `(${column.precision})`\n        }\n\n        if (column.isArray) type = `array<${type}>`\n\n        return type\n    }\n\n    /**\n     * Obtains a new database connection to a master server.\n     * Used for replication.\n     * If replication is not setup then returns default connection's database connection.\n     */\n    obtainMasterConnection(): Promise<any> {\n        return this.instanceDatabase\n    }\n\n    /**\n     * Obtains a new database connection to a slave server.\n     * Used for replication.\n     * If replication is not setup then returns master (default) connection's database connection.\n     */\n    obtainSlaveConnection(): Promise<any> {\n        return this.instanceDatabase\n    }\n\n    /**\n     * Creates generated map of values generated or returned by database after INSERT query.\n     */\n    createGeneratedMap(\n        metadata: EntityMetadata,\n        insertResult: any,\n        entityIndex: number,\n    ) {\n        if (!insertResult) {\n            return undefined\n        }\n\n        if (insertResult.insertId === undefined) {\n            return Object.keys(insertResult).reduce((map, key) => {\n                const column = metadata.findColumnWithDatabaseName(key)\n                if (column) {\n                    OrmUtils.mergeDeep(\n                        map,\n                        column.createValueMap(insertResult[key]),\n                    )\n                    // OrmUtils.mergeDeep(map, column.createValueMap(this.prepareHydratedValue(insertResult[key], column))); // TODO: probably should be like there, but fails on enums, fix later\n                }\n                return map\n            }, {} as ObjectLiteral)\n        }\n\n        const generatedMap = metadata.generatedColumns.reduce(\n            (map, generatedColumn) => {\n                let value: any\n                if (\n                    generatedColumn.generationStrategy === \"increment\" &&\n                    insertResult.insertId\n                ) {\n                    // NOTE: When multiple rows is inserted by a single INSERT statement,\n                    // `insertId` is the value generated for the first inserted row only.\n                    value = insertResult.insertId + entityIndex\n                    // } else if (generatedColumn.generationStrategy === \"uuid\") {\n                    //     console.log(\"getting db value:\", generatedColumn.databaseName);\n                    //     value = generatedColumn.getEntityValue(uuidMap);\n                }\n\n                return OrmUtils.mergeDeep(\n                    map,\n                    generatedColumn.createValueMap(value),\n                )\n            },\n            {} as ObjectLiteral,\n        )\n\n        return Object.keys(generatedMap).length > 0 ? generatedMap : undefined\n    }\n\n    /**\n     * Differentiate columns of this table and columns from the given column metadatas columns\n     * and returns only changed.\n     */\n    findChangedColumns(\n        tableColumns: TableColumn[],\n        columnMetadatas: ColumnMetadata[],\n    ): ColumnMetadata[] {\n        return columnMetadatas.filter((columnMetadata) => {\n            const tableColumn = tableColumns.find(\n                (c) => c.name === columnMetadata.databaseName,\n            )\n            if (!tableColumn) return false // we don't need new columns, we only need exist and changed\n\n            const isColumnChanged =\n                tableColumn.name !== columnMetadata.databaseName ||\n                tableColumn.type !== this.normalizeType(columnMetadata) ||\n                tableColumn.length !== this.getColumnLength(columnMetadata) ||\n                tableColumn.asExpression !== columnMetadata.asExpression ||\n                tableColumn.generatedType !== columnMetadata.generatedType ||\n                tableColumn.isPrimary !== columnMetadata.isPrimary ||\n                !this.compareNullableValues(columnMetadata, tableColumn) ||\n                tableColumn.isUnique !== this.normalizeIsUnique(columnMetadata)\n\n            // DEBUG SECTION\n            // if (isColumnChanged) {\n            //     console.log(\"table:\", columnMetadata.entityMetadata.tableName)\n            //     console.log(\n            //         \"name:\",\n            //         tableColumn.name,\n            //         columnMetadata.databaseName,\n            //     )\n            //     console.log(\n            //         \"type:\",\n            //         tableColumn.type,\n            //         this.normalizeType(columnMetadata),\n            //     )\n            //     console.log(\n            //         \"length:\",\n            //         tableColumn.length,\n            //         this.getColumnLength(columnMetadata),\n            //     )\n            //     console.log(\n            //         \"asExpression:\",\n            //         tableColumn.asExpression,\n            //         columnMetadata.asExpression,\n            //     )\n            //     console.log(\n            //         \"generatedType:\",\n            //         tableColumn.generatedType,\n            //         columnMetadata.generatedType,\n            //     )\n            //     console.log(\n            //         \"isPrimary:\",\n            //         tableColumn.isPrimary,\n            //         columnMetadata.isPrimary,\n            //     )\n            //     console.log(\n            //         \"isNullable:\",\n            //         tableColumn.isNullable,\n            //         columnMetadata.isNullable,\n            //     )\n            //     console.log(\n            //         \"isUnique:\",\n            //         tableColumn.isUnique,\n            //         this.normalizeIsUnique(columnMetadata),\n            //     )\n            //     console.log(\"==========================================\")\n            // }\n\n            return isColumnChanged\n        })\n    }\n\n    /**\n     * Returns true if driver supports RETURNING / OUTPUT statement.\n     */\n    isReturningSqlSupported(): boolean {\n        return true\n    }\n\n    /**\n     * Returns true if driver supports uuid values generation on its own.\n     */\n    isUUIDGenerationSupported(): boolean {\n        return true\n    }\n\n    /**\n     * Returns true if driver supports fulltext indices.\n     */\n    isFullTextColumnTypeSupported(): boolean {\n        return false\n    }\n\n    /**\n     * Creates an escaped parameter.\n     */\n    createParameter(parameterName: string, index: number): string {\n        return this.parametersPrefix + index\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Loads all driver dependencies.\n     */\n    protected loadDependencies(): void {\n        try {\n            const lib = this.options.driver || PlatformTools.load(\"spanner\")\n\n            if (this.options.credentials) {\n                this.spanner = new lib.Spanner({\n                    projectId: this.options.projectId,\n                    credentials: this.options.credentials,\n                })\n\n                return\n            }\n\n            this.spanner = new lib.Spanner({\n                projectId: this.options.projectId,\n            })\n        } catch (e) {\n            console.error(e)\n            throw new DriverPackageNotInstalledError(\n                \"Spanner\",\n                \"@google-cloud/spanner\",\n            )\n        }\n    }\n\n    compareNullableValues(\n        columnMetadata: ColumnMetadata,\n        tableColumn: TableColumn,\n    ): boolean {\n        // Spanner does not support NULL/NOT NULL expressions for generated columns\n        if (columnMetadata.generatedType) {\n            return true\n        }\n\n        return columnMetadata.isNullable === tableColumn.isNullable\n    }\n\n    /**\n     * Checks if \"DEFAULT\" values in the column metadata and in the database are equal.\n     */\n    protected compareDefaultValues(\n        columnMetadataValue: string | undefined,\n        databaseValue: string | undefined,\n    ): boolean {\n        if (\n            typeof columnMetadataValue === \"string\" &&\n            typeof databaseValue === \"string\"\n        ) {\n            // we need to cut out \"'\" because in mysql we can understand returned value is a string or a function\n            // as result compare cannot understand if default is really changed or not\n            columnMetadataValue = columnMetadataValue.replace(/^'+|'+$/g, \"\")\n            databaseValue = databaseValue.replace(/^'+|'+$/g, \"\")\n        }\n\n        return columnMetadataValue === databaseValue\n    }\n\n    /**\n     * If parameter is a datetime function, e.g. \"CURRENT_TIMESTAMP\", normalizes it.\n     * Otherwise returns original input.\n     */\n    protected normalizeDatetimeFunction(value?: string) {\n        if (!value) return value\n\n        // check if input is datetime function\n        const isDatetimeFunction =\n            value.toUpperCase().indexOf(\"CURRENT_TIMESTAMP\") !== -1 ||\n            value.toUpperCase().indexOf(\"NOW\") !== -1\n\n        if (isDatetimeFunction) {\n            // extract precision, e.g. \"(3)\"\n            const precision = value.match(/\\(\\d+\\)/)\n            return precision\n                ? `CURRENT_TIMESTAMP${precision[0]}`\n                : \"CURRENT_TIMESTAMP\"\n        } else {\n            return value\n        }\n    }\n\n    /**\n     * Escapes a given comment.\n     */\n    protected escapeComment(comment?: string) {\n        if (!comment) return comment\n\n        comment = comment.replace(/\\u0000/g, \"\") // Null bytes aren't allowed in comments\n\n        return comment\n    }\n}\n"], "sourceRoot": "../.."}