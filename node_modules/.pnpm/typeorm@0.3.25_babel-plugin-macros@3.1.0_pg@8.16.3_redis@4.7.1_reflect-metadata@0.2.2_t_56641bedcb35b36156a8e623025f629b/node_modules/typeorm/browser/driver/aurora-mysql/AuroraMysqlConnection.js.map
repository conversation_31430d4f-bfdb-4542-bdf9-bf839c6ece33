{"version": 3, "sources": ["../browser/src/driver/aurora-mysql/AuroraMysqlConnection.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,UAAU,EAAE,MAAM,8BAA8B,CAAA;AAKzD;;GAEG;AACH,MAAM,OAAO,qBAAsB,SAAQ,UAAU;IAGjD,YACI,OAA0B,EAC1B,WAAmC;QAEnC,KAAK,CAAC,OAAO,CAAC,CAAA;QACd,IAAI,CAAC,WAAW,GAAG,WAAW,CAAA;IAClC,CAAC;IAEM,iBAAiB,CAAC,IAAqB;QAC1C,OAAO,IAAI,CAAC,WAAW,CAAA;IAC3B,CAAC;CACJ", "file": "AuroraMysqlConnection.js", "sourcesContent": ["import { AuroraMysqlQueryRunner } from \"./AuroraMysqlQueryRunner\"\nimport { DataSource } from \"../../data-source/DataSource\"\nimport { ReplicationMode } from \"../types/ReplicationMode\"\nimport { DataSourceOptions } from \"../../data-source\"\nimport { QueryRunner } from \"../../query-runner/QueryRunner\"\n\n/**\n * Organizes communication with MySQL DBMS.\n */\nexport class AuroraMysqlConnection extends DataSource {\n    queryRunner: AuroraMysqlQueryRunner\n\n    constructor(\n        options: DataSourceOptions,\n        queryRunner: AuroraMysqlQueryRunner,\n    ) {\n        super(options)\n        this.queryRunner = queryRunner\n    }\n\n    public createQueryRunner(mode: ReplicationMode): QueryRunner {\n        return this.queryRunner\n    }\n}\n"], "sourceRoot": "../.."}