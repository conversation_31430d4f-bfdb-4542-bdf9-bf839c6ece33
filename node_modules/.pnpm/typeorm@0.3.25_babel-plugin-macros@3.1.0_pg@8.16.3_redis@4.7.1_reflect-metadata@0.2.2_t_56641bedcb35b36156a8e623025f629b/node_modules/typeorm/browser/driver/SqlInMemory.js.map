{"version": 3, "sources": ["../browser/src/driver/SqlInMemory.ts"], "names": [], "mappings": "AAEA;;GAEG;AACH,MAAM,OAAO,WAAW;IAAxB;QACI,cAAS,GAAY,EAAE,CAAA;QACvB,gBAAW,GAAY,EAAE,CAAA;IAC7B,CAAC;CAAA", "file": "SqlInMemory.js", "sourcesContent": ["import { Query } from \"./Query\"\n\n/**\n * This class stores up and down queries needed for migrations functionality.\n */\nexport class SqlInMemory {\n    upQueries: Query[] = []\n    downQueries: Query[] = []\n}\n"], "sourceRoot": ".."}