{"version": 3, "sources": ["../browser/src/driver/sqlserver/authentication/NtlmAuthentication.ts"], "names": [], "mappings": "", "file": "NtlmAuthentication.js", "sourcesContent": ["export interface NtlmAuthentication {\n    type: \"ntlm\"\n    options: {\n        /**\n         * User name from your windows account.\n         */\n        userName: string\n        /**\n         * Password from your windows account.\n         */\n        password: string\n        /**\n         * Once you set domain for ntlm authentication type, driver will connect to SQL Server using domain login.\n         *\n         * This is necessary for forming a connection using ntlm type\n         */\n        domain: string\n    }\n}\n"], "sourceRoot": "../../.."}