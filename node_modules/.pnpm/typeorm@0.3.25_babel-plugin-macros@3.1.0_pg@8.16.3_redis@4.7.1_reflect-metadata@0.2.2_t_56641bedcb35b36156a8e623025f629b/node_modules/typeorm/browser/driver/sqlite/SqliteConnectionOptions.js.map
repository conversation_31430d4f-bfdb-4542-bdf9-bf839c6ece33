{"version": 3, "sources": ["../browser/src/driver/sqlite/SqliteConnectionOptions.ts"], "names": [], "mappings": "", "file": "SqliteConnectionOptions.js", "sourcesContent": ["import { BaseDataSourceOptions } from \"../../data-source/BaseDataSourceOptions\"\n\n/**\n * Sqlite-specific connection options.\n */\nexport interface SqliteConnectionOptions extends BaseDataSourceOptions {\n    /**\n     * Database type.\n     */\n    readonly type: \"sqlite\"\n\n    /**\n     * Storage type or path to the storage.\n     */\n    readonly database: string\n\n    /**\n     * The driver object\n     * This defaults to require(\"sqlite3\")\n     */\n    readonly driver?: any\n\n    /**\n     * Encryption key for for SQLCipher.\n     */\n    readonly key?: string\n\n    /**\n     * In your SQLite application when you perform parallel writes its common to face SQLITE_BUSY error.\n     * This error indicates that SQLite failed to write to the database file since someone else already writes into it.\n     * Since SQLite cannot handle parallel saves this error cannot be avoided.\n     *\n     * To simplify life's of those who have this error this particular option sets a timeout within which ORM will try\n     * to perform requested write operation again and again until it receives SQLITE_BUSY error.\n     *\n     * Enabling WAL can improve your app performance and face less SQLITE_BUSY issues.\n     * Time in milliseconds.\n     */\n    readonly busyErrorRetry?: number\n\n    /**\n     * Enables WAL mode. By default its disabled.\n     *\n     * @see https://www.sqlite.org/wal.html\n     */\n    readonly enableWAL?: boolean\n\n    /**\n     * Specifies the open file flags. By default its undefined.\n     * @see https://www.sqlite.org/c3ref/c_open_autoproxy.html\n     * @see https://github.com/TryGhost/node-sqlite3/blob/master/test/open_close.test.js\n     */\n    readonly flags?: number\n\n    readonly poolSize?: never\n\n    /**\n     * Query or change the setting of the busy timeout.\n     * Time in milliseconds.\n     *\n     * @see https://www.sqlite.org/pragma.html#pragma_busy_timeout\n     */\n    readonly busyTimeout?: number\n}\n"], "sourceRoot": "../.."}