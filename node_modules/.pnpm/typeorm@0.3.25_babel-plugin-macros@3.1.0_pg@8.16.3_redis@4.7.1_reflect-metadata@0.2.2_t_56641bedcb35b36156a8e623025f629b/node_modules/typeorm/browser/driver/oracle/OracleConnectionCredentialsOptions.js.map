{"version": 3, "sources": ["../browser/src/driver/oracle/OracleConnectionCredentialsOptions.ts"], "names": [], "mappings": "", "file": "OracleConnectionCredentialsOptions.js", "sourcesContent": ["/**\n * Oracle specific connection credential options.\n */\nexport interface OracleConnectionCredentialsOptions {\n    /**\n     * Connection url where the connection is performed.\n     */\n    readonly url?: string\n\n    /**\n     * Database host.\n     */\n    readonly host?: string\n\n    /**\n     * Database host port.\n     */\n    readonly port?: number\n\n    /**\n     * Database username.\n     */\n    readonly username?: string\n\n    /**\n     * Database password.\n     */\n    readonly password?: string\n\n    /**\n     * Database name to connect to.\n     */\n    readonly database?: string\n\n    /**\n     * Connection SID.\n     */\n    readonly sid?: string\n\n    /**\n     * Connection Service Name.\n     */\n    readonly serviceName?: string\n\n    /**\n     * Embedded TNS Connection String\n     */\n    readonly connectString?: string\n}\n"], "sourceRoot": "../.."}