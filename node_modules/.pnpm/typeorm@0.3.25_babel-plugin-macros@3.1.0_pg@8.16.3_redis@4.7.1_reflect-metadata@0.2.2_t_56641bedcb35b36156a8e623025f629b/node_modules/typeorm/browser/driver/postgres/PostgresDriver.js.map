{"version": 3, "sources": ["../browser/src/driver/postgres/PostgresDriver.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,uBAAuB,EAAE,MAAM,qCAAqC,CAAA;AAC7E,OAAO,EAAE,8BAA8B,EAAE,MAAM,4CAA4C,CAAA;AAG3F,OAAO,EAAE,aAAa,EAAE,MAAM,8BAA8B,CAAA;AAE5D,OAAO,EAAE,kBAAkB,EAAE,MAAM,yCAAyC,CAAA;AAE5E,OAAO,EAAE,sBAAsB,EAAE,MAAM,mCAAmC,CAAA;AAC1E,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAA;AAChD,OAAO,EAAE,QAAQ,EAAE,MAAM,qBAAqB,CAAA;AAO9C,OAAO,EAAE,YAAY,EAAE,MAAM,yBAAyB,CAAA;AAGtD,OAAO,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAA;AAC3D,OAAO,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAA;AAC5C,OAAO,EAAE,YAAY,EAAE,MAAM,aAAa,CAAA;AAI1C,OAAO,EAAE,eAAe,EAAE,MAAM,4BAA4B,CAAA;AAG5D;;GAEG;AACH,MAAM,OAAO,cAAc;IA0QvB,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAY,UAAuB;QA1PnC;;;WAGG;QACH,WAAM,GAAU,EAAE,CAAA;QAElB;;WAEG;QACH,0BAAqB,GAAkB,EAAE,CAAA;QAoCzC;;WAEG;QACH,iBAAY,GAAY,KAAK,CAAA;QAE7B;;WAEG;QACH,gBAAW,GAAG,IAAI,CAAA;QAElB;;WAEG;QACH,uBAAkB,GAAG,QAAiB,CAAA;QAEtC;;;;;WAKG;QACH,uBAAkB,GAAiB;YAC/B,KAAK;YACL,MAAM;YACN,MAAM;YACN,MAAM;YACN,UAAU;YACV,SAAS;YACT,QAAQ;YACR,SAAS;YACT,SAAS;YACT,MAAM;YACN,OAAO;YACP,QAAQ;YACR,QAAQ;YACR,kBAAkB;YAClB,OAAO;YACP,mBAAmB;YACnB,SAAS;YACT,WAAW;YACX,MAAM;YACN,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,OAAO;YACP,KAAK;YACL,QAAQ;YACR,aAAa;YACb,QAAQ;YACR,aAAa;YACb,WAAW;YACX,6BAA6B;YAC7B,0BAA0B;YAC1B,MAAM;YACN,MAAM;YACN,wBAAwB;YACxB,qBAAqB;YACrB,UAAU;YACV,MAAM;YACN,SAAS;YACT,MAAM;YACN,OAAO;YACP,MAAM;YACN,MAAM;YACN,KAAK;YACL,MAAM;YACN,SAAS;YACT,QAAQ;YACR,MAAM;YACN,MAAM;YACN,SAAS;YACT,UAAU;YACV,UAAU;YACV,SAAS;YACT,MAAM;YACN,KAAK;YACL,MAAM;YACN,OAAO;YACP,WAAW;YACX,WAAW;YACX,UAAU;YACV,SAAS;YACT,WAAW;YACX,WAAW;YACX,gBAAgB;YAChB,gBAAgB;YAChB,eAAe;YACf,cAAc;YACd,gBAAgB;YAChB,gBAAgB;YAChB,UAAU;YACV,WAAW;YACX,MAAM;YACN,OAAO;SACV,CAAA;QAED;;WAEG;QACH,yBAAoB,GAAiB,CAAC,uBAAuB,CAAC,CAAA;QAE9D;;WAEG;QACH,iBAAY,GAAiB,CAAC,UAAU,EAAE,WAAW,CAAC,CAAA;QAEtD;;WAEG;QACH,0BAAqB,GAAiB;YAClC,mBAAmB;YACnB,SAAS;YACT,WAAW;YACX,MAAM;YACN,KAAK;YACL,QAAQ;YACR,aAAa;SAChB,CAAA;QAED;;WAEG;QACH,6BAAwB,GAAiB;YACrC,SAAS;YACT,SAAS;YACT,UAAU;YACV,wBAAwB;YACxB,qBAAqB;YACrB,6BAA6B;YAC7B,0BAA0B;SAC7B,CAAA;QAED;;WAEG;QACH,yBAAoB,GAAiB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAA;QAE3D;;;WAGG;QACH,oBAAe,GAAsB;YACjC,UAAU,EAAE,WAAW;YACvB,iBAAiB,EAAE,OAAO;YAC1B,UAAU,EAAE,WAAW;YACvB,iBAAiB,EAAE,OAAO;YAC1B,UAAU,EAAE,WAAW;YACvB,kBAAkB,EAAE,IAAI;YACxB,OAAO,EAAE,MAAM;YACf,SAAS,EAAE,MAAM;YACjB,WAAW,EAAE,MAAM;YACnB,aAAa,EAAE,SAAS;YACxB,kBAAkB,EAAE,MAAM;YAC1B,OAAO,EAAE,MAAM;YACf,eAAe,EAAE,SAAS;YAC1B,SAAS,EAAE,MAAM;YACjB,aAAa,EAAE,MAAM;YACrB,UAAU,EAAE,MAAM;YAClB,WAAW,EAAE,MAAM;YACnB,YAAY,EAAE,SAAS;YACvB,gBAAgB,EAAE,SAAS;YAC3B,cAAc,EAAE,SAAS;YACzB,aAAa,EAAE,SAAS;YACxB,YAAY,EAAE,SAAS;YACvB,aAAa,EAAE,MAAM;SACxB,CAAA;QAED;;WAEG;QACH,qBAAgB,GAAW,GAAG,CAAA;QAE9B;;;WAGG;QACH,qBAAgB,GAAqB;YACjC,SAAS,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE;YACxB,GAAG,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE;YAClB,QAAQ,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE;YAC1B,wBAAwB,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE;YAC1C,qBAAqB,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE;YACvC,6BAA6B,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE;YAC/C,0BAA0B,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE;SAC/C,CAAA;QAED;;;WAGG;QACH,mBAAc,GAAG,EAAE,CAAA;QAEnB,gCAA2B,GAAY,KAAK,CAAA;QAE5C,oBAAe,GAAoB;YAC/B,OAAO,EAAE,IAAI;YACb,QAAQ,EAAE,IAAI;YACd,qBAAqB,EAAE,IAAI;YAC3B,gBAAgB,EAAE,IAAI;SACzB,CAAA;QAOG,IAAI,CAAC,UAAU,EAAE,CAAC;YACd,OAAM;QACV,CAAC;QAED,IAAI,CAAC,UAAU,GAAG,UAAU,CAAA;QAC5B,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,OAAoC,CAAA;QAC9D,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAA;QAC3D,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YACtB,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,KAAK,CAAA;QAC5B,CAAC;QACD,wBAAwB;QACxB,IAAI,CAAC,gBAAgB,EAAE,CAAA;QAEvB,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC,kBAAkB,CAC1C,IAAI,CAAC,OAAO,CAAC,WAAW;YACpB,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM;YACjC,CAAC,CAAC,IAAI,CAAC,OAAO,CACrB,CAAC,QAAQ,CAAA;QACV,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,CAAA;QAEjE,kHAAkH;QAClH,kDAAkD;QAClD,oDAAoD;QACpD,0BAA0B;QAC1B,iDAAiD;QACjD,8BAA8B;QAC9B,qDAAqD;QACrD,8BAA8B;QAC9B,qDAAqD;IACzD,CAAC;IAED,4EAA4E;IAC5E,6BAA6B;IAC7B,4EAA4E;IAE5E;;;;OAIG;IACH,KAAK,CAAC,OAAO;QACT,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;YAC3B,IAAI,CAAC,MAAM,GAAG,MAAM,OAAO,CAAC,GAAG,CAC3B,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;gBAC1C,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;YAC/C,CAAC,CAAC,CACL,CAAA;YACD,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAC/B,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAClC,CAAA;QACL,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;QACnE,CAAC;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAA;QAEpD,IAAI,CAAC,OAAO,GAAG,MAAM,WAAW,CAAC,UAAU,EAAE,CAAA;QAE7C,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACjB,IAAI,CAAC,QAAQ,GAAG,MAAM,WAAW,CAAC,kBAAkB,EAAE,CAAA;QAC1D,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACrB,IAAI,CAAC,YAAY,GAAG,MAAM,WAAW,CAAC,gBAAgB,EAAE,CAAA;QAC5D,CAAC;QAED,MAAM,WAAW,CAAC,OAAO,EAAE,CAAA;QAE3B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAA;QACnC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY;QACd,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,0BAA0B,EAAE,CAAA;QAClE,MAAM,CAAC,UAAU,EAAE,OAAO,CAAC,GAAG,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAA;QAEjE,MAAM,iBAAiB,GACnB,IAAI,CAAC,OAAO,CAAC,iBAAiB,KAAK,SAAS;YAC5C,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAA;QAClC,IAAI,iBAAiB,IAAI,kBAAkB,CAAC,aAAa,EAAE,CAAC;YACxD,MAAM,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,UAAU,CAAC,CAAA;QAC/D,CAAC;QAED,IAAI,CAAC,2BAA2B,GAAG,YAAY,CAAC,gBAAgB,CAC5D,IAAI,CAAC,OAAO,EACZ,MAAM,CACT,CAAA;QAED,MAAM,OAAO,EAAE,CAAA;IACnB,CAAC;IAES,KAAK,CAAC,gBAAgB,CAAC,kBAAuB,EAAE,UAAe;QACrE,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAA;QAElC,MAAM,EACF,cAAc,EACd,gBAAgB,EAChB,gBAAgB,EAChB,cAAc,EACd,kBAAkB,EAClB,eAAe,EACf,uBAAuB,GAC1B,GAAG,kBAAkB,CAAA;QAEtB,IAAI,cAAc;YACd,IAAI,CAAC;gBACD,MAAM,IAAI,CAAC,YAAY,CACnB,UAAU,EACV,mCACI,IAAI,CAAC,OAAO,CAAC,aAAa,IAAI,WAClC,GAAG,CACN,CAAA;YACL,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACT,MAAM,CAAC,GAAG,CACN,MAAM,EACN,0DACI,IAAI,CAAC,OAAO,CAAC,aAAa,IAAI,WAClC,qIAAqI,CACxI,CAAA;YACL,CAAC;QACL,IAAI,gBAAgB;YAChB,IAAI,CAAC;gBACD,MAAM,IAAI,CAAC,YAAY,CACnB,UAAU,EACV,yCAAyC,CAC5C,CAAA;YACL,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACT,MAAM,CAAC,GAAG,CACN,MAAM,EACN,iKAAiK,CACpK,CAAA;YACL,CAAC;QACL,IAAI,gBAAgB;YAChB,IAAI,CAAC;gBACD,MAAM,IAAI,CAAC,YAAY,CACnB,UAAU,EACV,yCAAyC,CAC5C,CAAA;YACL,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACT,MAAM,CAAC,GAAG,CACN,MAAM,EACN,iKAAiK,CACpK,CAAA;YACL,CAAC;QACL,IAAI,kBAAkB;YAClB,IAAI,CAAC;gBACD,MAAM,IAAI,CAAC,YAAY,CACnB,UAAU,EACV,0CAA0C,CAC7C,CAAA;YACL,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACT,MAAM,CAAC,GAAG,CACN,MAAM,EACN,sKAAsK,CACzK,CAAA;YACL,CAAC;QACL,IAAI,cAAc;YACd,IAAI,CAAC;gBACD,MAAM,IAAI,CAAC,YAAY,CACnB,UAAU,EACV,uCAAuC,CAC1C,CAAA;YACL,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACT,MAAM,CAAC,GAAG,CACN,MAAM,EACN,+JAA+J,CAClK,CAAA;YACL,CAAC;QACL,IAAI,eAAe;YACf,IAAI,CAAC;gBACD,MAAM,IAAI,CAAC,YAAY,CACnB,UAAU,EACV,wCAAwC,CAC3C,CAAA;YACL,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACT,MAAM,CAAC,GAAG,CACN,MAAM,EACN,iKAAiK,CACpK,CAAA;YACL,CAAC;QACL,IAAI,uBAAuB;YACvB,IAAI,CAAC;gBACD,yFAAyF;gBACzF,MAAM,IAAI,CAAC,YAAY,CACnB,UAAU,EACV,6CAA6C,CAChD,CAAA;YACL,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACT,MAAM,CAAC,GAAG,CACN,MAAM,EACN,+KAA+K,CAClL,CAAA;YACL,CAAC;IACT,CAAC;IAES,KAAK,CAAC,0BAA0B;QACtC,MAAM,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,IAAI,CACvD,CAAC,QAAQ,EAAE,EAAE;YACT,OAAO,CACH,QAAQ,CAAC,gBAAgB,CAAC,MAAM,CAC5B,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,kBAAkB,KAAK,MAAM,CACnD,CAAC,MAAM,GAAG,CAAC,CACf,CAAA;QACL,CAAC,CACJ,CAAA;QACD,MAAM,gBAAgB,GAAG,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,IAAI,CACzD,CAAC,QAAQ,EAAE,EAAE;YACT,OAAO,CACH,QAAQ,CAAC,OAAO,CAAC,MAAM,CACnB,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,QAAQ,CACvC,CAAC,MAAM,GAAG,CAAC,CACf,CAAA;QACL,CAAC,CACJ,CAAA;QACD,MAAM,gBAAgB,GAAG,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,IAAI,CACzD,CAAC,QAAQ,EAAE,EAAE;YACT,OAAO,CACH,QAAQ,CAAC,OAAO,CAAC,MAAM,CACnB,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,QAAQ,CACvC,CAAC,MAAM,GAAG,CAAC,CACf,CAAA;QACL,CAAC,CACJ,CAAA;QACD,MAAM,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,IAAI,CACvD,CAAC,QAAQ,EAAE,EAAE;YACT,OAAO,CACH,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC;iBACtD,MAAM,GAAG,CAAC,CAClB,CAAA;QACL,CAAC,CACJ,CAAA;QACD,MAAM,kBAAkB,GAAG,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,IAAI,CAC3D,CAAC,QAAQ,EAAE,EAAE;YACT,OAAO,CACH,QAAQ,CAAC,OAAO,CAAC,MAAM,CACnB,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAC1D,CAAC,MAAM,GAAG,CAAC,CACf,CAAA;QACL,CAAC,CACJ,CAAA;QACD,MAAM,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,IAAI,CACxD,CAAC,QAAQ,EAAE,EAAE;YACT,OAAO,CACH,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,OAAO,CAAC;iBACvD,MAAM,GAAG,CAAC,CAClB,CAAA;QACL,CAAC,CACJ,CAAA;QACD,MAAM,uBAAuB,GAAG,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,IAAI,CAChE,CAAC,QAAQ,EAAE,EAAE;YACT,OAAO,QAAQ,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAA;QACzC,CAAC,CACJ,CAAA;QAED,OAAO;YACH,cAAc;YACd,gBAAgB;YAChB,gBAAgB;YAChB,cAAc;YACd,kBAAkB;YAClB,eAAe;YACf,uBAAuB;YACvB,aAAa,EACT,cAAc;gBACd,gBAAgB;gBAChB,gBAAgB;gBAChB,kBAAkB;gBAClB,cAAc;gBACd,eAAe;gBACf,uBAAuB;SAC9B,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACZ,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACf,MAAM,IAAI,uBAAuB,CAAC,UAAU,CAAC,CAAA;QACjD,CAAC;QAED,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QACjC,MAAM,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;QACpE,IAAI,CAAC,MAAM,GAAG,SAAS,CAAA;QACvB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAA;IACpB,CAAC;IAED;;OAEG;IACH,mBAAmB;QACf,OAAO,IAAI,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;IAClD,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,IAAqB;QACnC,OAAO,IAAI,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;IAC9C,CAAC;IAED;;OAEG;IACH,sBAAsB,CAAC,KAAU,EAAE,cAA8B;QAC7D,IAAI,cAAc,CAAC,WAAW;YAC1B,KAAK,GAAG,sBAAsB,CAAC,WAAW,CACtC,cAAc,CAAC,WAAW,EAC1B,KAAK,CACR,CAAA;QAEL,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS;YAAE,OAAO,KAAK,CAAA;QAEvD,IAAI,cAAc,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YAClC,OAAO,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QACjC,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACxC,OAAO,SAAS,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAA;QACjD,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACxC,OAAO,SAAS,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAA;QACjD,CAAC;aAAM,IACH,cAAc,CAAC,IAAI,KAAK,UAAU;YAClC,cAAc,CAAC,IAAI,KAAK,IAAI;YAC5B,cAAc,CAAC,IAAI,KAAK,WAAW;YACnC,cAAc,CAAC,IAAI,KAAK,0BAA0B;YAClD,cAAc,CAAC,IAAI,KAAK,6BAA6B,EACvD,CAAC;YACC,OAAO,SAAS,CAAC,eAAe,CAAC,KAAK,CAAC,CAAA;QAC3C,CAAC;aAAM,IACH,CAAC,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,OAAO,CAC3C,cAAc,CAAC,IAAI,CACtB,IAAI,CAAC,EACR,CAAC;YACC,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;QAChC,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC1C,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBAC5B,OAAO,KAAK,CAAA;YAChB,CAAC;iBAAM,CAAC;gBACJ,kDAAkD;gBAClD,MAAM,WAAW,GAAG,CAAC,KAAc,EAAE,EAAE;oBACnC,wFAAwF;oBACxF,4DAA4D;oBAC5D,IAAI,KAAK,KAAK,IAAI,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE,CAAC;wBACjD,OAAO,MAAM,CAAA;oBACjB,CAAC;oBACD,6EAA6E;oBAC7E,0FAA0F;oBAC1F,OAAO,IAAI,GAAG,KAAK,EAAE,CAAC,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,GAAG,CAAA;gBACxD,CAAC,CAAA;gBACD,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;qBACpB,GAAG,CACA,CAAC,GAAG,EAAE,EAAE,CACJ,WAAW,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CACxD;qBACA,IAAI,CAAC,GAAG,CAAC,CAAA;YAClB,CAAC;QACL,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;YAChD,OAAO,SAAS,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAA;QAC/C,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;YAC/C,OAAO,SAAS,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAA;QAC9C,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACxC,IAAI,cAAc,CAAC,OAAO,EAAE,CAAC;gBACzB,OAAO,IAAI,KAAK;qBACX,GAAG,CAAC,CAAC,IAAc,EAAE,EAAE,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;qBAChD,IAAI,CAAC,GAAG,CAAC,GAAG,CAAA;YACrB,CAAC;YACD,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAA;QACjC,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YACzC,OAAO,KAAK;iBACP,KAAK,CAAC,GAAG,CAAC;iBACV,MAAM,CAAC,OAAO,CAAC;iBACf,IAAI,CAAC,GAAG,CAAC;iBACT,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAA;QAC/B,CAAC;aAAM,IACH,CAAC,cAAc,CAAC,IAAI,KAAK,MAAM;YAC3B,cAAc,CAAC,IAAI,KAAK,aAAa,CAAC;YAC1C,CAAC,cAAc,CAAC,OAAO,EACzB,CAAC;YACC,OAAO,EAAE,GAAG,KAAK,CAAA;QACrB,CAAC;QAED,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,KAAU,EAAE,cAA8B;QAC3D,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS;YACrC,OAAO,cAAc,CAAC,WAAW;gBAC7B,CAAC,CAAC,sBAAsB,CAAC,aAAa,CAChC,cAAc,CAAC,WAAW,EAC1B,KAAK,CACR;gBACH,CAAC,CAAC,KAAK,CAAA;QAEf,IAAI,cAAc,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YAClC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAA;QAChC,CAAC;aAAM,IACH,cAAc,CAAC,IAAI,KAAK,UAAU;YAClC,cAAc,CAAC,IAAI,KAAK,IAAI;YAC5B,cAAc,CAAC,IAAI,KAAK,WAAW;YACnC,cAAc,CAAC,IAAI,KAAK,0BAA0B;YAClD,cAAc,CAAC,IAAI,KAAK,6BAA6B,EACvD,CAAC;YACC,KAAK,GAAG,SAAS,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAA;QAClD,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACxC,KAAK,GAAG,SAAS,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAA;QAClD,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACxC,KAAK,GAAG,SAAS,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAA;QAC9C,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC1C,IAAI,cAAc,CAAC,UAAU,KAAK,QAAQ,EAAE,CAAC;gBACzC,MAAM,cAAc,GAAG,CAAC,GAAW,EAAE,EAAE,CACnC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;gBACpC,MAAM,MAAM,GACR,2EAA2E,CAAA;gBAC/E,MAAM,MAAM,GAAkB,EAAE,CAC/B;gBAAA,GAAG,KAAK,EAAE,CAAC,OAAO,CACf,MAAM,EACN,CAAC,CAAC,EAAE,GAAG,EAAE,SAAS,EAAE,WAAW,EAAE,EAAE;oBAC/B,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;wBACnC,CAAC,CAAC,IAAI;wBACN,CAAC,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;oBACjC,OAAO,EAAE,CAAA;gBACb,CAAC,CACJ,CAAA;gBACD,KAAK,GAAG,MAAM,CAAA;YAClB,CAAC;QACL,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;YAChD,KAAK,GAAG,SAAS,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAA;QAChD,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;YAC/C,KAAK,GAAG,SAAS,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAA;QAC/C,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACxC,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAA,CAAC,oBAAoB;YAC1D,IAAI,cAAc,CAAC,OAAO,EAAE,CAAC;gBACzB;;;;;mBAKG;gBACH,MAAM,MAAM,GAAG,mCAAmC,CAAA;gBAClD,MAAM,mBAAmB,GAAG,KAAK,CAAA;gBAEjC,KAAK,GAAG,EAAE,CAAA;gBACV,IAAI,IAAI,GAA2B,IAAI,CAAA;gBACvC,6DAA6D;gBAC7D,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;oBACxD,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE,CAAC;wBACxB,KAAK,CAAC,IAAI,CACN,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CACjD,CAAA;oBACL,CAAC;yBAAM,CAAC;wBACJ,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;oBACzB,CAAC;gBACL,CAAC;YACL,CAAC;iBAAM,CAAC;gBACJ,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;YACxD,CAAC;QACL,CAAC;aAAM,IACH,cAAc,CAAC,IAAI,KAAK,MAAM;YAC9B,cAAc,CAAC,IAAI,KAAK,aAAa,EACvC,CAAC;YACC,IAAI,cAAc,CAAC,OAAO,EAAE,CAAC;gBACzB,IAAI,KAAK,KAAK,IAAI;oBAAE,OAAO,EAAE,CAAA;gBAE7B,8HAA8H;gBAC9H,KAAK,GAAI,KAAgB;qBACpB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;qBACZ,KAAK,CAAC,GAAG,CAAC;qBACV,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE;oBACT,4DAA4D;oBAC5D,IAAI,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC;wBACxC,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;oBAC1B,8CAA8C;oBAC9C,OAAO,GAAG,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC,CAAA;gBACzC,CAAC,CAAC,CAAA;gBAEN,4DAA4D;gBAC5D,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,GAAW,EAAE,EAAE;oBAC9B,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;wBACf,cAAc,CAAC,IAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;wBAChD,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC;wBACf,CAAC,CAAC,GAAG,CAAA;gBACb,CAAC,CAAC,CAAA;YACN,CAAC;iBAAM,CAAC;gBACJ,4DAA4D;gBAC5D,KAAK;oBACD,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;wBACd,cAAc,CAAC,IAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;wBAC9C,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC;wBACjB,CAAC,CAAC,KAAK,CAAA;YACnB,CAAC;QACL,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACxC,8BAA8B;YAC9B,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAA;QACpD,CAAC;QAED,IAAI,cAAc,CAAC,WAAW;YAC1B,KAAK,GAAG,sBAAsB,CAAC,aAAa,CACxC,cAAc,CAAC,WAAW,EAC1B,KAAK,CACR,CAAA;QACL,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;;OAGG;IACH,yBAAyB,CACrB,GAAW,EACX,UAAyB,EACzB,gBAA+B;QAE/B,MAAM,iBAAiB,GAAU,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,GAAG,CAC9D,CAAC,GAAG,EAAE,EAAE,CAAC,gBAAgB,CAAC,GAAG,CAAC,CACjC,CAAA;QACD,IAAI,CAAC,UAAU,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM;YAC9C,OAAO,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAA;QAEnC,MAAM,iBAAiB,GAAG,IAAI,GAAG,EAAkB,CAAA;QACnD,GAAG,GAAG,GAAG,CAAC,OAAO,CACb,6BAA6B,EAC7B,CAAC,IAAI,EAAE,OAAe,EAAE,GAAW,EAAU,EAAE;YAC3C,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC;gBAClC,OAAO,IAAI,CAAA;YACf,CAAC;YAED,IAAI,iBAAiB,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC7B,OAAO,IAAI,CAAC,gBAAgB,GAAG,iBAAiB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;YAC7D,CAAC;YAED,MAAM,KAAK,GAAQ,UAAU,CAAC,GAAG,CAAC,CAAA;YAElC,IAAI,OAAO,EAAE,CAAC;gBACV,OAAO,KAAK;qBACP,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE;oBACZ,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;oBACzB,OAAO,IAAI,CAAC,eAAe,CACvB,GAAG,EACH,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAC/B,CAAA;gBACL,CAAC,CAAC;qBACD,IAAI,CAAC,IAAI,CAAC,CAAA;YACnB,CAAC;YAED,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE,CAAC;gBAC9B,OAAO,KAAK,EAAE,CAAA;YAClB,CAAC;YAED,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YAC7B,iBAAiB,CAAC,GAAG,CAAC,GAAG,EAAE,iBAAiB,CAAC,MAAM,CAAC,CAAA;YACpD,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;QAClE,CAAC,CACJ,CAAA,CAAC,kEAAkE;QACpE,OAAO,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAA;IACnC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,UAAkB;QACrB,OAAO,GAAG,GAAG,UAAU,GAAG,GAAG,CAAA;IACjC,CAAC;IAED;;;OAGG;IACH,cAAc,CAAC,SAAiB,EAAE,MAAe;QAC7C,MAAM,SAAS,GAAG,CAAC,SAAS,CAAC,CAAA;QAE7B,IAAI,MAAM,EAAE,CAAC;YACT,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;QAC7B,CAAC;QAED,OAAO,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IAC9B,CAAC;IAED;;OAEG;IACH,cAAc,CACV,MAAgE;QAEhE,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAA;QACpC,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAA;QAEhC,IAAI,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,eAAe,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;YACpE,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;YAE/C,OAAO;gBACH,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,IAAI,cAAc;gBAC9D,MAAM,EAAE,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,IAAI,YAAY;gBACtD,SAAS,EAAE,MAAM,CAAC,SAAS;aAC9B,CAAA;QACL,CAAC;QAED,IAAI,eAAe,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE,CAAC;YAC5C,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAA;YAE9D,OAAO;gBACH,QAAQ,EACJ,MAAM,CAAC,kBAAkB;oBACzB,MAAM,CAAC,QAAQ;oBACf,cAAc;gBAClB,MAAM,EACF,MAAM,CAAC,gBAAgB,IAAI,MAAM,CAAC,MAAM,IAAI,YAAY;gBAC5D,SAAS,EAAE,MAAM,CAAC,SAAS;aAC9B,CAAA;QACL,CAAC;QAED,IAAI,eAAe,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,CAAC;YAC3C,2CAA2C;YAE3C,OAAO;gBACH,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,cAAc;gBAC3C,MAAM,EAAE,MAAM,CAAC,MAAM,IAAI,YAAY;gBACrC,SAAS,EAAE,MAAM,CAAC,SAAS;aAC9B,CAAA;QACL,CAAC;QAED,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QAE/B,OAAO;YACH,QAAQ,EAAE,cAAc;YACxB,MAAM,EAAE,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,YAAY;YACjE,SAAS,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;SACpD,CAAA;IACL,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,MAMb;QACG,IACI,MAAM,CAAC,IAAI,KAAK,MAAM;YACtB,MAAM,CAAC,IAAI,KAAK,KAAK;YACrB,MAAM,CAAC,IAAI,KAAK,MAAM,EACxB,CAAC;YACC,OAAO,SAAS,CAAA;QACpB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YAC7D,OAAO,mBAAmB,CAAA;QAC9B,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,IAAI,MAAM,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;YAC7D,OAAO,6BAA6B,CAAA;QACxC,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;YACvC,OAAO,0BAA0B,CAAA;QACrC,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YAChC,OAAO,wBAAwB,CAAA;QACnC,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;YAClC,OAAO,qBAAqB,CAAA;QAChC,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,OAAO,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YAC3D,OAAO,SAAS,CAAA;QACpB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;YACxC,OAAO,MAAM,CAAA;QACjB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;YACvC,OAAO,MAAM,CAAA;QACjB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;YACvC,OAAO,MAAM,CAAA;QACjB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YAChC,OAAO,UAAU,CAAA;QACrB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YAChC,OAAO,QAAQ,CAAA;QACnB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YACnC,OAAO,SAAS,CAAA;QACpB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,QAAQ,IAAI,MAAM,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YAC7D,OAAO,kBAAkB,CAAA;QAC7B,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;YAClC,OAAO,MAAM,CAAA;QACjB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YAChC,OAAO,WAAW,CAAA;QACtB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;YAClC,OAAO,aAAa,CAAA;QACxB,CAAC;aAAM,CAAC;YACJ,OAAQ,MAAM,CAAC,IAAe,IAAI,EAAE,CAAA;QACxC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,cAA8B;QAC3C,MAAM,YAAY,GAAG,cAAc,CAAC,OAAO,CAAA;QAE3C,IAAI,YAAY,KAAK,IAAI,IAAI,YAAY,KAAK,SAAS,EAAE,CAAC;YACtD,OAAO,SAAS,CAAA;QACpB,CAAC;QAED,IAAI,cAAc,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC;YACxD,OAAO,KAAK,YAAY;iBACnB,GAAG,CAAC,CAAC,GAAW,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC;iBAC9B,IAAI,CAAC,GAAG,CAAC,IAAI,CAAA;QACtB,CAAC;QAED,IACI,CAAC,cAAc,CAAC,IAAI,KAAK,MAAM;YAC3B,cAAc,CAAC,IAAI,KAAK,aAAa;YACrC,OAAO,YAAY,KAAK,QAAQ;YAChC,OAAO,YAAY,KAAK,QAAQ,CAAC;YACrC,YAAY,KAAK,SAAS,EAC5B,CAAC;YACC,OAAO,IAAI,YAAY,GAAG,CAAA;QAC9B,CAAC;QAED,IAAI,OAAO,YAAY,KAAK,SAAS,EAAE,CAAC;YACpC,OAAO,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAA;QAC1C,CAAC;QAED,IAAI,OAAO,YAAY,KAAK,UAAU,EAAE,CAAC;YACrC,MAAM,KAAK,GAAG,YAAY,EAAE,CAAA;YAE5B,OAAO,IAAI,CAAC,yBAAyB,CAAC,KAAK,CAAC,CAAA;QAChD,CAAC;QAED,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE,CAAC;YACnC,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,GAAG,CAAA;QAC9C,CAAC;QAED,OAAO,GAAG,YAAY,EAAE,CAAA;IAC5B,CAAC;IAED;;;OAGG;IACK,YAAY,CAChB,cAA8B,EAC9B,WAAwB;QAExB,IACI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAc,CAAC;YACzD,CAAC,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC,QAAQ,CAAC,OAAO,cAAc,CAAC,OAAO,CAAC,EACpE,CAAC;YACC,MAAM,kBAAkB,GACpB,OAAO,WAAW,CAAC,OAAO,KAAK,QAAQ;gBACnC,CAAC,CAAC,IAAI,CAAC,KAAK,CACN,WAAW,CAAC,OAAO,CAAC,SAAS,CACzB,CAAC,EACD,WAAW,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CACjC,CACJ;gBACH,CAAC,CAAC,WAAW,CAAC,OAAO,CAAA;YAE7B,OAAO,QAAQ,CAAC,WAAW,CACvB,cAAc,CAAC,OAAO,EACtB,kBAAkB,CACrB,CAAA;QACL,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,4BAA4B,CACnD,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,CACxC,CAAA;QACD,OAAO,aAAa,KAAK,WAAW,CAAC,OAAO,CAAA;IAChD,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,MAAsB;QACpC,OAAO,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CACrC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM,CAC9D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,MAAsB;QAClC,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,EAAE,CAAA;IACxD,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,MAAmB;QAC9B,IAAI,IAAI,GAAG,MAAM,CAAC,IAAI,CAAA;QAEtB,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YAChB,IAAI,IAAI,GAAG,GAAG,MAAM,CAAC,MAAM,GAAG,GAAG,CAAA;QACrC,CAAC;aAAM,IACH,MAAM,CAAC,SAAS,KAAK,IAAI;YACzB,MAAM,CAAC,SAAS,KAAK,SAAS;YAC9B,MAAM,CAAC,KAAK,KAAK,IAAI;YACrB,MAAM,CAAC,KAAK,KAAK,SAAS,EAC5B,CAAC;YACC,IAAI,IAAI,GAAG,GAAG,MAAM,CAAC,SAAS,GAAG,GAAG,GAAG,MAAM,CAAC,KAAK,GAAG,GAAG,CAAA;QAC7D,CAAC;aAAM,IACH,MAAM,CAAC,SAAS,KAAK,IAAI;YACzB,MAAM,CAAC,SAAS,KAAK,SAAS,EAChC,CAAC;YACC,IAAI,IAAI,GAAG,GAAG,MAAM,CAAC,SAAS,GAAG,GAAG,CAAA;QACxC,CAAC;QAED,IAAI,MAAM,CAAC,IAAI,KAAK,wBAAwB,EAAE,CAAC;YAC3C,IAAI;gBACA,MAAM;oBACN,CAAC,MAAM,CAAC,SAAS,KAAK,IAAI,IAAI,MAAM,CAAC,SAAS,KAAK,SAAS;wBACxD,CAAC,CAAC,GAAG,GAAG,MAAM,CAAC,SAAS,GAAG,GAAG;wBAC9B,CAAC,CAAC,EAAE,CAAC,CAAA;QACjB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,qBAAqB,EAAE,CAAC;YAC/C,IAAI;gBACA,MAAM;oBACN,CAAC,MAAM,CAAC,SAAS,KAAK,IAAI,IAAI,MAAM,CAAC,SAAS,KAAK,SAAS;wBACxD,CAAC,CAAC,GAAG,GAAG,MAAM,CAAC,SAAS,GAAG,GAAG;wBAC9B,CAAC,CAAC,EAAE,CAAC;oBACT,iBAAiB,CAAA;QACzB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,6BAA6B,EAAE,CAAC;YACvD,IAAI;gBACA,WAAW;oBACX,CAAC,MAAM,CAAC,SAAS,KAAK,IAAI,IAAI,MAAM,CAAC,SAAS,KAAK,SAAS;wBACxD,CAAC,CAAC,GAAG,GAAG,MAAM,CAAC,SAAS,GAAG,GAAG;wBAC9B,CAAC,CAAC,EAAE,CAAC,CAAA;QACjB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,0BAA0B,EAAE,CAAC;YACpD,IAAI;gBACA,WAAW;oBACX,CAAC,MAAM,CAAC,SAAS,KAAK,IAAI,IAAI,MAAM,CAAC,SAAS,KAAK,SAAS;wBACxD,CAAC,CAAC,GAAG,GAAG,MAAM,CAAC,SAAS,GAAG,GAAG;wBAC9B,CAAC,CAAC,EAAE,CAAC;oBACT,iBAAiB,CAAA;QACzB,CAAC;aAAM,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,IAAkB,CAAC,IAAI,CAAC,EAAE,CAAC;YACnE,IAAI,MAAM,CAAC,kBAAkB,IAAI,IAAI,IAAI,MAAM,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC;gBAC3D,IAAI,GAAG,GAAG,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,kBAAkB,IAAI,MAAM,CAAC,IAAI,GAAG,CAAA;YACxE,CAAC;iBAAM,IAAI,MAAM,CAAC,kBAAkB,IAAI,IAAI,EAAE,CAAC;gBAC3C,IAAI,GAAG,GAAG,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,kBAAkB,GAAG,CAAA;YACzD,CAAC;iBAAM,CAAC;gBACJ,IAAI,GAAG,MAAM,CAAC,IAAI,CAAA;YACtB,CAAC;QACL,CAAC;QAED,IAAI,MAAM,CAAC,OAAO;YAAE,IAAI,IAAI,QAAQ,CAAA;QAEpC,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,sBAAsB;QACxB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACf,MAAM,IAAI,YAAY,CAAC,sBAAsB,CAAC,CAAA;QAClD,CAAC;QAED,OAAO,IAAI,OAAO,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;YAC5B,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,GAAQ,EAAE,UAAe,EAAE,OAAY,EAAE,EAAE;gBAC5D,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,CAAA;YAC/C,CAAC,CAAC,CAAA;QACN,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,qBAAqB;QACvB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC,sBAAsB,EAAE,CAAA;QACxC,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;QAE7D,OAAO,IAAI,OAAO,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;YAC5B,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CACvB,CAAC,GAAQ,EAAE,UAAe,EAAE,OAAY,EAAE,EAAE;gBACxC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,CAAA;YAC/C,CAAC,CACJ,CAAA;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;;;OAIG;IACH,kBAAkB,CAAC,QAAwB,EAAE,YAA2B;QACpE,IAAI,CAAC,YAAY;YAAE,OAAO,SAAS,CAAA;QAEnC,OAAO,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;YACjD,MAAM,MAAM,GAAG,QAAQ,CAAC,0BAA0B,CAAC,GAAG,CAAC,CAAA;YACvD,IAAI,MAAM,EAAE,CAAC;gBACT,QAAQ,CAAC,SAAS,CACd,GAAG,EACH,MAAM,CAAC,cAAc,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAC3C,CAAA;gBACD,8KAA8K;YAClL,CAAC;YACD,OAAO,GAAG,CAAA;QACd,CAAC,EAAE,EAAmB,CAAC,CAAA;IAC3B,CAAC;IAED;;;OAGG;IACH,kBAAkB,CACd,YAA2B,EAC3B,eAAiC;QAEjC,OAAO,eAAe,CAAC,MAAM,CAAC,CAAC,cAAc,EAAE,EAAE;YAC7C,MAAM,WAAW,GAAG,YAAY,CAAC,IAAI,CACjC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,cAAc,CAAC,YAAY,CAChD,CAAA;YACD,IAAI,CAAC,WAAW;gBAAE,OAAO,KAAK,CAAA,CAAC,4DAA4D;YAE3F,MAAM,eAAe,GACjB,WAAW,CAAC,IAAI,KAAK,cAAc,CAAC,YAAY;gBAChD,WAAW,CAAC,IAAI,KAAK,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC;gBACvD,WAAW,CAAC,MAAM,KAAK,cAAc,CAAC,MAAM;gBAC5C,WAAW,CAAC,OAAO,KAAK,cAAc,CAAC,OAAO;gBAC9C,WAAW,CAAC,SAAS,KAAK,cAAc,CAAC,SAAS;gBAClD,CAAC,cAAc,CAAC,KAAK,KAAK,SAAS;oBAC/B,WAAW,CAAC,KAAK,KAAK,cAAc,CAAC,KAAK,CAAC;gBAC/C,WAAW,CAAC,OAAO;oBACf,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,OAAO,CAAC;gBAC9C,CAAC,CAAC,WAAW,CAAC,WAAW;oBACrB,CAAC,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC,IAAI,kGAAkG;gBAC1J,WAAW,CAAC,SAAS,KAAK,cAAc,CAAC,SAAS;gBAClD,WAAW,CAAC,UAAU,KAAK,cAAc,CAAC,UAAU;gBACpD,WAAW,CAAC,QAAQ;oBAChB,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC;gBAC1C,WAAW,CAAC,QAAQ,KAAK,cAAc,CAAC,QAAQ;gBAChD,CAAC,WAAW,CAAC,IAAI;oBACb,cAAc,CAAC,IAAI;oBACnB,CAAC,QAAQ,CAAC,aAAa,CACnB,WAAW,CAAC,IAAI,EAChB,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,CAC7C,CAAC,IAAI,uCAAuC;gBACjD,WAAW,CAAC,WAAW,KAAK,cAAc,CAAC,WAAW;gBACtD,CAAC,WAAW,CAAC,kBAAkB,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE;oBAChD,CAAC,cAAc,CAAC,kBAAkB,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE;gBAC3D,WAAW,CAAC,IAAI,KAAK,cAAc,CAAC,IAAI;gBACxC,WAAW,CAAC,aAAa,KAAK,cAAc,CAAC,aAAa;gBAC1D,CAAC,WAAW,CAAC,YAAY,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE;oBACnC,CAAC,cAAc,CAAC,YAAY,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE;gBAC9C,WAAW,CAAC,SAAS,KAAK,cAAc,CAAC,SAAS,CAAA;YAEtD,gBAAgB;YAChB,yBAAyB;YACzB,qEAAqE;YACrE,mBAAmB;YACnB,mBAAmB;YACnB,4BAA4B;YAC5B,uCAAuC;YACvC,QAAQ;YACR,mBAAmB;YACnB,mBAAmB;YACnB,4BAA4B;YAC5B,8CAA8C;YAC9C,QAAQ;YACR,mBAAmB;YACnB,qBAAqB;YACrB,8BAA8B;YAC9B,iCAAiC;YACjC,QAAQ;YACR,mBAAmB;YACnB,sBAAsB;YACtB,+BAA+B;YAC/B,kCAAkC;YAClC,QAAQ;YACR,mBAAmB;YACnB,wBAAwB;YACxB,iCAAiC;YACjC,oCAAoC;YACpC,QAAQ;YACR,qEAAqE;YACrE,mBAAmB;YACnB,sBAAsB;YACtB,+BAA+B;YAC/B,sDAAsD;YACtD,QAAQ;YACR,mBAAmB;YACnB,uBAAuB;YACvB,gCAAgC;YAChC,mCAAmC;YACnC,QAAQ;YACR,mBAAmB;YACnB,mBAAmB;YACnB,8BAA8B;YAC9B,qCAAqC;YACrC,uCAAuC;YACvC,oCAAoC;YACpC,8DAA8D;YAC9D,iBAAiB;YACjB,QAAQ;YACR,mBAAmB;YACnB,wBAAwB;YACxB,iCAAiC;YACjC,oCAAoC;YACpC,QAAQ;YACR,mBAAmB;YACnB,yBAAyB;YACzB,kCAAkC;YAClC,qCAAqC;YACrC,QAAQ;YACR,mBAAmB;YACnB,uBAAuB;YACvB,gCAAgC;YAChC,kDAAkD;YAClD,QAAQ;YACR,mBAAmB;YACnB,0BAA0B;YAC1B,mCAAmC;YACnC,sCAAsC;YACtC,QAAQ;YACR,mBAAmB;YACnB,4BAA4B;YAC5B,qCAAqC;YACrC,wCAAwC;YACxC,QAAQ;YACR,mBAAmB;YACnB,2BAA2B;YAC3B,mDAAmD;YACnD,sDAAsD;YACtD,QAAQ;YACR,mBAAmB;YACnB,wBAAwB;YACxB,iCAAiC;YACjC,oCAAoC;YACpC,QAAQ;YACR,mBAAmB;YACnB,4BAA4B;YAC5B,sCAAsC;YACtC,iDAAiD;YACjD,yDAAyD;YACzD,yCAAyC;YACzC,QAAQ;YACR,mBAAmB;YACnB,iCAAiC;YACjC,gEAAgE;YAChE,mEAAmE;YACnE,QAAQ;YACR,iEAAiE;YACjE,gEAAgE;YAChE,IAAI;YAEJ,OAAO,eAAe,CAAA;QAC1B,CAAC,CAAC,CAAA;IACN,CAAC;IAEO,4BAA4B,CAAC,KAAyB;QAC1D,oEAAoE;QACpE,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,OAAO,KAAK,CAAA;QAChB,CAAC;QACD,OAAO,KAAK;aACP,KAAK,CAAC,GAAG,CAAC;aACV,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YACV,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAA;QAC5C,CAAC,CAAC;aACD,IAAI,CAAC,GAAG,CAAC,CAAA;IAClB,CAAC;IAED;;OAEG;IACH,uBAAuB;QACnB,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;OAEG;IACH,yBAAyB;QACrB,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;OAEG;IACH,6BAA6B;QACzB,OAAO,KAAK,CAAA;IAChB,CAAC;IAED,IAAI,aAAa;QACb,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa,KAAK,UAAU;YAC5C,CAAC,CAAC,mBAAmB;YACrB,CAAC,CAAC,oBAAoB,CAAA;IAC9B,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,aAAqB,EAAE,KAAa;QAChD,OAAO,IAAI,CAAC,gBAAgB,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAA;IAC9C,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,oBAAoB;QAChB,IAAI,CAAC;YACD,OAAO,aAAa,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAA;QAChD,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACT,qCAAqC;YACrC,MAAM,IAAI,YAAY,CAClB,6GAA6G,CAChH,CAAA;QACL,CAAC;IACL,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;OAEG;IACO,gBAAgB;QACtB,IAAI,CAAC;YACD,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAChE,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAA;YACxB,IAAI,CAAC;gBACD,MAAM,QAAQ,GACV,IAAI,CAAC,OAAO,CAAC,YAAY,IAAI,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;gBAChE,IAAI,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM;oBAChC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAA;YAC5C,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC,CAAA,CAAC;QAClB,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACT,qCAAqC;YACrC,MAAM,IAAI,8BAA8B,CAAC,UAAU,EAAE,IAAI,CAAC,CAAA;QAC9D,CAAC;IACL,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,UAAU,CACtB,OAAkC,EAClC,WAAiD;QAEjD,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAA;QAClC,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,WAAW,CAAC,CAAA;QAE5C,0CAA0C;QAC1C,mFAAmF;QACnF,MAAM,iBAAiB,GAAG,MAAM,CAAC,MAAM,CACnC,EAAE,EACF;YACI,gBAAgB,EAAE,WAAW,CAAC,GAAG;YACjC,IAAI,EAAE,WAAW,CAAC,IAAI;YACtB,IAAI,EAAE,WAAW,CAAC,QAAQ;YAC1B,QAAQ,EAAE,WAAW,CAAC,QAAQ;YAC9B,QAAQ,EAAE,WAAW,CAAC,QAAQ;YAC9B,IAAI,EAAE,WAAW,CAAC,IAAI;YACtB,GAAG,EAAE,WAAW,CAAC,GAAG;YACpB,uBAAuB,EAAE,OAAO,CAAC,gBAAgB;YACjD,gBAAgB,EACZ,OAAO,CAAC,eAAe,IAAI,WAAW,CAAC,eAAe;YAC1D,GAAG,EAAE,OAAO,CAAC,QAAQ;SACxB,EACD,OAAO,CAAC,KAAK,IAAI,EAAE,CACtB,CAAA;QAED,IAAI,OAAO,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;YAClC,IACI,IAAI,CAAC,QAAQ,CAAC,QAAQ;gBACtB,MAAM,CAAC,wBAAwB,CAC3B,IAAI,CAAC,QAAQ,CAAC,QAAQ,EACtB,WAAW,CACd,EAAE,GAAG,EACR,CAAC;gBACC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAA;YACxD,CAAC;iBAAM,CAAC;gBACJ,MAAM,CAAC,GAAG,CACN,MAAM,EACN,sIAAsI,CACzI,CAAA;YACL,CAAC;QACL,CAAC;QAED,2BAA2B;QAC3B,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAA;QAEtD,MAAM,gBAAgB,GAClB,OAAO,CAAC,gBAAgB;YACxB,CAAC,CAAC,KAAU,EAAE,EAAE,CACZ,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,kCAAkC,KAAK,EAAE,CAAC,CAAC,CAAA;QAEtE;;;WAGG;QACH,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAA;QAElC,OAAO,IAAI,OAAO,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;YAC5B,IAAI,CAAC,OAAO,CAAC,CAAC,GAAQ,EAAE,UAAe,EAAE,OAAiB,EAAE,EAAE;gBAC1D,IAAI,GAAG;oBAAE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAA;gBAEzB,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC;oBAC3B,UAAU,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,GAAQ,EAAE,EAAE;wBACjC,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,OAAO,CAAC,CAAA;oBAC1D,CAAC,CAAC,CAAA;oBACF,UAAU,CAAC,EAAE,CAAC,cAAc,EAAE,CAAC,GAAQ,EAAE,EAAE;wBACvC,GAAG;4BACC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,CACtB,MAAM,EACN,8BAA8B,GAAG,CAAC,OAAO,KAAK,GAAG,CAAC,OAAO,GAAG,CAC/D,CAAA;oBACT,CAAC,CAAC,CAAA;gBACN,CAAC;gBACD,OAAO,EAAE,CAAA;gBACT,EAAE,CAAC,IAAI,CAAC,CAAA;YACZ,CAAC,CAAC,CAAA;QACN,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,SAAS,CAAC,IAAS;QAC/B,OAAO,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,CAAC;YACvC,MAAM,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAA;QACjD,CAAC;QAED,OAAO,IAAI,OAAO,CAAO,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;YAClC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAQ,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAA;QACpD,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACO,YAAY,CAAC,UAAe,EAAE,KAAa;QACjD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;QAEtC,OAAO,IAAI,OAAO,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;YAC5B,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,GAAQ,EAAE,MAAW,EAAE,EAAE,CAC9C,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAC/B,CAAA;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;;OAGG;IACO,yBAAyB,CAAC,KAAa;QAC7C,sCAAsC;QACtC,MAAM,cAAc,GAAG,KAAK,CAAC,WAAW,EAAE,CAAA;QAC1C,MAAM,kBAAkB,GACpB,cAAc,CAAC,OAAO,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;YAClD,cAAc,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YAC7C,cAAc,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YAC7C,cAAc,CAAC,OAAO,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;YAC/C,cAAc,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAA;QAE9C,IAAI,kBAAkB,EAAE,CAAC;YACrB,gCAAgC;YAChC,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;YAExC,IAAI,cAAc,CAAC,OAAO,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;gBACrD,OAAO,SAAS;oBACZ,CAAC,CAAC,2BAA2B,SAAS,CAAC,CAAC,CAAC,iBAAiB;oBAC1D,CAAC,CAAC,OAAO,CAAA;YACjB,CAAC;iBAAM,IAAI,cAAc,KAAK,cAAc,EAAE,CAAC;gBAC3C,OAAO,qBAAqB,CAAA;YAChC,CAAC;iBAAM,IAAI,cAAc,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;gBACvD,OAAO,SAAS;oBACZ,CAAC,CAAC,sBAAsB,SAAS,CAAC,CAAC,CAAC,iBAAiB;oBACrD,CAAC,CAAC,oCAAoC,CAAA;YAC9C,CAAC;iBAAM,IAAI,cAAc,CAAC,OAAO,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;gBACzD,OAAO,SAAS;oBACZ,CAAC,CAAC,2BAA2B,SAAS,CAAC,CAAC,CAAC,oBAAoB;oBAC7D,CAAC,CAAC,4CAA4C,CAAA;YACtD,CAAC;iBAAM,IAAI,cAAc,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;gBACpD,OAAO,SAAS;oBACZ,CAAC,CAAC,sBAAsB,SAAS,CAAC,CAAC,CAAC,oBAAoB;oBACxD,CAAC,CAAC,uCAAuC,CAAA;YACjD,CAAC;QACL,CAAC;QAED,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;OAEG;IACO,aAAa,CAAC,OAAgB;QACpC,IAAI,CAAC,OAAO;YAAE,OAAO,OAAO,CAAA;QAE5B,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAA,CAAC,wCAAwC;QAEjF,OAAO,OAAO,CAAA;IAClB,CAAC;CACJ", "file": "PostgresDriver.js", "sourcesContent": ["import { ObjectLiteral } from \"../../common/ObjectLiteral\"\nimport { DataSource } from \"../../data-source/DataSource\"\nimport { ConnectionIsNotSetError } from \"../../error/ConnectionIsNotSetError\"\nimport { DriverPackageNotInstalledError } from \"../../error/DriverPackageNotInstalledError\"\nimport { ColumnMetadata } from \"../../metadata/ColumnMetadata\"\nimport { EntityMetadata } from \"../../metadata/EntityMetadata\"\nimport { PlatformTools } from \"../../platform/PlatformTools\"\nimport { QueryRunner } from \"../../query-runner/QueryRunner\"\nimport { RdbmsSchemaBuilder } from \"../../schema-builder/RdbmsSchemaBuilder\"\nimport { TableColumn } from \"../../schema-builder/table/TableColumn\"\nimport { ApplyValueTransformers } from \"../../util/ApplyValueTransformers\"\nimport { DateUtils } from \"../../util/DateUtils\"\nimport { OrmUtils } from \"../../util/OrmUtils\"\nimport { Driver } from \"../Driver\"\nimport { ColumnType } from \"../types/ColumnTypes\"\nimport { CteCapabilities } from \"../types/CteCapabilities\"\nimport { DataTypeDefaults } from \"../types/DataTypeDefaults\"\nimport { MappedColumnTypes } from \"../types/MappedColumnTypes\"\nimport { ReplicationMode } from \"../types/ReplicationMode\"\nimport { VersionUtils } from \"../../util/VersionUtils\"\nimport { PostgresConnectionCredentialsOptions } from \"./PostgresConnectionCredentialsOptions\"\nimport { PostgresConnectionOptions } from \"./PostgresConnectionOptions\"\nimport { PostgresQueryRunner } from \"./PostgresQueryRunner\"\nimport { DriverUtils } from \"../DriverUtils\"\nimport { TypeORMError } from \"../../error\"\nimport { Table } from \"../../schema-builder/table/Table\"\nimport { View } from \"../../schema-builder/view/View\"\nimport { TableForeignKey } from \"../../schema-builder/table/TableForeignKey\"\nimport { InstanceChecker } from \"../../util/InstanceChecker\"\nimport { UpsertType } from \"../types/UpsertType\"\n\n/**\n * Organizes communication with PostgreSQL DBMS.\n */\nexport class PostgresDriver implements Driver {\n    // -------------------------------------------------------------------------\n    // Public Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Connection used by driver.\n     */\n    connection: DataSource\n\n    /**\n     * Postgres underlying library.\n     */\n    postgres: any\n\n    /**\n     * Pool for master database.\n     */\n    master: any\n\n    /**\n     * Pool for slave databases.\n     * Used in replication.\n     */\n    slaves: any[] = []\n\n    /**\n     * We store all created query runners because we need to release them.\n     */\n    connectedQueryRunners: QueryRunner[] = []\n\n    // -------------------------------------------------------------------------\n    // Public Implemented Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Connection options.\n     */\n    options: PostgresConnectionOptions\n\n    /**\n     * Version of Postgres. Requires a SQL query to the DB, so it is not always set\n     */\n    version?: string\n\n    /**\n     * Database name used to perform all write queries.\n     */\n    database?: string\n\n    /**\n     * Schema name used to perform all write queries.\n     */\n    schema?: string\n\n    /**\n     * Schema that's used internally by Postgres for object resolution.\n     *\n     * Because we never set this we have to track it in separately from the `schema` so\n     * we know when we have to specify the full schema or not.\n     *\n     * In most cases this will be `public`.\n     */\n    searchSchema?: string\n\n    /**\n     * Indicates if replication is enabled.\n     */\n    isReplicated: boolean = false\n\n    /**\n     * Indicates if tree tables are supported by this driver.\n     */\n    treeSupport = true\n\n    /**\n     * Represent transaction support by this driver\n     */\n    transactionSupport = \"nested\" as const\n\n    /**\n     * Gets list of supported column data types by a driver.\n     *\n     * @see https://www.tutorialspoint.com/postgresql/postgresql_data_types.htm\n     * @see https://www.postgresql.org/docs/9.2/static/datatype.html\n     */\n    supportedDataTypes: ColumnType[] = [\n        \"int\",\n        \"int2\",\n        \"int4\",\n        \"int8\",\n        \"smallint\",\n        \"integer\",\n        \"bigint\",\n        \"decimal\",\n        \"numeric\",\n        \"real\",\n        \"float\",\n        \"float4\",\n        \"float8\",\n        \"double precision\",\n        \"money\",\n        \"character varying\",\n        \"varchar\",\n        \"character\",\n        \"char\",\n        \"text\",\n        \"citext\",\n        \"hstore\",\n        \"bytea\",\n        \"bit\",\n        \"varbit\",\n        \"bit varying\",\n        \"timetz\",\n        \"timestamptz\",\n        \"timestamp\",\n        \"timestamp without time zone\",\n        \"timestamp with time zone\",\n        \"date\",\n        \"time\",\n        \"time without time zone\",\n        \"time with time zone\",\n        \"interval\",\n        \"bool\",\n        \"boolean\",\n        \"enum\",\n        \"point\",\n        \"line\",\n        \"lseg\",\n        \"box\",\n        \"path\",\n        \"polygon\",\n        \"circle\",\n        \"cidr\",\n        \"inet\",\n        \"macaddr\",\n        \"macaddr8\",\n        \"tsvector\",\n        \"tsquery\",\n        \"uuid\",\n        \"xml\",\n        \"json\",\n        \"jsonb\",\n        \"int4range\",\n        \"int8range\",\n        \"numrange\",\n        \"tsrange\",\n        \"tstzrange\",\n        \"daterange\",\n        \"int4multirange\",\n        \"int8multirange\",\n        \"nummultirange\",\n        \"tsmultirange\",\n        \"tstzmultirange\",\n        \"datemultirange\",\n        \"geometry\",\n        \"geography\",\n        \"cube\",\n        \"ltree\",\n    ]\n\n    /**\n     * Returns type of upsert supported by driver if any\n     */\n    supportedUpsertTypes: UpsertType[] = [\"on-conflict-do-update\"]\n\n    /**\n     * Gets list of spatial column data types.\n     */\n    spatialTypes: ColumnType[] = [\"geometry\", \"geography\"]\n\n    /**\n     * Gets list of column data types that support length by a driver.\n     */\n    withLengthColumnTypes: ColumnType[] = [\n        \"character varying\",\n        \"varchar\",\n        \"character\",\n        \"char\",\n        \"bit\",\n        \"varbit\",\n        \"bit varying\",\n    ]\n\n    /**\n     * Gets list of column data types that support precision by a driver.\n     */\n    withPrecisionColumnTypes: ColumnType[] = [\n        \"numeric\",\n        \"decimal\",\n        \"interval\",\n        \"time without time zone\",\n        \"time with time zone\",\n        \"timestamp without time zone\",\n        \"timestamp with time zone\",\n    ]\n\n    /**\n     * Gets list of column data types that support scale by a driver.\n     */\n    withScaleColumnTypes: ColumnType[] = [\"numeric\", \"decimal\"]\n\n    /**\n     * Orm has special columns and we need to know what database column types should be for those types.\n     * Column types are driver dependant.\n     */\n    mappedDataTypes: MappedColumnTypes = {\n        createDate: \"timestamp\",\n        createDateDefault: \"now()\",\n        updateDate: \"timestamp\",\n        updateDateDefault: \"now()\",\n        deleteDate: \"timestamp\",\n        deleteDateNullable: true,\n        version: \"int4\",\n        treeLevel: \"int4\",\n        migrationId: \"int4\",\n        migrationName: \"varchar\",\n        migrationTimestamp: \"int8\",\n        cacheId: \"int4\",\n        cacheIdentifier: \"varchar\",\n        cacheTime: \"int8\",\n        cacheDuration: \"int4\",\n        cacheQuery: \"text\",\n        cacheResult: \"text\",\n        metadataType: \"varchar\",\n        metadataDatabase: \"varchar\",\n        metadataSchema: \"varchar\",\n        metadataTable: \"varchar\",\n        metadataName: \"varchar\",\n        metadataValue: \"text\",\n    }\n\n    /**\n     * The prefix used for the parameters\n     */\n    parametersPrefix: string = \"$\"\n\n    /**\n     * Default values of length, precision and scale depends on column data type.\n     * Used in the cases when length/precision/scale is not specified by user.\n     */\n    dataTypeDefaults: DataTypeDefaults = {\n        character: { length: 1 },\n        bit: { length: 1 },\n        interval: { precision: 6 },\n        \"time without time zone\": { precision: 6 },\n        \"time with time zone\": { precision: 6 },\n        \"timestamp without time zone\": { precision: 6 },\n        \"timestamp with time zone\": { precision: 6 },\n    }\n\n    /**\n     * Max length allowed by Postgres for aliases.\n     * @see https://www.postgresql.org/docs/current/sql-syntax-lexical.html#SQL-SYNTAX-IDENTIFIERS\n     */\n    maxAliasLength = 63\n\n    isGeneratedColumnsSupported: boolean = false\n\n    cteCapabilities: CteCapabilities = {\n        enabled: true,\n        writable: true,\n        requiresRecursiveHint: true,\n        materializedHint: true,\n    }\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(connection?: DataSource) {\n        if (!connection) {\n            return\n        }\n\n        this.connection = connection\n        this.options = connection.options as PostgresConnectionOptions\n        this.isReplicated = this.options.replication ? true : false\n        if (this.options.useUTC) {\n            process.env.PGTZ = \"UTC\"\n        }\n        // load postgres package\n        this.loadDependencies()\n\n        this.database = DriverUtils.buildDriverOptions(\n            this.options.replication\n                ? this.options.replication.master\n                : this.options,\n        ).database\n        this.schema = DriverUtils.buildDriverOptions(this.options).schema\n\n        // ObjectUtils.assign(this.options, DriverUtils.buildDriverOptions(connection.options)); // todo: do it better way\n        // validate options to make sure everything is set\n        // todo: revisit validation with replication in mind\n        // if (!this.options.host)\n        //     throw new DriverOptionNotSetError(\"host\");\n        // if (!this.options.username)\n        //     throw new DriverOptionNotSetError(\"username\");\n        // if (!this.options.database)\n        //     throw new DriverOptionNotSetError(\"database\");\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Implemented Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Performs connection to the database.\n     * Based on pooling options, it can either create connection immediately,\n     * either create a pool and create connection when needed.\n     */\n    async connect(): Promise<void> {\n        if (this.options.replication) {\n            this.slaves = await Promise.all(\n                this.options.replication.slaves.map((slave) => {\n                    return this.createPool(this.options, slave)\n                }),\n            )\n            this.master = await this.createPool(\n                this.options,\n                this.options.replication.master,\n            )\n        } else {\n            this.master = await this.createPool(this.options, this.options)\n        }\n\n        const queryRunner = this.createQueryRunner(\"master\")\n\n        this.version = await queryRunner.getVersion()\n\n        if (!this.database) {\n            this.database = await queryRunner.getCurrentDatabase()\n        }\n\n        if (!this.searchSchema) {\n            this.searchSchema = await queryRunner.getCurrentSchema()\n        }\n\n        await queryRunner.release()\n\n        if (!this.schema) {\n            this.schema = this.searchSchema\n        }\n    }\n\n    /**\n     * Makes any action after connection (e.g. create extensions in Postgres driver).\n     */\n    async afterConnect(): Promise<void> {\n        const extensionsMetadata = await this.checkMetadataForExtensions()\n        const [connection, release] = await this.obtainMasterConnection()\n\n        const installExtensions =\n            this.options.installExtensions === undefined ||\n            this.options.installExtensions\n        if (installExtensions && extensionsMetadata.hasExtensions) {\n            await this.enableExtensions(extensionsMetadata, connection)\n        }\n\n        this.isGeneratedColumnsSupported = VersionUtils.isGreaterOrEqual(\n            this.version,\n            \"12.0\",\n        )\n\n        await release()\n    }\n\n    protected async enableExtensions(extensionsMetadata: any, connection: any) {\n        const { logger } = this.connection\n\n        const {\n            hasUuidColumns,\n            hasCitextColumns,\n            hasHstoreColumns,\n            hasCubeColumns,\n            hasGeometryColumns,\n            hasLtreeColumns,\n            hasExclusionConstraints,\n        } = extensionsMetadata\n\n        if (hasUuidColumns)\n            try {\n                await this.executeQuery(\n                    connection,\n                    `CREATE EXTENSION IF NOT EXISTS \"${\n                        this.options.uuidExtension || \"uuid-ossp\"\n                    }\"`,\n                )\n            } catch (_) {\n                logger.log(\n                    \"warn\",\n                    `At least one of the entities has uuid column, but the '${\n                        this.options.uuidExtension || \"uuid-ossp\"\n                    }' extension cannot be installed automatically. Please install it manually using superuser rights, or select another uuid extension.`,\n                )\n            }\n        if (hasCitextColumns)\n            try {\n                await this.executeQuery(\n                    connection,\n                    `CREATE EXTENSION IF NOT EXISTS \"citext\"`,\n                )\n            } catch (_) {\n                logger.log(\n                    \"warn\",\n                    \"At least one of the entities has citext column, but the 'citext' extension cannot be installed automatically. Please install it manually using superuser rights\",\n                )\n            }\n        if (hasHstoreColumns)\n            try {\n                await this.executeQuery(\n                    connection,\n                    `CREATE EXTENSION IF NOT EXISTS \"hstore\"`,\n                )\n            } catch (_) {\n                logger.log(\n                    \"warn\",\n                    \"At least one of the entities has hstore column, but the 'hstore' extension cannot be installed automatically. Please install it manually using superuser rights\",\n                )\n            }\n        if (hasGeometryColumns)\n            try {\n                await this.executeQuery(\n                    connection,\n                    `CREATE EXTENSION IF NOT EXISTS \"postgis\"`,\n                )\n            } catch (_) {\n                logger.log(\n                    \"warn\",\n                    \"At least one of the entities has a geometry column, but the 'postgis' extension cannot be installed automatically. Please install it manually using superuser rights\",\n                )\n            }\n        if (hasCubeColumns)\n            try {\n                await this.executeQuery(\n                    connection,\n                    `CREATE EXTENSION IF NOT EXISTS \"cube\"`,\n                )\n            } catch (_) {\n                logger.log(\n                    \"warn\",\n                    \"At least one of the entities has a cube column, but the 'cube' extension cannot be installed automatically. Please install it manually using superuser rights\",\n                )\n            }\n        if (hasLtreeColumns)\n            try {\n                await this.executeQuery(\n                    connection,\n                    `CREATE EXTENSION IF NOT EXISTS \"ltree\"`,\n                )\n            } catch (_) {\n                logger.log(\n                    \"warn\",\n                    \"At least one of the entities has a ltree column, but the 'ltree' extension cannot be installed automatically. Please install it manually using superuser rights\",\n                )\n            }\n        if (hasExclusionConstraints)\n            try {\n                // The btree_gist extension provides operator support in PostgreSQL exclusion constraints\n                await this.executeQuery(\n                    connection,\n                    `CREATE EXTENSION IF NOT EXISTS \"btree_gist\"`,\n                )\n            } catch (_) {\n                logger.log(\n                    \"warn\",\n                    \"At least one of the entities has an exclusion constraint, but the 'btree_gist' extension cannot be installed automatically. Please install it manually using superuser rights\",\n                )\n            }\n    }\n\n    protected async checkMetadataForExtensions() {\n        const hasUuidColumns = this.connection.entityMetadatas.some(\n            (metadata) => {\n                return (\n                    metadata.generatedColumns.filter(\n                        (column) => column.generationStrategy === \"uuid\",\n                    ).length > 0\n                )\n            },\n        )\n        const hasCitextColumns = this.connection.entityMetadatas.some(\n            (metadata) => {\n                return (\n                    metadata.columns.filter(\n                        (column) => column.type === \"citext\",\n                    ).length > 0\n                )\n            },\n        )\n        const hasHstoreColumns = this.connection.entityMetadatas.some(\n            (metadata) => {\n                return (\n                    metadata.columns.filter(\n                        (column) => column.type === \"hstore\",\n                    ).length > 0\n                )\n            },\n        )\n        const hasCubeColumns = this.connection.entityMetadatas.some(\n            (metadata) => {\n                return (\n                    metadata.columns.filter((column) => column.type === \"cube\")\n                        .length > 0\n                )\n            },\n        )\n        const hasGeometryColumns = this.connection.entityMetadatas.some(\n            (metadata) => {\n                return (\n                    metadata.columns.filter(\n                        (column) => this.spatialTypes.indexOf(column.type) >= 0,\n                    ).length > 0\n                )\n            },\n        )\n        const hasLtreeColumns = this.connection.entityMetadatas.some(\n            (metadata) => {\n                return (\n                    metadata.columns.filter((column) => column.type === \"ltree\")\n                        .length > 0\n                )\n            },\n        )\n        const hasExclusionConstraints = this.connection.entityMetadatas.some(\n            (metadata) => {\n                return metadata.exclusions.length > 0\n            },\n        )\n\n        return {\n            hasUuidColumns,\n            hasCitextColumns,\n            hasHstoreColumns,\n            hasCubeColumns,\n            hasGeometryColumns,\n            hasLtreeColumns,\n            hasExclusionConstraints,\n            hasExtensions:\n                hasUuidColumns ||\n                hasCitextColumns ||\n                hasHstoreColumns ||\n                hasGeometryColumns ||\n                hasCubeColumns ||\n                hasLtreeColumns ||\n                hasExclusionConstraints,\n        }\n    }\n\n    /**\n     * Closes connection with database.\n     */\n    async disconnect(): Promise<void> {\n        if (!this.master) {\n            throw new ConnectionIsNotSetError(\"postgres\")\n        }\n\n        await this.closePool(this.master)\n        await Promise.all(this.slaves.map((slave) => this.closePool(slave)))\n        this.master = undefined\n        this.slaves = []\n    }\n\n    /**\n     * Creates a schema builder used to build and sync a schema.\n     */\n    createSchemaBuilder() {\n        return new RdbmsSchemaBuilder(this.connection)\n    }\n\n    /**\n     * Creates a query runner used to execute database queries.\n     */\n    createQueryRunner(mode: ReplicationMode): PostgresQueryRunner {\n        return new PostgresQueryRunner(this, mode)\n    }\n\n    /**\n     * Prepares given value to a value to be persisted, based on its column type and metadata.\n     */\n    preparePersistentValue(value: any, columnMetadata: ColumnMetadata): any {\n        if (columnMetadata.transformer)\n            value = ApplyValueTransformers.transformTo(\n                columnMetadata.transformer,\n                value,\n            )\n\n        if (value === null || value === undefined) return value\n\n        if (columnMetadata.type === Boolean) {\n            return value === true ? 1 : 0\n        } else if (columnMetadata.type === \"date\") {\n            return DateUtils.mixedDateToDateString(value)\n        } else if (columnMetadata.type === \"time\") {\n            return DateUtils.mixedDateToTimeString(value)\n        } else if (\n            columnMetadata.type === \"datetime\" ||\n            columnMetadata.type === Date ||\n            columnMetadata.type === \"timestamp\" ||\n            columnMetadata.type === \"timestamp with time zone\" ||\n            columnMetadata.type === \"timestamp without time zone\"\n        ) {\n            return DateUtils.mixedDateToDate(value)\n        } else if (\n            [\"json\", \"jsonb\", ...this.spatialTypes].indexOf(\n                columnMetadata.type,\n            ) >= 0\n        ) {\n            return JSON.stringify(value)\n        } else if (columnMetadata.type === \"hstore\") {\n            if (typeof value === \"string\") {\n                return value\n            } else {\n                // https://www.postgresql.org/docs/9.0/hstore.html\n                const quoteString = (value: unknown) => {\n                    // If a string to be quoted is `null` or `undefined`, we return a literal unquoted NULL.\n                    // This way, NULL values can be stored in the hstore object.\n                    if (value === null || typeof value === \"undefined\") {\n                        return \"NULL\"\n                    }\n                    // Convert non-null values to string since HStore only stores strings anyway.\n                    // To include a double quote or a backslash in a key or value, escape it with a backslash.\n                    return `\"${`${value}`.replace(/(?=[\"\\\\])/g, \"\\\\\")}\"`\n                }\n                return Object.keys(value)\n                    .map(\n                        (key) =>\n                            quoteString(key) + \"=>\" + quoteString(value[key]),\n                    )\n                    .join(\",\")\n            }\n        } else if (columnMetadata.type === \"simple-array\") {\n            return DateUtils.simpleArrayToString(value)\n        } else if (columnMetadata.type === \"simple-json\") {\n            return DateUtils.simpleJsonToString(value)\n        } else if (columnMetadata.type === \"cube\") {\n            if (columnMetadata.isArray) {\n                return `{${value\n                    .map((cube: number[]) => `\"(${cube.join(\",\")})\"`)\n                    .join(\",\")}}`\n            }\n            return `(${value.join(\",\")})`\n        } else if (columnMetadata.type === \"ltree\") {\n            return value\n                .split(\".\")\n                .filter(Boolean)\n                .join(\".\")\n                .replace(/[\\s]+/g, \"_\")\n        } else if (\n            (columnMetadata.type === \"enum\" ||\n                columnMetadata.type === \"simple-enum\") &&\n            !columnMetadata.isArray\n        ) {\n            return \"\" + value\n        }\n\n        return value\n    }\n\n    /**\n     * Prepares given value to a value to be persisted, based on its column type or metadata.\n     */\n    prepareHydratedValue(value: any, columnMetadata: ColumnMetadata): any {\n        if (value === null || value === undefined)\n            return columnMetadata.transformer\n                ? ApplyValueTransformers.transformFrom(\n                      columnMetadata.transformer,\n                      value,\n                  )\n                : value\n\n        if (columnMetadata.type === Boolean) {\n            value = value ? true : false\n        } else if (\n            columnMetadata.type === \"datetime\" ||\n            columnMetadata.type === Date ||\n            columnMetadata.type === \"timestamp\" ||\n            columnMetadata.type === \"timestamp with time zone\" ||\n            columnMetadata.type === \"timestamp without time zone\"\n        ) {\n            value = DateUtils.normalizeHydratedDate(value)\n        } else if (columnMetadata.type === \"date\") {\n            value = DateUtils.mixedDateToDateString(value)\n        } else if (columnMetadata.type === \"time\") {\n            value = DateUtils.mixedTimeToString(value)\n        } else if (columnMetadata.type === \"hstore\") {\n            if (columnMetadata.hstoreType === \"object\") {\n                const unescapeString = (str: string) =>\n                    str.replace(/\\\\./g, (m) => m[1])\n                const regexp =\n                    /\"([^\"\\\\]*(?:\\\\.[^\"\\\\]*)*)\"=>(?:(NULL)|\"([^\"\\\\]*(?:\\\\.[^\"\\\\]*)*)\")(?:,|$)/g\n                const object: ObjectLiteral = {}\n                ;`${value}`.replace(\n                    regexp,\n                    (_, key, nullValue, stringValue) => {\n                        object[unescapeString(key)] = nullValue\n                            ? null\n                            : unescapeString(stringValue)\n                        return \"\"\n                    },\n                )\n                value = object\n            }\n        } else if (columnMetadata.type === \"simple-array\") {\n            value = DateUtils.stringToSimpleArray(value)\n        } else if (columnMetadata.type === \"simple-json\") {\n            value = DateUtils.stringToSimpleJson(value)\n        } else if (columnMetadata.type === \"cube\") {\n            value = value.replace(/[()\\s]+/g, \"\") // remove whitespace\n            if (columnMetadata.isArray) {\n                /**\n                 * Strips these groups from `{\"1,2,3\",\"\",NULL}`:\n                 * 1. [\"1,2,3\", undefined]  <- cube of arity 3\n                 * 2. [\"\", undefined]         <- cube of arity 0\n                 * 3. [undefined, \"NULL\"]     <- NULL\n                 */\n                const regexp = /(?:\"((?:[\\d\\s.,])*)\")|(?:(NULL))/g\n                const unparsedArrayString = value\n\n                value = []\n                let cube: RegExpExecArray | null = null\n                // Iterate through all regexp matches for cubes/null in array\n                while ((cube = regexp.exec(unparsedArrayString)) !== null) {\n                    if (cube[1] !== undefined) {\n                        value.push(\n                            cube[1].split(\",\").filter(Boolean).map(Number),\n                        )\n                    } else {\n                        value.push(undefined)\n                    }\n                }\n            } else {\n                value = value.split(\",\").filter(Boolean).map(Number)\n            }\n        } else if (\n            columnMetadata.type === \"enum\" ||\n            columnMetadata.type === \"simple-enum\"\n        ) {\n            if (columnMetadata.isArray) {\n                if (value === \"{}\") return []\n\n                // manually convert enum array to array of values (pg does not support, see https://github.com/brianc/node-pg-types/issues/56)\n                value = (value as string)\n                    .slice(1, -1)\n                    .split(\",\")\n                    .map((val) => {\n                        // replace double quotes from the beginning and from the end\n                        if (val.startsWith(`\"`) && val.endsWith(`\"`))\n                            val = val.slice(1, -1)\n                        // replace escaped backslash and double quotes\n                        return val.replace(/\\\\(\\\\|\")/g, \"$1\")\n                    })\n\n                // convert to number if that exists in possible enum options\n                value = value.map((val: string) => {\n                    return !isNaN(+val) &&\n                        columnMetadata.enum!.indexOf(parseInt(val)) >= 0\n                        ? parseInt(val)\n                        : val\n                })\n            } else {\n                // convert to number if that exists in possible enum options\n                value =\n                    !isNaN(+value) &&\n                    columnMetadata.enum!.indexOf(parseInt(value)) >= 0\n                        ? parseInt(value)\n                        : value\n            }\n        } else if (columnMetadata.type === Number) {\n            // convert to number if number\n            value = !isNaN(+value) ? parseInt(value) : value\n        }\n\n        if (columnMetadata.transformer)\n            value = ApplyValueTransformers.transformFrom(\n                columnMetadata.transformer,\n                value,\n            )\n        return value\n    }\n\n    /**\n     * Replaces parameters in the given sql with special escaping character\n     * and an array of parameter names to be passed to a query.\n     */\n    escapeQueryWithParameters(\n        sql: string,\n        parameters: ObjectLiteral,\n        nativeParameters: ObjectLiteral,\n    ): [string, any[]] {\n        const escapedParameters: any[] = Object.keys(nativeParameters).map(\n            (key) => nativeParameters[key],\n        )\n        if (!parameters || !Object.keys(parameters).length)\n            return [sql, escapedParameters]\n\n        const parameterIndexMap = new Map<string, number>()\n        sql = sql.replace(\n            /:(\\.\\.\\.)?([A-Za-z0-9_.]+)/g,\n            (full, isArray: string, key: string): string => {\n                if (!parameters.hasOwnProperty(key)) {\n                    return full\n                }\n\n                if (parameterIndexMap.has(key)) {\n                    return this.parametersPrefix + parameterIndexMap.get(key)\n                }\n\n                const value: any = parameters[key]\n\n                if (isArray) {\n                    return value\n                        .map((v: any) => {\n                            escapedParameters.push(v)\n                            return this.createParameter(\n                                key,\n                                escapedParameters.length - 1,\n                            )\n                        })\n                        .join(\", \")\n                }\n\n                if (typeof value === \"function\") {\n                    return value()\n                }\n\n                escapedParameters.push(value)\n                parameterIndexMap.set(key, escapedParameters.length)\n                return this.createParameter(key, escapedParameters.length - 1)\n            },\n        ) // todo: make replace only in value statements, otherwise problems\n        return [sql, escapedParameters]\n    }\n\n    /**\n     * Escapes a column name.\n     */\n    escape(columnName: string): string {\n        return '\"' + columnName + '\"'\n    }\n\n    /**\n     * Build full table name with schema name and table name.\n     * E.g. myDB.mySchema.myTable\n     */\n    buildTableName(tableName: string, schema?: string): string {\n        const tablePath = [tableName]\n\n        if (schema) {\n            tablePath.unshift(schema)\n        }\n\n        return tablePath.join(\".\")\n    }\n\n    /**\n     * Parse a target table name or other types and return a normalized table definition.\n     */\n    parseTableName(\n        target: EntityMetadata | Table | View | TableForeignKey | string,\n    ): { database?: string; schema?: string; tableName: string } {\n        const driverDatabase = this.database\n        const driverSchema = this.schema\n\n        if (InstanceChecker.isTable(target) || InstanceChecker.isView(target)) {\n            const parsed = this.parseTableName(target.name)\n\n            return {\n                database: target.database || parsed.database || driverDatabase,\n                schema: target.schema || parsed.schema || driverSchema,\n                tableName: parsed.tableName,\n            }\n        }\n\n        if (InstanceChecker.isTableForeignKey(target)) {\n            const parsed = this.parseTableName(target.referencedTableName)\n\n            return {\n                database:\n                    target.referencedDatabase ||\n                    parsed.database ||\n                    driverDatabase,\n                schema:\n                    target.referencedSchema || parsed.schema || driverSchema,\n                tableName: parsed.tableName,\n            }\n        }\n\n        if (InstanceChecker.isEntityMetadata(target)) {\n            // EntityMetadata tableName is never a path\n\n            return {\n                database: target.database || driverDatabase,\n                schema: target.schema || driverSchema,\n                tableName: target.tableName,\n            }\n        }\n\n        const parts = target.split(\".\")\n\n        return {\n            database: driverDatabase,\n            schema: (parts.length > 1 ? parts[0] : undefined) || driverSchema,\n            tableName: parts.length > 1 ? parts[1] : parts[0],\n        }\n    }\n\n    /**\n     * Creates a database type from a given column metadata.\n     */\n    normalizeType(column: {\n        type?: ColumnType\n        length?: number | string\n        precision?: number | null\n        scale?: number\n        isArray?: boolean\n    }): string {\n        if (\n            column.type === Number ||\n            column.type === \"int\" ||\n            column.type === \"int4\"\n        ) {\n            return \"integer\"\n        } else if (column.type === String || column.type === \"varchar\") {\n            return \"character varying\"\n        } else if (column.type === Date || column.type === \"timestamp\") {\n            return \"timestamp without time zone\"\n        } else if (column.type === \"timestamptz\") {\n            return \"timestamp with time zone\"\n        } else if (column.type === \"time\") {\n            return \"time without time zone\"\n        } else if (column.type === \"timetz\") {\n            return \"time with time zone\"\n        } else if (column.type === Boolean || column.type === \"bool\") {\n            return \"boolean\"\n        } else if (column.type === \"simple-array\") {\n            return \"text\"\n        } else if (column.type === \"simple-json\") {\n            return \"text\"\n        } else if (column.type === \"simple-enum\") {\n            return \"enum\"\n        } else if (column.type === \"int2\") {\n            return \"smallint\"\n        } else if (column.type === \"int8\") {\n            return \"bigint\"\n        } else if (column.type === \"decimal\") {\n            return \"numeric\"\n        } else if (column.type === \"float8\" || column.type === \"float\") {\n            return \"double precision\"\n        } else if (column.type === \"float4\") {\n            return \"real\"\n        } else if (column.type === \"char\") {\n            return \"character\"\n        } else if (column.type === \"varbit\") {\n            return \"bit varying\"\n        } else {\n            return (column.type as string) || \"\"\n        }\n    }\n\n    /**\n     * Normalizes \"default\" value of the column.\n     */\n    normalizeDefault(columnMetadata: ColumnMetadata): string | undefined {\n        const defaultValue = columnMetadata.default\n\n        if (defaultValue === null || defaultValue === undefined) {\n            return undefined\n        }\n\n        if (columnMetadata.isArray && Array.isArray(defaultValue)) {\n            return `'{${defaultValue\n                .map((val: string) => `${val}`)\n                .join(\",\")}}'`\n        }\n\n        if (\n            (columnMetadata.type === \"enum\" ||\n                columnMetadata.type === \"simple-enum\" ||\n                typeof defaultValue === \"number\" ||\n                typeof defaultValue === \"string\") &&\n            defaultValue !== undefined\n        ) {\n            return `'${defaultValue}'`\n        }\n\n        if (typeof defaultValue === \"boolean\") {\n            return defaultValue ? \"true\" : \"false\"\n        }\n\n        if (typeof defaultValue === \"function\") {\n            const value = defaultValue()\n\n            return this.normalizeDatetimeFunction(value)\n        }\n\n        if (typeof defaultValue === \"object\") {\n            return `'${JSON.stringify(defaultValue)}'`\n        }\n\n        return `${defaultValue}`\n    }\n\n    /**\n     * Compares \"default\" value of the column.\n     * Postgres sorts json values before it is saved, so in that case a deep comparison has to be performed to see if has changed.\n     */\n    private defaultEqual(\n        columnMetadata: ColumnMetadata,\n        tableColumn: TableColumn,\n    ): boolean {\n        if (\n            [\"json\", \"jsonb\"].includes(columnMetadata.type as string) &&\n            ![\"function\", \"undefined\"].includes(typeof columnMetadata.default)\n        ) {\n            const tableColumnDefault =\n                typeof tableColumn.default === \"string\"\n                    ? JSON.parse(\n                          tableColumn.default.substring(\n                              1,\n                              tableColumn.default.length - 1,\n                          ),\n                      )\n                    : tableColumn.default\n\n            return OrmUtils.deepCompare(\n                columnMetadata.default,\n                tableColumnDefault,\n            )\n        }\n\n        const columnDefault = this.lowerDefaultValueIfNecessary(\n            this.normalizeDefault(columnMetadata),\n        )\n        return columnDefault === tableColumn.default\n    }\n\n    /**\n     * Normalizes \"isUnique\" value of the column.\n     */\n    normalizeIsUnique(column: ColumnMetadata): boolean {\n        return column.entityMetadata.uniques.some(\n            (uq) => uq.columns.length === 1 && uq.columns[0] === column,\n        )\n    }\n\n    /**\n     * Returns default column lengths, which is required on column creation.\n     */\n    getColumnLength(column: ColumnMetadata): string {\n        return column.length ? column.length.toString() : \"\"\n    }\n\n    /**\n     * Creates column type definition including length, precision and scale\n     */\n    createFullType(column: TableColumn): string {\n        let type = column.type\n\n        if (column.length) {\n            type += \"(\" + column.length + \")\"\n        } else if (\n            column.precision !== null &&\n            column.precision !== undefined &&\n            column.scale !== null &&\n            column.scale !== undefined\n        ) {\n            type += \"(\" + column.precision + \",\" + column.scale + \")\"\n        } else if (\n            column.precision !== null &&\n            column.precision !== undefined\n        ) {\n            type += \"(\" + column.precision + \")\"\n        }\n\n        if (column.type === \"time without time zone\") {\n            type =\n                \"TIME\" +\n                (column.precision !== null && column.precision !== undefined\n                    ? \"(\" + column.precision + \")\"\n                    : \"\")\n        } else if (column.type === \"time with time zone\") {\n            type =\n                \"TIME\" +\n                (column.precision !== null && column.precision !== undefined\n                    ? \"(\" + column.precision + \")\"\n                    : \"\") +\n                \" WITH TIME ZONE\"\n        } else if (column.type === \"timestamp without time zone\") {\n            type =\n                \"TIMESTAMP\" +\n                (column.precision !== null && column.precision !== undefined\n                    ? \"(\" + column.precision + \")\"\n                    : \"\")\n        } else if (column.type === \"timestamp with time zone\") {\n            type =\n                \"TIMESTAMP\" +\n                (column.precision !== null && column.precision !== undefined\n                    ? \"(\" + column.precision + \")\"\n                    : \"\") +\n                \" WITH TIME ZONE\"\n        } else if (this.spatialTypes.indexOf(column.type as ColumnType) >= 0) {\n            if (column.spatialFeatureType != null && column.srid != null) {\n                type = `${column.type}(${column.spatialFeatureType},${column.srid})`\n            } else if (column.spatialFeatureType != null) {\n                type = `${column.type}(${column.spatialFeatureType})`\n            } else {\n                type = column.type\n            }\n        }\n\n        if (column.isArray) type += \" array\"\n\n        return type\n    }\n\n    /**\n     * Obtains a new database connection to a master server.\n     * Used for replication.\n     * If replication is not setup then returns default connection's database connection.\n     */\n    async obtainMasterConnection(): Promise<[any, Function]> {\n        if (!this.master) {\n            throw new TypeORMError(\"Driver not Connected\")\n        }\n\n        return new Promise((ok, fail) => {\n            this.master.connect((err: any, connection: any, release: any) => {\n                err ? fail(err) : ok([connection, release])\n            })\n        })\n    }\n\n    /**\n     * Obtains a new database connection to a slave server.\n     * Used for replication.\n     * If replication is not setup then returns master (default) connection's database connection.\n     */\n    async obtainSlaveConnection(): Promise<[any, Function]> {\n        if (!this.slaves.length) {\n            return this.obtainMasterConnection()\n        }\n\n        const random = Math.floor(Math.random() * this.slaves.length)\n\n        return new Promise((ok, fail) => {\n            this.slaves[random].connect(\n                (err: any, connection: any, release: any) => {\n                    err ? fail(err) : ok([connection, release])\n                },\n            )\n        })\n    }\n\n    /**\n     * Creates generated map of values generated or returned by database after INSERT query.\n     *\n     * todo: slow. optimize Object.keys(), OrmUtils.mergeDeep and column.createValueMap parts\n     */\n    createGeneratedMap(metadata: EntityMetadata, insertResult: ObjectLiteral) {\n        if (!insertResult) return undefined\n\n        return Object.keys(insertResult).reduce((map, key) => {\n            const column = metadata.findColumnWithDatabaseName(key)\n            if (column) {\n                OrmUtils.mergeDeep(\n                    map,\n                    column.createValueMap(insertResult[key]),\n                )\n                // OrmUtils.mergeDeep(map, column.createValueMap(this.prepareHydratedValue(insertResult[key], column))); // TODO: probably should be like there, but fails on enums, fix later\n            }\n            return map\n        }, {} as ObjectLiteral)\n    }\n\n    /**\n     * Differentiate columns of this table and columns from the given column metadatas columns\n     * and returns only changed.\n     */\n    findChangedColumns(\n        tableColumns: TableColumn[],\n        columnMetadatas: ColumnMetadata[],\n    ): ColumnMetadata[] {\n        return columnMetadatas.filter((columnMetadata) => {\n            const tableColumn = tableColumns.find(\n                (c) => c.name === columnMetadata.databaseName,\n            )\n            if (!tableColumn) return false // we don't need new columns, we only need exist and changed\n\n            const isColumnChanged =\n                tableColumn.name !== columnMetadata.databaseName ||\n                tableColumn.type !== this.normalizeType(columnMetadata) ||\n                tableColumn.length !== columnMetadata.length ||\n                tableColumn.isArray !== columnMetadata.isArray ||\n                tableColumn.precision !== columnMetadata.precision ||\n                (columnMetadata.scale !== undefined &&\n                    tableColumn.scale !== columnMetadata.scale) ||\n                tableColumn.comment !==\n                    this.escapeComment(columnMetadata.comment) ||\n                (!tableColumn.isGenerated &&\n                    !this.defaultEqual(columnMetadata, tableColumn)) || // we included check for generated here, because generated columns already can have default values\n                tableColumn.isPrimary !== columnMetadata.isPrimary ||\n                tableColumn.isNullable !== columnMetadata.isNullable ||\n                tableColumn.isUnique !==\n                    this.normalizeIsUnique(columnMetadata) ||\n                tableColumn.enumName !== columnMetadata.enumName ||\n                (tableColumn.enum &&\n                    columnMetadata.enum &&\n                    !OrmUtils.isArraysEqual(\n                        tableColumn.enum,\n                        columnMetadata.enum.map((val) => val + \"\"),\n                    )) || // enums in postgres are always strings\n                tableColumn.isGenerated !== columnMetadata.isGenerated ||\n                (tableColumn.spatialFeatureType || \"\").toLowerCase() !==\n                    (columnMetadata.spatialFeatureType || \"\").toLowerCase() ||\n                tableColumn.srid !== columnMetadata.srid ||\n                tableColumn.generatedType !== columnMetadata.generatedType ||\n                (tableColumn.asExpression || \"\").trim() !==\n                    (columnMetadata.asExpression || \"\").trim() ||\n                tableColumn.collation !== columnMetadata.collation\n\n            // DEBUG SECTION\n            // if (isColumnChanged) {\n            //     console.log(\"table:\", columnMetadata.entityMetadata.tableName)\n            //     console.log(\n            //         \"name:\",\n            //         tableColumn.name,\n            //         columnMetadata.databaseName,\n            //     )\n            //     console.log(\n            //         \"type:\",\n            //         tableColumn.type,\n            //         this.normalizeType(columnMetadata),\n            //     )\n            //     console.log(\n            //         \"length:\",\n            //         tableColumn.length,\n            //         columnMetadata.length,\n            //     )\n            //     console.log(\n            //         \"isArray:\",\n            //         tableColumn.isArray,\n            //         columnMetadata.isArray,\n            //     )\n            //     console.log(\n            //         \"precision:\",\n            //         tableColumn.precision,\n            //         columnMetadata.precision,\n            //     )\n            //     console.log(\"scale:\", tableColumn.scale, columnMetadata.scale)\n            //     console.log(\n            //         \"comment:\",\n            //         tableColumn.comment,\n            //         this.escapeComment(columnMetadata.comment),\n            //     )\n            //     console.log(\n            //         \"enumName:\",\n            //         tableColumn.enumName,\n            //         columnMetadata.enumName,\n            //     )\n            //     console.log(\n            //         \"enum:\",\n            //         tableColumn.enum &&\n            //             columnMetadata.enum &&\n            //             !OrmUtils.isArraysEqual(\n            //                 tableColumn.enum,\n            //                 columnMetadata.enum.map((val) => val + \"\"),\n            //             ),\n            //     )\n            //     console.log(\n            //         \"isPrimary:\",\n            //         tableColumn.isPrimary,\n            //         columnMetadata.isPrimary,\n            //     )\n            //     console.log(\n            //         \"isNullable:\",\n            //         tableColumn.isNullable,\n            //         columnMetadata.isNullable,\n            //     )\n            //     console.log(\n            //         \"isUnique:\",\n            //         tableColumn.isUnique,\n            //         this.normalizeIsUnique(columnMetadata),\n            //     )\n            //     console.log(\n            //         \"isGenerated:\",\n            //         tableColumn.isGenerated,\n            //         columnMetadata.isGenerated,\n            //     )\n            //     console.log(\n            //         \"generatedType:\",\n            //         tableColumn.generatedType,\n            //         columnMetadata.generatedType,\n            //     )\n            //     console.log(\n            //         \"asExpression:\",\n            //         (tableColumn.asExpression || \"\").trim(),\n            //         (columnMetadata.asExpression || \"\").trim(),\n            //     )\n            //     console.log(\n            //         \"collation:\",\n            //         tableColumn.collation,\n            //         columnMetadata.collation,\n            //     )\n            //     console.log(\n            //         \"isGenerated 2:\",\n            //         !tableColumn.isGenerated &&\n            //             this.lowerDefaultValueIfNecessary(\n            //                 this.normalizeDefault(columnMetadata),\n            //             ) !== tableColumn.default,\n            //     )\n            //     console.log(\n            //         \"spatialFeatureType:\",\n            //         (tableColumn.spatialFeatureType || \"\").toLowerCase(),\n            //         (columnMetadata.spatialFeatureType || \"\").toLowerCase(),\n            //     )\n            //     console.log(\"srid\", tableColumn.srid, columnMetadata.srid)\n            //     console.log(\"==========================================\")\n            // }\n\n            return isColumnChanged\n        })\n    }\n\n    private lowerDefaultValueIfNecessary(value: string | undefined) {\n        // Postgres saves function calls in default value as lowercase #2733\n        if (!value) {\n            return value\n        }\n        return value\n            .split(`'`)\n            .map((v, i) => {\n                return i % 2 === 1 ? v : v.toLowerCase()\n            })\n            .join(`'`)\n    }\n\n    /**\n     * Returns true if driver supports RETURNING / OUTPUT statement.\n     */\n    isReturningSqlSupported(): boolean {\n        return true\n    }\n\n    /**\n     * Returns true if driver supports uuid values generation on its own.\n     */\n    isUUIDGenerationSupported(): boolean {\n        return true\n    }\n\n    /**\n     * Returns true if driver supports fulltext indices.\n     */\n    isFullTextColumnTypeSupported(): boolean {\n        return false\n    }\n\n    get uuidGenerator(): string {\n        return this.options.uuidExtension === \"pgcrypto\"\n            ? \"gen_random_uuid()\"\n            : \"uuid_generate_v4()\"\n    }\n\n    /**\n     * Creates an escaped parameter.\n     */\n    createParameter(parameterName: string, index: number): string {\n        return this.parametersPrefix + (index + 1)\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Loads postgres query stream package.\n     */\n    loadStreamDependency() {\n        try {\n            return PlatformTools.load(\"pg-query-stream\")\n        } catch (e) {\n            // todo: better error for browser env\n            throw new TypeORMError(\n                `To use streams you should install pg-query-stream package. Please run npm i pg-query-stream --save command.`,\n            )\n        }\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * If driver dependency is not given explicitly, then try to load it via \"require\".\n     */\n    protected loadDependencies(): void {\n        try {\n            const postgres = this.options.driver || PlatformTools.load(\"pg\")\n            this.postgres = postgres\n            try {\n                const pgNative =\n                    this.options.nativeDriver || PlatformTools.load(\"pg-native\")\n                if (pgNative && this.postgres.native)\n                    this.postgres = this.postgres.native\n            } catch (e) {}\n        } catch (e) {\n            // todo: better error for browser env\n            throw new DriverPackageNotInstalledError(\"Postgres\", \"pg\")\n        }\n    }\n\n    /**\n     * Creates a new connection pool for a given database credentials.\n     */\n    protected async createPool(\n        options: PostgresConnectionOptions,\n        credentials: PostgresConnectionCredentialsOptions,\n    ): Promise<any> {\n        const { logger } = this.connection\n        credentials = Object.assign({}, credentials)\n\n        // build connection options for the driver\n        // See: https://github.com/brianc/node-postgres/tree/master/packages/pg-pool#create\n        const connectionOptions = Object.assign(\n            {},\n            {\n                connectionString: credentials.url,\n                host: credentials.host,\n                user: credentials.username,\n                password: credentials.password,\n                database: credentials.database,\n                port: credentials.port,\n                ssl: credentials.ssl,\n                connectionTimeoutMillis: options.connectTimeoutMS,\n                application_name:\n                    options.applicationName ?? credentials.applicationName,\n                max: options.poolSize,\n            },\n            options.extra || {},\n        )\n\n        if (options.parseInt8 !== undefined) {\n            if (\n                this.postgres.defaults &&\n                Object.getOwnPropertyDescriptor(\n                    this.postgres.defaults,\n                    \"parseInt8\",\n                )?.set\n            ) {\n                this.postgres.defaults.parseInt8 = options.parseInt8\n            } else {\n                logger.log(\n                    \"warn\",\n                    \"Attempted to set parseInt8 option, but the postgres driver does not support setting defaults.parseInt8. This option will be ignored.\",\n                )\n            }\n        }\n\n        // create a connection pool\n        const pool = new this.postgres.Pool(connectionOptions)\n\n        const poolErrorHandler =\n            options.poolErrorHandler ||\n            ((error: any) =>\n                logger.log(\"warn\", `Postgres pool raised an error. ${error}`))\n\n        /*\n          Attaching an error handler to pool errors is essential, as, otherwise, errors raised will go unhandled and\n          cause the hosting app to crash.\n         */\n        pool.on(\"error\", poolErrorHandler)\n\n        return new Promise((ok, fail) => {\n            pool.connect((err: any, connection: any, release: Function) => {\n                if (err) return fail(err)\n\n                if (options.logNotifications) {\n                    connection.on(\"notice\", (msg: any) => {\n                        msg && this.connection.logger.log(\"info\", msg.message)\n                    })\n                    connection.on(\"notification\", (msg: any) => {\n                        msg &&\n                            this.connection.logger.log(\n                                \"info\",\n                                `Received NOTIFY on channel ${msg.channel}: ${msg.payload}.`,\n                            )\n                    })\n                }\n                release()\n                ok(pool)\n            })\n        })\n    }\n\n    /**\n     * Closes connection pool.\n     */\n    protected async closePool(pool: any): Promise<void> {\n        while (this.connectedQueryRunners.length) {\n            await this.connectedQueryRunners[0].release()\n        }\n\n        return new Promise<void>((ok, fail) => {\n            pool.end((err: any) => (err ? fail(err) : ok()))\n        })\n    }\n\n    /**\n     * Executes given query.\n     */\n    protected executeQuery(connection: any, query: string) {\n        this.connection.logger.logQuery(query)\n\n        return new Promise((ok, fail) => {\n            connection.query(query, (err: any, result: any) =>\n                err ? fail(err) : ok(result),\n            )\n        })\n    }\n\n    /**\n     * If parameter is a datetime function, e.g. \"CURRENT_TIMESTAMP\", normalizes it.\n     * Otherwise returns original input.\n     */\n    protected normalizeDatetimeFunction(value: string) {\n        // check if input is datetime function\n        const upperCaseValue = value.toUpperCase()\n        const isDatetimeFunction =\n            upperCaseValue.indexOf(\"CURRENT_TIMESTAMP\") !== -1 ||\n            upperCaseValue.indexOf(\"CURRENT_DATE\") !== -1 ||\n            upperCaseValue.indexOf(\"CURRENT_TIME\") !== -1 ||\n            upperCaseValue.indexOf(\"LOCALTIMESTAMP\") !== -1 ||\n            upperCaseValue.indexOf(\"LOCALTIME\") !== -1\n\n        if (isDatetimeFunction) {\n            // extract precision, e.g. \"(3)\"\n            const precision = value.match(/\\(\\d+\\)/)\n\n            if (upperCaseValue.indexOf(\"CURRENT_TIMESTAMP\") !== -1) {\n                return precision\n                    ? `('now'::text)::timestamp${precision[0]} with time zone`\n                    : \"now()\"\n            } else if (upperCaseValue === \"CURRENT_DATE\") {\n                return \"('now'::text)::date\"\n            } else if (upperCaseValue.indexOf(\"CURRENT_TIME\") !== -1) {\n                return precision\n                    ? `('now'::text)::time${precision[0]} with time zone`\n                    : \"('now'::text)::time with time zone\"\n            } else if (upperCaseValue.indexOf(\"LOCALTIMESTAMP\") !== -1) {\n                return precision\n                    ? `('now'::text)::timestamp${precision[0]} without time zone`\n                    : \"('now'::text)::timestamp without time zone\"\n            } else if (upperCaseValue.indexOf(\"LOCALTIME\") !== -1) {\n                return precision\n                    ? `('now'::text)::time${precision[0]} without time zone`\n                    : \"('now'::text)::time without time zone\"\n            }\n        }\n\n        return value\n    }\n\n    /**\n     * Escapes a given comment.\n     */\n    protected escapeComment(comment?: string) {\n        if (!comment) return comment\n\n        comment = comment.replace(/\\u0000/g, \"\") // Null bytes aren't allowed in comments\n\n        return comment\n    }\n}\n"], "sourceRoot": "../.."}