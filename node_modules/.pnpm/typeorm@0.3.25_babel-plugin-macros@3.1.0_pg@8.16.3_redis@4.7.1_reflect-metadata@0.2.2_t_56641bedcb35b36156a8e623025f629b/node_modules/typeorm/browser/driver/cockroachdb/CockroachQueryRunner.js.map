{"version": 3, "sources": ["../browser/src/driver/cockroachdb/CockroachQueryRunner.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,MAAM,aAAa,CAAA;AAC1C,OAAO,EAAE,gBAAgB,EAAE,MAAM,8BAA8B,CAAA;AAC/D,OAAO,EAAE,+BAA+B,EAAE,MAAM,6CAA6C,CAAA;AAC7F,OAAO,EAAE,0BAA0B,EAAE,MAAM,wCAAwC,CAAA;AAEnF,OAAO,EAAE,eAAe,EAAE,MAAM,oCAAoC,CAAA;AACpE,OAAO,EAAE,WAAW,EAAE,MAAM,gCAAgC,CAAA;AAG5D,OAAO,EAAE,KAAK,EAAE,MAAM,kCAAkC,CAAA;AACxD,OAAO,EAAE,UAAU,EAAE,MAAM,uCAAuC,CAAA;AAClE,OAAO,EAAE,WAAW,EAAE,MAAM,wCAAwC,CAAA;AACpE,OAAO,EAAE,cAAc,EAAE,MAAM,2CAA2C,CAAA;AAC1E,OAAO,EAAE,eAAe,EAAE,MAAM,4CAA4C,CAAA;AAC5E,OAAO,EAAE,UAAU,EAAE,MAAM,uCAAuC,CAAA;AAClE,OAAO,EAAE,WAAW,EAAE,MAAM,wCAAwC,CAAA;AACpE,OAAO,EAAE,IAAI,EAAE,MAAM,gCAAgC,CAAA;AACrD,OAAO,EAAE,WAAW,EAAE,MAAM,8BAA8B,CAAA;AAC1D,OAAO,EAAE,iBAAiB,EAAE,MAAM,oCAAoC,CAAA;AACtE,OAAO,EAAE,eAAe,EAAE,MAAM,4BAA4B,CAAA;AAC5D,OAAO,EAAE,QAAQ,EAAE,MAAM,qBAAqB,CAAA;AAC9C,OAAO,EAAE,YAAY,EAAE,MAAM,yBAAyB,CAAA;AACtD,OAAO,EAAE,KAAK,EAAE,MAAM,UAAU,CAAA;AAGhC,OAAO,EAAE,iBAAiB,EAAE,MAAM,4BAA4B,CAAA;AAI9D;;GAEG;AACH,MAAM,OAAO,oBACT,SAAQ,eAAe;IAyCvB,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAY,MAAuB,EAAE,IAAqB;QACtD,KAAK,EAAE,CAAA;QApBX;;WAEG;QACO,YAAO,GAA4C,EAAE,CAAA;QAE/D;;WAEG;QACO,iBAAY,GAAY,KAAK,CAAA;QAEvC;;WAEG;QACO,uBAAkB,GAAW,CAAC,CAAA;QAQpC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACpB,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,CAAA;QACnC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC,WAAW,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,CAAA;IAC5C,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;;OAGG;IACH,OAAO;QACH,IAAI,IAAI,CAAC,kBAAkB;YACvB,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAA;QAEnD,IAAI,IAAI,CAAC,yBAAyB;YAC9B,OAAO,IAAI,CAAC,yBAAyB,CAAA;QAEzC,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;YACpD,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,MAAM;iBACvC,qBAAqB,EAAE;iBACvB,IAAI,CAAC,CAAC,CAAC,UAAU,EAAE,OAAO,CAAQ,EAAE,EAAE;gBACnC,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;gBAC5C,IAAI,CAAC,kBAAkB,GAAG,UAAU,CAAA;gBAEpC,MAAM,eAAe,GAAG,CAAC,GAAU,EAAE,EAAE,CACnC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAA;gBAC/B,IAAI,CAAC,eAAe,GAAG,CAAC,GAAW,EAAE,EAAE;oBACnC,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAClC,OAAO,EACP,eAAe,CAClB,CAAA;oBACD,OAAO,CAAC,GAAG,CAAC,CAAA;gBAChB,CAAC,CAAA;gBACD,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC,OAAO,EAAE,eAAe,CAAC,CAAA;gBAEpD,OAAO,IAAI,CAAC,kBAAkB,CAAA;YAClC,CAAC,CAAC,CAAA;QACV,CAAC;aAAM,CAAC;YACJ,SAAS;YACT,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,MAAM;iBACvC,sBAAsB,EAAE;iBACxB,IAAI,CAAC,CAAC,CAAC,UAAU,EAAE,OAAO,CAAQ,EAAE,EAAE;gBACnC,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;gBAC5C,IAAI,CAAC,kBAAkB,GAAG,UAAU,CAAA;gBAEpC,MAAM,eAAe,GAAG,CAAC,GAAU,EAAE,EAAE,CACnC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAA;gBAC/B,IAAI,CAAC,eAAe,GAAG,CAAC,GAAW,EAAE,EAAE;oBACnC,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAClC,OAAO,EACP,eAAe,CAClB,CAAA;oBACD,OAAO,CAAC,GAAG,CAAC,CAAA;gBAChB,CAAC,CAAA;gBACD,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC,OAAO,EAAE,eAAe,CAAC,CAAA;gBAEpD,OAAO,IAAI,CAAC,kBAAkB,CAAA;YAClC,CAAC,CAAC,CAAA;QACV,CAAC;QAED,OAAO,IAAI,CAAC,yBAAyB,CAAA;IACzC,CAAC;IAED;;;OAGG;IACK,KAAK,CAAC,iBAAiB,CAAC,GAAW;QACvC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,OAAM;QACV,CAAC;QAED,IAAI,CAAC,UAAU,GAAG,IAAI,CAAA;QACtB,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAA;YACzB,IAAI,CAAC,eAAe,GAAG,SAAS,CAAA;QACpC,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;QAE7D,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;QACtD,CAAC;IACL,CAAC;IAED;;;OAGG;IACH,OAAO;QACH,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAA;IACnC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAAC,cAA+B;QAClD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAA;QAC/B,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAA;QAC3B,IAAI,CAAC;YACD,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAA;QAC9D,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAA;YAChC,MAAM,GAAG,CAAA;QACb,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,KAAK,CAAC,EAAE,CAAC;YAC9B,MAAM,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAA;YACrC,MAAM,IAAI,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAA;YAC/C,IAAI,cAAc,EAAE,CAAC;gBACjB,MAAM,IAAI,CAAC,KAAK,CACZ,kCAAkC,GAAG,cAAc,CACtD,CAAA;YACL,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,MAAM,IAAI,CAAC,KAAK,CAAC,qBAAqB,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAA;QAClE,CAAC;QAED,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAA;QAC1B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAA;QAExB,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAA;IAC7D,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,iBAAiB;QACnB,IAAI,CAAC,IAAI,CAAC,mBAAmB;YAAE,MAAM,IAAI,0BAA0B,EAAE,CAAA;QAErE,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAA;QAE3D,IAAI,IAAI,CAAC,gBAAgB,GAAG,CAAC,EAAE,CAAC;YAC5B,MAAM,IAAI,CAAC,KAAK,CACZ,6BAA6B,IAAI,CAAC,gBAAgB,GAAG,CAAC,EAAE,CAC3D,CAAA;YACD,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAA;QAC9B,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,YAAY,GAAG,KAAK,CAAA;YACzB,MAAM,IAAI,CAAC,KAAK,CAAC,qCAAqC,CAAC,CAAA;YACvD,MAAM,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;YAC1B,IAAI,CAAC,OAAO,GAAG,EAAE,CAAA;YACjB,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAA;YAChC,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAA;YAC3B,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAA;QAC9B,CAAC;QAED,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAA;IAC9D,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,mBAAmB;QACrB,IAAI,CAAC,IAAI,CAAC,mBAAmB;YAAE,MAAM,IAAI,0BAA0B,EAAE,CAAA;QAErE,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAA;QAE7D,IAAI,IAAI,CAAC,gBAAgB,GAAG,CAAC,EAAE,CAAC;YAC5B,MAAM,IAAI,CAAC,KAAK,CACZ,iCAAiC,IAAI,CAAC,gBAAgB,GAAG,CAAC,EAAE,CAC/D,CAAA;QACL,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,YAAY,GAAG,KAAK,CAAA;YACzB,MAAM,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAA;YAC5B,IAAI,CAAC,OAAO,GAAG,EAAE,CAAA;YACjB,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAA;YAChC,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAA;QAC/B,CAAC;QACD,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAA;QAE1B,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAA;IAChE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK,CACP,KAAa,EACb,UAAkB,EAClB,mBAAmB,GAAG,KAAK;QAE3B,IAAI,IAAI,CAAC,UAAU;YAAE,MAAM,IAAI,+BAA+B,EAAE,CAAA;QAEhE,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAA;QAE/C,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;QAC/D,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,aAAa,EAAE,KAAK,EAAE,UAAU,CAAC,CAAA;QAElE,MAAM,iBAAiB,GAAG,IAAI,iBAAiB,EAAE,CAAA;QACjD,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAEjC,IAAI,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YAChD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,CAAA;QAC5C,CAAC;QAED,IAAI,CAAC;YACD,MAAM,GAAG,GAAG,MAAM,IAAI,OAAO,CAAM,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;gBAC5C,kBAAkB,CAAC,KAAK,CACpB,KAAK,EACL,UAAU,EACV,CAAC,GAAQ,EAAE,GAAQ,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CACtD,CAAA;YACL,CAAC,CAAC,CAAA;YAEF,oDAAoD;YACpD,MAAM,qBAAqB,GACvB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,qBAAqB,CAAA;YAC7C,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;YAC/B,MAAM,kBAAkB,GAAG,YAAY,GAAG,cAAc,CAAA;YACxD,IACI,qBAAqB;gBACrB,kBAAkB,GAAG,qBAAqB,EAC5C,CAAC;gBACC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,YAAY,CACtC,kBAAkB,EAClB,KAAK,EACL,UAAU,EACV,IAAI,CACP,CAAA;YACL,CAAC;YAED,MAAM,MAAM,GAAG,IAAI,WAAW,EAAE,CAAA;YAEhC,IAAI,GAAG,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,CAAC;gBACjC,MAAM,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAA;YAClC,CAAC;YAED,IAAI,GAAG,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC7B,MAAM,CAAC,OAAO,GAAG,GAAG,CAAC,IAAI,CAAA;YAC7B,CAAC;YAED,QAAQ,GAAG,CAAC,OAAO,EAAE,CAAC;gBAClB,KAAK,QAAQ;oBACT,+DAA+D;oBAC/D,MAAM,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAA;oBACrC,MAAK;gBACT;oBACI,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC,IAAI,CAAA;YAC7B,CAAC;YAED,IAAI,CAAC,WAAW,CAAC,wBAAwB,CACrC,iBAAiB,EACjB,KAAK,EACL,UAAU,EACV,IAAI,EACJ,kBAAkB,EAClB,GAAG,EACH,SAAS,CACZ,CAAA;YAED,IAAI,mBAAmB,EAAE,CAAC;gBACtB,OAAO,MAAM,CAAA;YACjB,CAAC;iBAAM,CAAC;gBACJ,OAAO,MAAM,CAAC,GAAG,CAAA;YACrB,CAAC;QACL,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,IACI,GAAG,CAAC,IAAI,KAAK,OAAO;gBACpB,IAAI,CAAC,mBAAmB;gBACxB,IAAI,CAAC,kBAAkB;oBACnB,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,qBAAqB,IAAI,CAAC,CAAC,EACtD,CAAC;gBACC,IAAI,CAAC,kBAAkB,IAAI,CAAC,CAAA;gBAC5B,IAAI,CAAC,YAAY,GAAG,KAAK,CAAA;gBACzB,MAAM,IAAI,CAAC,KAAK,CAAC,yCAAyC,CAAC,CAAA;gBAC3D,MAAM,SAAS,GACX,CAAC,IAAI,IAAI,CAAC,kBAAkB;oBAC5B,GAAG;oBACH,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC;oBACrB,IAAI,CAAA;gBACR,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,CAAA;gBAE9D,IAAI,MAAM,GAAG,SAAS,CAAA;gBACtB,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;oBAC3B,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAClC,mCAAmC,CAAC,CAAC,KAAK,GAAG,EAC7C,CAAC,CAAC,UAAU,EACZ,IAAI,CACP,CAAA;oBACD,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,UAAU,CAAC,CAAA;gBACpD,CAAC;gBACD,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAA;gBAC3B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAA;gBAExB,OAAO,MAAM,CAAA;YACjB,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa,CACvC,GAAG,EACH,KAAK,EACL,UAAU,EACV,IAAI,CACP,CAAA;gBACD,IAAI,CAAC,WAAW,CAAC,wBAAwB,CACrC,iBAAiB,EACjB,KAAK,EACL,UAAU,EACV,KAAK,EACL,SAAS,EACT,SAAS,EACT,GAAG,CACN,CAAA;gBACD,MAAM,IAAI,gBAAgB,CAAC,KAAK,EAAE,UAAU,EAAE,GAAG,CAAC,CAAA;YACtD,CAAC;QACL,CAAC;gBAAS,CAAC;YACP,MAAM,iBAAiB,CAAC,IAAI,EAAE,CAAA;QAClC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM,CACR,KAAa,EACb,UAAkB,EAClB,KAAgB,EAChB,OAAkB;QAElB,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE,CAAA;QACtD,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,MAAM,IAAI,+BAA+B,EAAE,CAAA;QAC/C,CAAC;QAED,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAA;QAC/C,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;QAC/D,MAAM,MAAM,GAAG,kBAAkB,CAAC,KAAK,CACnC,IAAI,WAAW,CAAC,KAAK,EAAE,UAAU,CAAC,CACrC,CAAA;QAED,IAAI,KAAK,EAAE,CAAC;YACR,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAC3B,CAAC;QAED,IAAI,OAAO,EAAE,CAAC;YACV,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;QAC/B,CAAC;QAED,OAAO,MAAM,CAAA;IACjB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY;QACd,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAA;IAC9B,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,UAAU,CAAC,QAAiB;QAC9B,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAA;IAC9B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,QAAgB;QAC9B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAC3B,kDAAkD,QAAQ,GAAG,CAChE,CAAA;QACD,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAA;IACvC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB;QACpB,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,kCAAkC,CAAC,CAAA;QAClE,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAA;IACvC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CAAC,MAAc;QAC1B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAC3B,wEAAwE,MAAM,GAAG,CACpF,CAAA;QACD,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAA;IACvC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB;QAClB,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAA;QAChE,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAA;IACrC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ,CAAC,WAA2B;QACtC,MAAM,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAE/D,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC;YAC1B,eAAe,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;QAC1D,CAAC;QAED,MAAM,GAAG,GAAG,uEAAuE,eAAe,CAAC,MAAM,yBAAyB,eAAe,CAAC,SAAS,GAAG,CAAA;QAC9J,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QACpC,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAA;IACvC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CACX,WAA2B,EAC3B,UAAkB;QAElB,MAAM,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAE/D,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC;YAC1B,eAAe,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;QAC1D,CAAC;QAED,MAAM,GAAG,GAAG,wEAAwE,eAAe,CAAC,MAAM,yBAAyB,eAAe,CAAC,SAAS,0BAA0B,UAAU,GAAG,CAAA;QACnM,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QACpC,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAA;IACvC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAChB,QAAgB,EAChB,UAAoB;QAEpB,MAAM,EAAE,GAAG,mBACP,UAAU,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,EACpC,KAAK,QAAQ,GAAG,CAAA;QAChB,MAAM,IAAI,GAAG,kBAAkB,QAAQ,GAAG,CAAA;QAC1C,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAA;IAC7D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,QAAgB,EAAE,OAAiB;QAClD,MAAM,EAAE,GAAG,iBAAiB,OAAO,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,KAAK,QAAQ,GAAG,CAAA;QACvE,MAAM,IAAI,GAAG,oBAAoB,QAAQ,GAAG,CAAA;QAC5C,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAA;IAC7D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CACd,UAAkB,EAClB,UAAoB;QAEpB,MAAM,MAAM,GACR,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAC1B,CAAC,CAAC,UAAU;YACZ,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;QAElC,MAAM,EAAE,GAAG,UAAU;YACjB,CAAC,CAAC,gCAAgC,MAAM,GAAG;YAC3C,CAAC,CAAC,kBAAkB,MAAM,GAAG,CAAA;QACjC,MAAM,IAAI,GAAG,gBAAgB,MAAM,WAAW,CAAA;QAC9C,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAA;IAC7D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CACZ,UAAkB,EAClB,OAAiB,EACjB,SAAmB;QAEnB,MAAM,MAAM,GACR,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAC1B,CAAC,CAAC,UAAU;YACZ,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;QAElC,MAAM,EAAE,GAAG,OAAO;YACd,CAAC,CAAC,0BAA0B,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE;YACnE,CAAC,CAAC,gBAAgB,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAA;QAC7D,MAAM,IAAI,GAAG,kBAAkB,MAAM,GAAG,CAAA;QACxC,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAA;IAC7D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACb,KAAY,EACZ,aAAsB,KAAK,EAC3B,oBAA6B,IAAI,EACjC,gBAAyB,IAAI;QAE7B,IAAI,UAAU,EAAE,CAAC;YACb,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;YAC/C,IAAI,YAAY;gBAAE,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;QAC9C,CAAC;QACD,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAE/B,6EAA6E;QAC7E,MAAM,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CACpC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa,CACtE,CAAA;QACD,MAAM,gBAAgB,GAAa,EAAE,CAAA;QACrC,KAAK,MAAM,MAAM,IAAI,WAAW,EAAE,CAAC;YAC/B,2EAA2E;YAC3E,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;YACrD,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;YAElD,8FAA8F;YAC9F,IAAI,CAAC,OAAO,IAAI,gBAAgB,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;gBACxD,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;gBAC/B,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAA;gBAC/D,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAA;YACnE,CAAC;QACL,CAAC;QAED,KAAK,CAAC,OAAO;aACR,MAAM,CACH,CAAC,MAAM,EAAE,EAAE,CACP,MAAM,CAAC,WAAW;YAClB,MAAM,CAAC,kBAAkB,KAAK,WAAW,CAChD;aACA,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YAChB,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,mBAAmB,IAAI,CAAC,UAAU,CAC9B,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,MAAM,CAAC,CACxC,EAAE,CACN,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,iBAAiB,IAAI,CAAC,UAAU,CAC5B,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,MAAM,CAAC,CACxC,EAAE,CACN,CACJ,CAAA;QACL,CAAC,CAAC,CAAA;QAEN,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,iBAAiB,CAAC,CAAC,CAAA;QAC7D,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAA;QAE1C,iFAAiF;QACjF,kIAAkI;QAClI,IAAI,iBAAiB;YACjB,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE,CACrC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,CAC9D,CAAA;QAEL,IAAI,aAAa,EAAE,CAAC;YAChB,KAAK,CAAC,OAAO;iBACR,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC;iBAClC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;gBACf,sFAAsF;gBACtF,IAAI,CAAC,KAAK,CAAC,IAAI;oBACX,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CACjD,KAAK,EACL,KAAK,CAAC,WAAW,EACjB,KAAK,CAAC,KAAK,CACd,CAAA;gBACL,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAA;gBACjD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAA;YACrD,CAAC,CAAC,CAAA;QACV,CAAC;QAED,6FAA6F;QAC7F,MAAM,gBAAgB,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CACzC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,aAAa,IAAI,MAAM,CAAC,YAAY,CAC1D,CAAA;QAED,KAAK,MAAM,MAAM,IAAI,gBAAgB,EAAE,CAAC;YACpC,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;YACnD,IAAI,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;YAClD,IAAI,CAAC,MAAM,EAAE,CAAC;gBACV,MAAM,GAAG,aAAa,CAAA;YAC1B,CAAC;YAED,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,MAAM,EAAE,MAAM;gBACd,KAAK,EAAE,KAAK,CAAC,IAAI;gBACjB,IAAI,EAAE,iBAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,KAAK,EAAE,MAAM,CAAC,YAAY;aAC7B,CAAC,CAAA;YAEF,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,MAAM,EAAE,MAAM;gBACd,KAAK,EAAE,KAAK,CAAC,IAAI;gBACjB,IAAI,EAAE,iBAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;aACpB,CAAC,CAAA;YAEF,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YAC3B,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QACjC,CAAC;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;IACrD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CACX,MAAsB,EACtB,OAAiB,EACjB,kBAA2B,IAAI,EAC/B,cAAuB,IAAI;QAE3B,qGAAqG;QACrG,wDAAwD;QACxD,IAAI,OAAO,EAAE,CAAC;YACV,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;YAChD,IAAI,CAAC,YAAY;gBAAE,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;QAC/C,CAAC;QAED,8FAA8F;QAC9F,MAAM,iBAAiB,GAAY,eAAe,CAAA;QAClD,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAA;QAC3C,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAA;QAClD,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAE/B,4EAA4E;QAC5E,IAAI,eAAe;YACf,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE,CACrC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,CAC5D,CAAA;QAEL,IAAI,WAAW,EAAE,CAAC;YACd,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;gBAC5B,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAA;gBAC/C,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAA;YACvD,CAAC,CAAC,CAAA;QACN,CAAC;QAED,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAA;QACxC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,iBAAiB,CAAC,CAAC,CAAA;QAE/D,KAAK,CAAC,OAAO;aACR,MAAM,CACH,CAAC,MAAM,EAAE,EAAE,CACP,MAAM,CAAC,WAAW;YAClB,MAAM,CAAC,kBAAkB,KAAK,WAAW,CAChD;aACA,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YAChB,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,iBAAiB,IAAI,CAAC,UAAU,CAC5B,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,MAAM,CAAC,CACxC,EAAE,CACN,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,mBAAmB,IAAI,CAAC,UAAU,CAC9B,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,MAAM,CAAC,CACxC,EAAE,CACN,CACJ,CAAA;QACL,CAAC,CAAC,CAAA;QAEN,kGAAkG;QAClG,MAAM,gBAAgB,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CACzC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,aAAa,IAAI,MAAM,CAAC,YAAY,CAC1D,CAAA;QAED,KAAK,MAAM,MAAM,IAAI,gBAAgB,EAAE,CAAC;YACpC,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;YACnD,IAAI,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;YAClD,IAAI,CAAC,MAAM,EAAE,CAAC;gBACV,MAAM,GAAG,aAAa,CAAA;YAC1B,CAAC;YAED,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,MAAM,EAAE,MAAM;gBACd,KAAK,EAAE,KAAK,CAAC,IAAI;gBACjB,IAAI,EAAE,iBAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;aACpB,CAAC,CAAA;YAEF,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,MAAM,EAAE,MAAM;gBACd,KAAK,EAAE,KAAK,CAAC,IAAI;gBACjB,IAAI,EAAE,iBAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,KAAK,EAAE,MAAM,CAAC,YAAY;aAC7B,CAAC,CAAA;YAEF,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YAC3B,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QACjC,CAAC;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;IACrD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CACZ,IAAU,EACV,mBAA4B,KAAK;QAEjC,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAC/B,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAA;QACxC,IAAI,gBAAgB;YAChB,SAAS,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAA;QAC5D,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAA;QACxC,IAAI,gBAAgB;YAChB,WAAW,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAA;QAC9D,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;IACrD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ,CAAC,MAAqB;QAChC,MAAM,QAAQ,GAAG,eAAe,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAA;QACtE,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAA;QAE/C,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAC/B,SAAS,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAA;QACxD,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAA;QACtC,WAAW,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAA;QAC1D,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAA;QAC1C,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;IACrD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACb,cAA8B,EAC9B,YAAoB;QAEpB,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAC/B,MAAM,QAAQ,GAAG,eAAe,CAAC,OAAO,CAAC,cAAc,CAAC;YACpD,CAAC,CAAC,cAAc;YAChB,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAA;QAC/C,MAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAA;QAEjC,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,YAAY,EAAE,GACjD,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAA;QAExC,QAAQ,CAAC,IAAI,GAAG,UAAU;YACtB,CAAC,CAAC,GAAG,UAAU,IAAI,YAAY,EAAE;YACjC,CAAC,CAAC,YAAY,CAAA;QAElB,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,QAAQ,CACX,eAAe,YAAY,GAAG,CAClC,CACJ,CAAA;QACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,QAAQ,CACX,eAAe,YAAY,GAAG,CAClC,CACJ,CAAA;QAED,uCAAuC;QACvC,IACI,QAAQ,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC;YAClC,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB,EACtD,CAAC;YACC,MAAM,WAAW,GAAG,QAAQ,CAAC,cAAc,CAAC,GAAG,CAC3C,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAC1B,CAAA;YAED,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CAC3D,QAAQ,EACR,WAAW,CACd,CAAA;YACD,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CAC3D,QAAQ,EACR,WAAW,CACd,CAAA;YAED,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,QAAQ,CACX,uBAAuB,SAAS,SAAS,SAAS,GAAG,CACzD,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,QAAQ,CACX,uBAAuB,SAAS,SAAS,SAAS,GAAG,CACzD,CACJ,CAAA;QACL,CAAC;QAED,4BAA4B;QAC5B,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YAChC,MAAM,aAAa,GACf,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CAC/C,QAAQ,EACR,MAAM,CAAC,WAAW,CACrB,CAAA;YAEL,2DAA2D;YAC3D,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa;gBAAE,OAAM;YAEzC,4BAA4B;YAC5B,MAAM,aAAa,GACf,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CAC/C,QAAQ,EACR,MAAM,CAAC,WAAW,CACrB,CAAA;YAEL,gBAAgB;YAChB,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,QAAQ,CACX,uBACG,MAAM,CAAC,IACX,SAAS,aAAa,GAAG,CAC5B,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,QAAQ,CACX,uBAAuB,aAAa,SACjC,MAAM,CAAC,IACX,GAAG,CACN,CACJ,CAAA;YAED,0BAA0B;YAC1B,MAAM,CAAC,IAAI,GAAG,aAAa,CAAA;QAC/B,CAAC,CAAC,CAAA;QAEF,2BAA2B;QAC3B,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAC/B,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CACzD,QAAQ,EACR,KAAK,CAAC,WAAW,EACjB,KAAK,CAAC,KAAK,CACd,CAAA;YAED,0DAA0D;YAC1D,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY;gBAAE,OAAM;YAEvC,4BAA4B;YAC5B,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAA;YACvD,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CACzD,QAAQ,EACR,KAAK,CAAC,WAAW,EACjB,KAAK,CAAC,KAAK,CACd,CAAA;YAED,gBAAgB;YAChB,MAAM,EAAE,GAAG,MAAM;gBACb,CAAC,CAAC,gBAAgB,MAAM,MAAM,KAAK,CAAC,IAAI,gBAAgB,YAAY,GAAG;gBACvE,CAAC,CAAC,gBAAgB,KAAK,CAAC,IAAI,gBAAgB,YAAY,GAAG,CAAA;YAC/D,MAAM,IAAI,GAAG,MAAM;gBACf,CAAC,CAAC,gBAAgB,MAAM,MAAM,YAAY,gBAAgB,KAAK,CAAC,IAAI,GAAG;gBACvE,CAAC,CAAC,gBAAgB,YAAY,gBAAgB,KAAK,CAAC,IAAI,GAAG,CAAA;YAC/D,SAAS,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,CAAC,CAAA;YAC7B,WAAW,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAA;YAEjC,0BAA0B;YAC1B,KAAK,CAAC,IAAI,GAAG,YAAY,CAAA;QAC7B,CAAC,CAAC,CAAA;QAEF,iCAAiC;QACjC,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;YACxC,MAAM,iBAAiB,GACnB,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,QAAQ,EACR,UAAU,CAAC,WAAW,EACtB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAC7B,UAAU,CAAC,qBAAqB,CACnC,CAAA;YAEL,gEAAgE;YAChE,IAAI,UAAU,CAAC,IAAI,KAAK,iBAAiB;gBAAE,OAAM;YAEjD,4BAA4B;YAC5B,MAAM,iBAAiB,GACnB,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,QAAQ,EACR,UAAU,CAAC,WAAW,EACtB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAC7B,UAAU,CAAC,qBAAqB,CACnC,CAAA;YAEL,gBAAgB;YAChB,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,QAAQ,CACX,uBACG,UAAU,CAAC,IACf,SAAS,iBAAiB,GAAG,CAChC,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,QAAQ,CACX,uBAAuB,iBAAiB,SACrC,UAAU,CAAC,IACf,GAAG,CACN,CACJ,CAAA;YAED,0BAA0B;YAC1B,UAAU,CAAC,IAAI,GAAG,iBAAiB,CAAA;QACvC,CAAC,CAAC,CAAA;QAEF,oBAAoB;QACpB,MAAM,WAAW,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CACvC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa,CACtE,CAAA;QACD,KAAK,MAAM,MAAM,IAAI,WAAW,EAAE,CAAC;YAC/B,2CAA2C;YAC3C,IAAI,MAAM,CAAC,QAAQ;gBAAE,SAAQ;YAE7B,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,sBAAsB,CACjD,QAAQ,EACR,MAAM,CACT,CAAA;YACD,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,WAAW,CAAC,MAAM,MAC7B,WAAW,CAAC,IAChB,eAAe,IAAI,CAAC,aAAa,CAC7B,QAAQ,EACR,MAAM,EACN,KAAK,CACR,EAAE,CACN,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,cAAc,IAAI,CAAC,aAAa,CAC5B,QAAQ,EACR,MAAM,CACT,eAAe,WAAW,CAAC,IAAI,GAAG,CACtC,CACJ,CAAA;QACL,CAAC;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;IACrD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CACX,WAA2B,EAC3B,MAAmB;QAEnB,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QACjC,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAE/B,IAAI,MAAM,CAAC,kBAAkB,KAAK,WAAW,EAAE,CAAC;YAC5C,MAAM,IAAI,YAAY,CAClB,0EAA0E,CAC7E,CAAA;QACL,CAAC;QAED,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;YAC1D,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;YACrD,IAAI,CAAC,OAAO,EAAE,CAAC;gBACX,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAA;gBACrD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAA;YACzD,CAAC;QACL,CAAC;QAED,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,QAAQ,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE,CACtD,CACJ,CAAA;QACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,iBACjC,MAAM,CAAC,IACX,GAAG,CACN,CACJ,CAAA;QAED,0CAA0C;QAC1C,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;YACnB,MAAM,cAAc,GAAG,WAAW,CAAC,cAAc,CAAA;YACjD,wEAAwE;YACxE,gDAAgD;YAChD,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC5B,MAAM,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;oBACrD,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;oBAC5C,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAC9C,CAAA;gBAEP,MAAM,WAAW,GAAG,cAAc;qBAC7B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC;qBACnC,IAAI,CAAC,IAAI,CAAC,CAAA;gBACf,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,MAAM,GAAG,CAClC,CACJ,CAAA;gBACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBAAoB,MAAM,kBAAkB,WAAW,GAAG,CAC9D,CACJ,CAAA;YACL,CAAC;YAED,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;YAC3B,MAAM,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;gBACrD,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;gBAC5C,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAC9C,CAAA;YAEP,MAAM,WAAW,GAAG,cAAc;iBAC7B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC;iBACnC,IAAI,CAAC,IAAI,CAAC,CAAA;YACf,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBAAoB,MAAM,kBAAkB,WAAW,GAAG,CAC9D,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,MAAM,GAAG,CAClC,CACJ,CAAA;QACL,CAAC;QAED,IAAI,MAAM,CAAC,aAAa,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;YAC9C,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;YACnD,IAAI,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;YAClD,IAAI,CAAC,MAAM,EAAE,CAAC;gBACV,MAAM,GAAG,aAAa,CAAA;YAC1B,CAAC;YACD,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,MAAM,EAAE,MAAM;gBACd,KAAK,EAAE,KAAK,CAAC,IAAI;gBACjB,IAAI,EAAE,iBAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,KAAK,EAAE,MAAM,CAAC,YAAY;aAC7B,CAAC,CAAA;YAEF,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,MAAM,EAAE,MAAM;gBACd,KAAK,EAAE,KAAK,CAAC,IAAI;gBACjB,IAAI,EAAE,iBAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;aACpB,CAAC,CAAA;YAEF,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YAC3B,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QACjC,CAAC;QAED,sBAAsB;QACtB,MAAM,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CACxC,CAAC,KAAK,EAAE,EAAE,CACN,KAAK,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;YAC9B,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,IAAI,CAC3C,CAAA;QACD,IAAI,WAAW,EAAE,CAAC;YACd,0DAA0D;YAC1D,IAAI,WAAW,CAAC,QAAQ,EAAE,CAAC;gBACvB,MAAM,MAAM,GAAG,IAAI,WAAW,CAAC;oBAC3B,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CACrD,KAAK,EACL,WAAW,CAAC,WAAW,CAC1B;oBACD,WAAW,EAAE,WAAW,CAAC,WAAW;iBACvC,CAAC,CAAA;gBACF,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAA;gBAC7D,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAA;gBAClD,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;YACpC,CAAC;iBAAM,CAAC;gBACJ,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAA;gBACvD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAA;YAC3D,CAAC;QACL,CAAC;QAED,2BAA2B;QAC3B,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;YAClB,MAAM,gBAAgB,GAAG,IAAI,WAAW,CAAC;gBACrC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CACrD,KAAK,EACL,CAAC,MAAM,CAAC,IAAI,CAAC,CAChB;gBACD,WAAW,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC;aAC7B,CAAC,CAAA;YACF,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;YAC1C,SAAS,CAAC,IAAI,CACV,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAC1D,CAAA;YACD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,gBAAgB,CAAC,IAAK,CAAC,CAAC,CAAA,CAAC,qDAAqD;QAC5H,CAAC;QAED,0BAA0B;QAC1B,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACjB,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,qBAAqB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KACvC,MAAM,CAAC,IACX,QAAQ,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAC/C,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,qBAAqB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KACvC,MAAM,CAAC,IACX,QAAQ,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAC/C,CACJ,CAAA;QACL,CAAC;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;QAEjD,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;QAC7B,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CACZ,WAA2B,EAC3B,OAAsB;QAEtB,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC3B,MAAM,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,MAAM,CAAC,CAAA;QAC7C,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CACd,WAA2B,EAC3B,oBAA0C,EAC1C,oBAA0C;QAE1C,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,SAAS,GAAG,eAAe,CAAC,aAAa,CAAC,oBAAoB,CAAC;YACjE,CAAC,CAAC,oBAAoB;YACtB,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,oBAAoB,CAAC,CAAA;QAChE,IAAI,CAAC,SAAS;YACV,MAAM,IAAI,YAAY,CAClB,WAAW,oBAAoB,2BAA2B,KAAK,CAAC,IAAI,UAAU,CACjF,CAAA;QAEL,IAAI,SAAS,CAAA;QACb,IAAI,eAAe,CAAC,aAAa,CAAC,oBAAoB,CAAC,EAAE,CAAC;YACtD,SAAS,GAAG,oBAAoB,CAAA;QACpC,CAAC;aAAM,CAAC;YACJ,SAAS,GAAG,SAAS,CAAC,KAAK,EAAE,CAAA;YAC7B,SAAS,CAAC,IAAI,GAAG,oBAAoB,CAAA;QACzC,CAAC;QAED,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,SAAS,EAAE,SAAS,CAAC,CAAA;IACzD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CACd,WAA2B,EAC3B,oBAA0C,EAC1C,SAAsB;QAEtB,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,IAAI,WAAW,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QAC/B,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAC/B,IAAI,mBAAmB,GAAG,KAAK,CAAA;QAE/B,MAAM,SAAS,GAAG,eAAe,CAAC,aAAa,CAAC,oBAAoB,CAAC;YACjE,CAAC,CAAC,oBAAoB;YACtB,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CACd,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,oBAAoB,CACnD,CAAA;QACP,IAAI,CAAC,SAAS;YACV,MAAM,IAAI,YAAY,CAClB,WAAW,oBAAoB,2BAA2B,KAAK,CAAC,IAAI,UAAU,CACjF,CAAA;QAEL,IACI,SAAS,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI;YACjC,SAAS,CAAC,MAAM,KAAK,SAAS,CAAC,MAAM;YACrC,SAAS,CAAC,OAAO,KAAK,SAAS,CAAC,OAAO;YACvC,SAAS,CAAC,aAAa,KAAK,SAAS,CAAC,aAAa;YACnD,SAAS,CAAC,YAAY,KAAK,SAAS,CAAC,YAAY,EACnD,CAAC;YACC,oDAAoD;YACpD,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,SAAS,CAAC,CAAA;YACvC,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,CAAA;YAEtC,sBAAsB;YACtB,WAAW,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QAC/B,CAAC;aAAM,CAAC;YACJ,IAAI,SAAS,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,EAAE,CAAC;gBACpC,gBAAgB;gBAChB,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,mBACjC,SAAS,CAAC,IACd,SAAS,SAAS,CAAC,IAAI,GAAG,CAC7B,CACJ,CAAA;gBACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,mBACjC,SAAS,CAAC,IACd,SAAS,SAAS,CAAC,IAAI,GAAG,CAC7B,CACJ,CAAA;gBAED,mBAAmB;gBACnB,IACI,SAAS,CAAC,IAAI,KAAK,MAAM;oBACzB,SAAS,CAAC,IAAI,KAAK,aAAa,EAClC,CAAC;oBACC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,sBAAsB,CACjD,KAAK,EACL,SAAS,CACZ,CAAA;oBACD,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,WAAW,CAAC,MAAM,MAC7B,WAAW,CAAC,IAChB,eAAe,IAAI,CAAC,aAAa,CAC7B,KAAK,EACL,SAAS,EACT,KAAK,CACR,EAAE,CACN,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,cAAc,IAAI,CAAC,aAAa,CAC5B,KAAK,EACL,SAAS,CACZ,eAAe,WAAW,CAAC,IAAI,GAAG,CACtC,CACJ,CAAA;gBACL,CAAC;gBAED,uCAAuC;gBACvC,IACI,SAAS,CAAC,SAAS,KAAK,IAAI;oBAC5B,CAAC,SAAS,CAAC,wBAAwB,EACrC,CAAC;oBACC,MAAM,cAAc,GAAG,WAAW,CAAC,cAAc,CAAA;oBAEjD,oCAAoC;oBACpC,MAAM,WAAW,GAAG,cAAc,CAAC,GAAG,CAClC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAC1B,CAAA;oBACD,MAAM,SAAS,GACX,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,WAAW,CACd,CAAA;oBAEL,+CAA+C;oBAC/C,WAAW,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAA;oBAC1D,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;oBAEhC,oCAAoC;oBACpC,MAAM,SAAS,GACX,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,WAAW,CACd,CAAA;oBAEL,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,uBAAuB,SAAS,SAAS,SAAS,GAAG,CACzD,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,uBAAuB,SAAS,SAAS,SAAS,GAAG,CACzD,CACJ,CAAA;gBACL,CAAC;gBAED,4BAA4B;gBAC5B,WAAW,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;oBACxD,MAAM,aAAa,GACf,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CAC/C,WAAW,EACX,MAAM,CAAC,WAAW,CACrB,CAAA;oBAEL,2DAA2D;oBAC3D,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa;wBAAE,OAAM;oBAEzC,4BAA4B;oBAC5B,MAAM,CAAC,WAAW,CAAC,MAAM,CACrB,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,EAC1C,CAAC,CACJ,CAAA;oBACD,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;oBACvC,MAAM,aAAa,GACf,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CAC/C,WAAW,EACX,MAAM,CAAC,WAAW,CACrB,CAAA;oBAEL,gBAAgB;oBAChB,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,uBACG,MAAM,CAAC,IACX,SAAS,aAAa,GAAG,CAC5B,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,uBAAuB,aAAa,SACjC,MAAM,CAAC,IACX,GAAG,CACN,CACJ,CAAA;oBAED,0BAA0B;oBAC1B,MAAM,CAAC,IAAI,GAAG,aAAa,CAAA;gBAC/B,CAAC,CAAC,CAAA;gBAEF,2BAA2B;gBAC3B,WAAW,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;oBACvD,MAAM,YAAY,GACd,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CACpC,WAAW,EACX,KAAK,CAAC,WAAW,EACjB,KAAK,CAAC,KAAK,CACd,CAAA;oBAEL,0DAA0D;oBAC1D,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY;wBAAE,OAAM;oBAEvC,4BAA4B;oBAC5B,KAAK,CAAC,WAAW,CAAC,MAAM,CACpB,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,EACzC,CAAC,CACJ,CAAA;oBACD,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;oBACtC,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;oBACpD,MAAM,YAAY,GACd,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CACpC,WAAW,EACX,KAAK,CAAC,WAAW,EACjB,KAAK,CAAC,KAAK,CACd,CAAA;oBAEL,gBAAgB;oBAChB,MAAM,EAAE,GAAG,MAAM;wBACb,CAAC,CAAC,gBAAgB,MAAM,MAAM,KAAK,CAAC,IAAI,gBAAgB,YAAY,GAAG;wBACvE,CAAC,CAAC,gBAAgB,KAAK,CAAC,IAAI,gBAAgB,YAAY,GAAG,CAAA;oBAC/D,MAAM,IAAI,GAAG,MAAM;wBACf,CAAC,CAAC,gBAAgB,MAAM,MAAM,YAAY,gBAAgB,KAAK,CAAC,IAAI,GAAG;wBACvE,CAAC,CAAC,gBAAgB,YAAY,gBAAgB,KAAK,CAAC,IAAI,GAAG,CAAA;oBAC/D,SAAS,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,CAAC,CAAA;oBAC7B,WAAW,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAA;oBAEjC,0BAA0B;oBAC1B,KAAK,CAAC,IAAI,GAAG,YAAY,CAAA;gBAC7B,CAAC,CAAC,CAAA;gBAEF,iCAAiC;gBACjC,WAAW;qBACN,qBAAqB,CAAC,SAAS,CAAC;qBAChC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;oBACpB,MAAM,cAAc,GAChB,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,UAAU,CAAC,WAAW,EACtB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAC7B,UAAU,CAAC,qBAAqB,CACnC,CAAA;oBAEL,gEAAgE;oBAChE,IAAI,UAAU,CAAC,IAAI,KAAK,cAAc;wBAAE,OAAM;oBAE9C,4BAA4B;oBAC5B,UAAU,CAAC,WAAW,CAAC,MAAM,CACzB,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,EAC9C,CAAC,CACJ,CAAA;oBACD,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;oBAC3C,MAAM,iBAAiB,GACnB,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,UAAU,CAAC,WAAW,EACtB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAC7B,UAAU,CAAC,qBAAqB,CACnC,CAAA;oBAEL,gBAAgB;oBAChB,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,uBACG,UAAU,CAAC,IACf,SAAS,iBAAiB,GAAG,CAChC,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,uBAAuB,iBAAiB,SACrC,UAAU,CAAC,IACf,GAAG,CACN,CACJ,CAAA;oBAED,0BAA0B;oBAC1B,UAAU,CAAC,IAAI,GAAG,iBAAiB,CAAA;gBACvC,CAAC,CAAC,CAAA;gBAEN,wCAAwC;gBACxC,MAAM,cAAc,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CAC3C,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CAC7C,CAAA;gBACD,WAAW,CAAC,OAAO,CACf,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,cAAe,CAAC,CAC/C,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI,CAAA;gBACvB,SAAS,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI,CAAA;YACnC,CAAC;YAED,IACI,SAAS,CAAC,SAAS,KAAK,SAAS,CAAC,SAAS;gBAC3C,SAAS,CAAC,KAAK,KAAK,SAAS,CAAC,KAAK,EACrC,CAAC;gBACC,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,kBACjC,SAAS,CAAC,IACd,UAAU,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE,CACpD,CACJ,CAAA;gBACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,kBACjC,SAAS,CAAC,IACd,UAAU,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE,CACpD,CACJ,CAAA;YACL,CAAC;YAED,IAAI,SAAS,CAAC,UAAU,KAAK,SAAS,CAAC,UAAU,EAAE,CAAC;gBAChD,IAAI,SAAS,CAAC,UAAU,EAAE,CAAC;oBACvB,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,kBAAkB,SAAS,CAAC,IAAI,iBAAiB,CACrD,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,kBAAkB,SAAS,CAAC,IAAI,gBAAgB,CACpD,CACJ,CAAA;gBACL,CAAC;qBAAM,CAAC;oBACJ,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,kBAAkB,SAAS,CAAC,IAAI,gBAAgB,CACpD,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,kBAAkB,SAAS,CAAC,IAAI,iBAAiB,CACrD,CACJ,CAAA;gBACL,CAAC;YACL,CAAC;YAED,IAAI,SAAS,CAAC,OAAO,KAAK,SAAS,CAAC,OAAO,EAAE,CAAC;gBAC1C,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,qBAAqB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KACvC,SAAS,CAAC,IACd,QAAQ,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,CAClD,CACJ,CAAA;gBACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,qBAAqB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KACvC,SAAS,CAAC,IACd,QAAQ,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,CAClD,CACJ,CAAA;YACL,CAAC;YAED,IAAI,SAAS,CAAC,SAAS,KAAK,SAAS,CAAC,SAAS,EAAE,CAAC;gBAC9C,MAAM,cAAc,GAAG,WAAW,CAAC,cAAc,CAAA;gBAEjD,2EAA2E;gBAC3E,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC5B,MAAM,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;wBACrD,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;wBAC5C,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAC9C,CAAA;oBAEP,MAAM,WAAW,GAAG,cAAc;yBAC7B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC;yBACnC,IAAI,CAAC,IAAI,CAAC,CAAA;oBAEf,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,MAAM,GAAG,CAClC,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBAAoB,MAAM,kBAAkB,WAAW,GAAG,CAC9D,CACJ,CAAA;gBACL,CAAC;gBAED,IAAI,SAAS,CAAC,SAAS,KAAK,IAAI,EAAE,CAAC;oBAC/B,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;oBAC9B,yBAAyB;oBACzB,MAAM,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CACnC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CAC7C,CAAA;oBACD,MAAO,CAAC,SAAS,GAAG,IAAI,CAAA;oBACxB,MAAM,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;wBACrD,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;wBAC5C,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAC9C,CAAA;oBAEP,MAAM,WAAW,GAAG,cAAc;yBAC7B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC;yBACnC,IAAI,CAAC,IAAI,CAAC,CAAA;oBAEf,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBAAoB,MAAM,kBAAkB,WAAW,GAAG,CAC9D,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,MAAM,GAAG,CAClC,CACJ,CAAA;gBACL,CAAC;qBAAM,CAAC;oBACJ,MAAM,aAAa,GAAG,cAAc,CAAC,IAAI,CACrC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CACnC,CAAA;oBACD,cAAc,CAAC,MAAM,CACjB,cAAc,CAAC,OAAO,CAAC,aAAc,CAAC,EACtC,CAAC,CACJ,CAAA;oBAED,yBAAyB;oBACzB,MAAM,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CACnC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CAC7C,CAAA;oBACD,MAAO,CAAC,SAAS,GAAG,KAAK,CAAA;oBAEzB,gEAAgE;oBAChE,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBAC5B,MAAM,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC;6BAC3B,wBAAwB;4BACzB,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;4BAC5C,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAC9C,CAAA;wBAEP,MAAM,WAAW,GAAG,cAAc;6BAC7B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC;6BACnC,IAAI,CAAC,IAAI,CAAC,CAAA;wBACf,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBAAoB,MAAM,kBAAkB,WAAW,GAAG,CAC9D,CACJ,CAAA;wBACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,MAAM,GAAG,CAClC,CACJ,CAAA;oBACL,CAAC;gBACL,CAAC;YACL,CAAC;YAED,IAAI,SAAS,CAAC,QAAQ,KAAK,SAAS,CAAC,QAAQ,EAAE,CAAC;gBAC5C,IAAI,SAAS,CAAC,QAAQ,EAAE,CAAC;oBACrB,MAAM,gBAAgB,GAAG,IAAI,WAAW,CAAC;wBACrC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CACrD,KAAK,EACL,CAAC,SAAS,CAAC,IAAI,CAAC,CACnB;wBACD,WAAW,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC;qBAChC,CAAC,CAAA;oBACF,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;oBAC1C,SAAS,CAAC,IAAI,CACV,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAC1D,CAAA;oBACD,mDAAmD;oBACnD,iEAAiE;oBACjE,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC,CAAA;gBAChE,CAAC;qBAAM,CAAC;oBACJ,MAAM,gBAAgB,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CAC7C,CAAC,MAAM,EAAE,EAAE;wBACP,OAAO,CACH,MAAM,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;4BAC/B,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CACrB,CAAC,UAAU,EAAE,EAAE,CACX,UAAU,KAAK,SAAS,CAAC,IAAI,CACpC,CACJ,CAAA;oBACL,CAAC,CACJ,CAAA;oBACD,WAAW,CAAC,OAAO,CAAC,MAAM,CACtB,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,gBAAiB,CAAC,EAC9C,CAAC,CACJ,CAAA;oBACD,mDAAmD;oBACnD,iEAAiE;oBACjE,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,gBAAiB,CAAC,CAAC,CAAA;oBAC3D,WAAW,CAAC,IAAI,CACZ,IAAI,CAAC,yBAAyB,CAC1B,KAAK,EACL,gBAAiB,CACpB,CACJ,CAAA;gBACL,CAAC;YACL,CAAC;YAED,IACI,CAAC,SAAS,CAAC,IAAI,KAAK,MAAM;gBACtB,SAAS,CAAC,IAAI,KAAK,aAAa,CAAC;gBACrC,CAAC,SAAS,CAAC,IAAI,KAAK,MAAM;oBACtB,SAAS,CAAC,IAAI,KAAK,aAAa,CAAC;gBACrC,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,SAAS,CAAC,IAAK,EAAE,SAAS,CAAC,IAAK,CAAC;oBACtD,SAAS,CAAC,QAAQ,KAAK,SAAS,CAAC,QAAQ,CAAC,EAChD,CAAC;gBACC,MAAM,WAAW,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAA;gBAEjD,sBAAsB;gBACtB,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,SAAS,CAAC,CAAA;gBAExD,sBAAsB;gBACtB,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,SAAS,CAAC,CAAA;gBAExD,aAAa;gBACb,MAAM,wBAAwB,GAAG,IAAI,CAAC,aAAa,CAC/C,KAAK,EACL,SAAS,EACT,KAAK,CACR,CAAA;gBAED,yBAAyB;gBACzB,MAAM,yBAAyB,GAAG,IAAI,CAAC,aAAa,CAChD,KAAK,EACL,SAAS,EACT,IAAI,EACJ,KAAK,EACL,IAAI,CACP,CAAA;gBAED,gBAAgB;gBAChB,MAAM,4BAA4B,GAAG,IAAI,CAAC,aAAa,CACnD,KAAK,EACL,SAAS,EACT,KAAK,EACL,KAAK,EACL,IAAI,CACP,CAAA;gBAED,kBAAkB;gBAClB,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,cAAc,WAAW,cAAc,4BAA4B,EAAE,CACxE,CACJ,CAAA;gBACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,cAAc,yBAAyB,cAAc,wBAAwB,EAAE,CAClF,CACJ,CAAA;gBAED,kBAAkB;gBAClB,SAAS,CAAC,IAAI,CACV,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,SAAS,EAAE,WAAW,CAAC,CACxD,CAAA;gBACD,WAAW,CAAC,IAAI,CACZ,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,SAAS,EAAE,WAAW,CAAC,CACtD,CAAA;gBAED,kFAAkF;gBAClF,IACI,SAAS,CAAC,OAAO,KAAK,IAAI;oBAC1B,SAAS,CAAC,OAAO,KAAK,SAAS,EACjC,CAAC;oBACC,mDAAmD;oBACnD,mBAAmB,GAAG,IAAI,CAAA;oBAC1B,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,kBAAkB,SAAS,CAAC,IAAI,gBAAgB,CACpD,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,kBAAkB,SAAS,CAAC,IAAI,iBAC7B,SAAS,CAAC,OACd,EAAE,CACL,CACJ,CAAA;gBACL,CAAC;gBAED,qBAAqB;gBACrB,MAAM,MAAM,GAAG,GAAG,WAAW,GAAG,WAAW,WAAW,SAAS,CAAC,IAAI,cAAc,WAAW,GAAG,WAAW,EAAE,CAAA;gBAC7G,MAAM,QAAQ,GAAG,GAAG,yBAAyB,GAAG,WAAW,WAAW,SAAS,CAAC,IAAI,cAAc,yBAAyB,GAAG,WAAW,EAAE,CAAA;gBAE3I,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,kBACjC,SAAS,CAAC,IACd,UAAU,MAAM,EAAE,CACrB,CACJ,CAAA;gBAED,mEAAmE;gBACnE,oEAAoE;gBACpE,2DAA2D;gBAC3D,SAAS,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC,CAAA;gBACjD,WAAW,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC,CAAA;gBAEnD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,kBACjC,SAAS,CAAC,IACd,UAAU,QAAQ,EAAE,CACvB,CACJ,CAAA;gBAED,2CAA2C;gBAC3C,IACI,SAAS,CAAC,OAAO,KAAK,IAAI;oBAC1B,SAAS,CAAC,OAAO,KAAK,SAAS,EACjC,CAAC;oBACC,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,kBAAkB,SAAS,CAAC,IAAI,iBAC7B,SAAS,CAAC,OACd,EAAE,CACL,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,kBAAkB,SAAS,CAAC,IAAI,gBAAgB,CACpD,CACJ,CAAA;gBACL,CAAC;gBAED,kBAAkB;gBAClB,SAAS,CAAC,IAAI,CACV,IAAI,CAAC,eAAe,CAChB,KAAK,EACL,SAAS,EACT,yBAAyB,CAC5B,CACJ,CAAA;gBACD,WAAW,CAAC,IAAI,CACZ,IAAI,CAAC,iBAAiB,CAClB,KAAK,EACL,SAAS,EACT,yBAAyB,CAC5B,CACJ,CAAA;YACL,CAAC;YAED,IACI,SAAS,CAAC,WAAW,KAAK,SAAS,CAAC,WAAW;gBAC/C,SAAS,CAAC,kBAAkB,KAAK,MAAM,EACzC,CAAC;gBACC,IAAI,SAAS,CAAC,WAAW,EAAE,CAAC;oBACxB,IAAI,SAAS,CAAC,kBAAkB,KAAK,WAAW,EAAE,CAAC;wBAC/C,MAAM,IAAI,YAAY,CAClB,0EAA0E,CAC7E,CAAA;oBACL,CAAC;yBAAM,IAAI,SAAS,CAAC,kBAAkB,KAAK,OAAO,EAAE,CAAC;wBAClD,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,kBACG,SAAS,CAAC,IACd,8BAA8B,CACjC,CACJ,CAAA;wBACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,kBACG,SAAS,CAAC,IACd,gBAAgB,CACnB,CACJ,CAAA;oBACL,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACJ,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,kBAAkB,SAAS,CAAC,IAAI,gBAAgB,CACpD,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,kBACG,SAAS,CAAC,IACd,8BAA8B,CACjC,CACJ,CAAA;gBACL,CAAC;YACL,CAAC;YAED,IACI,SAAS,CAAC,OAAO,KAAK,SAAS,CAAC,OAAO;gBACvC,CAAC,mBAAmB,EACtB,CAAC;gBACC,IACI,SAAS,CAAC,OAAO,KAAK,IAAI;oBAC1B,SAAS,CAAC,OAAO,KAAK,SAAS,EACjC,CAAC;oBACC,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,kBAAkB,SAAS,CAAC,IAAI,iBAC7B,SAAS,CAAC,OACd,EAAE,CACL,CACJ,CAAA;oBAED,IACI,SAAS,CAAC,OAAO,KAAK,IAAI;wBAC1B,SAAS,CAAC,OAAO,KAAK,SAAS,EACjC,CAAC;wBACC,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,kBACG,SAAS,CAAC,IACd,iBAAiB,SAAS,CAAC,OAAO,EAAE,CACvC,CACJ,CAAA;oBACL,CAAC;yBAAM,CAAC;wBACJ,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,kBACG,SAAS,CAAC,IACd,gBAAgB,CACnB,CACJ,CAAA;oBACL,CAAC;gBACL,CAAC;qBAAM,IACH,SAAS,CAAC,OAAO,KAAK,IAAI;oBAC1B,SAAS,CAAC,OAAO,KAAK,SAAS,EACjC,CAAC;oBACC,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,kBAAkB,SAAS,CAAC,IAAI,gBAAgB,CACpD,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,kBAAkB,SAAS,CAAC,IAAI,iBAC7B,SAAS,CAAC,OACd,EAAE,CACL,CACJ,CAAA;gBACL,CAAC;YACL,CAAC;QACL,CAAC;QAED,IACI,CAAC,SAAS,CAAC,kBAAkB,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE;YAC9C,CAAC,SAAS,CAAC,kBAAkB,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE;YACtD,SAAS,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,EACnC,CAAC;YACC,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,kBACjC,SAAS,CAAC,IACd,UAAU,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE,CACpD,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,kBACjC,SAAS,CAAC,IACd,UAAU,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE,CACpD,CACJ,CAAA;QACL,CAAC;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;QACjD,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CACf,WAA2B,EAC3B,cAAoE;QAEpE,KAAK,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,cAAc,EAAE,CAAC;YACpD,MAAM,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,SAAS,EAAE,SAAS,CAAC,CAAA;QAC9D,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CACZ,WAA2B,EAC3B,YAAkC;QAElC,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,MAAM,GAAG,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YACtD,CAAC,CAAC,YAAY;YACd,CAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAA;QAC1C,IAAI,CAAC,MAAM;YACP,MAAM,IAAI,YAAY,CAClB,WAAW,YAAY,6BAA6B,KAAK,CAAC,IAAI,GAAG,CACpE,CAAA;QAEL,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QACjC,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAE/B,8BAA8B;QAC9B,gDAAgD;QAChD,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;YACnB,MAAM,MAAM,GAAG,MAAM,CAAC,wBAAwB;gBAC1C,CAAC,CAAC,MAAM,CAAC,wBAAwB;gBACjC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,WAAW,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAC1D,CAAA;YAEP,MAAM,WAAW,GAAG,WAAW,CAAC,cAAc;iBACzC,GAAG,CAAC,CAAC,aAAa,EAAE,EAAE,CAAC,IAAI,aAAa,CAAC,IAAI,GAAG,CAAC;iBACjD,IAAI,CAAC,IAAI,CAAC,CAAA;YACf,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,WAAW,CACd,qBAAqB,MAAM,GAAG,CAClC,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,WAAW,CACd,oBAAoB,MAAM,kBAAkB,WAAW,GAAG,CAC9D,CACJ,CAAA;YAED,yBAAyB;YACzB,MAAM,WAAW,GAAG,WAAW,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;YAC7D,WAAY,CAAC,SAAS,GAAG,KAAK,CAAA;YAE9B,mFAAmF;YACnF,IAAI,WAAW,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACxC,MAAM,MAAM,GAAG,WAAW,CAAC,cAAc,CAAC,CAAC,CAAC;qBACvC,wBAAwB;oBACzB,CAAC,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;oBACxD,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,WAAW,CAAC,cAAc,CAAC,GAAG,CAC1B,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAC1B,CACJ,CAAA;gBAEP,MAAM,WAAW,GAAG,WAAW,CAAC,cAAc;qBACzC,GAAG,CAAC,CAAC,aAAa,EAAE,EAAE,CAAC,IAAI,aAAa,CAAC,IAAI,GAAG,CAAC;qBACjD,IAAI,CAAC,IAAI,CAAC,CAAA;gBAEf,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,WAAW,CACd,oBAAoB,MAAM,kBAAkB,WAAW,GAAG,CAC9D,CACJ,CAAA;gBACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,WAAW,CACd,qBAAqB,MAAM,GAAG,CAClC,CACJ,CAAA;YACL,CAAC;QACL,CAAC;QAED,oBAAoB;QACpB,MAAM,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CACxC,CAAC,KAAK,EAAE,EAAE,CACN,KAAK,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;YAC9B,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,IAAI,CAC3C,CAAA;QACD,IAAI,WAAW,EAAE,CAAC;YACd,WAAW,CAAC,OAAO,CAAC,MAAM,CACtB,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,EACxC,CAAC,CACJ,CAAA;YACD,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAA;YACrD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAA;QAC7D,CAAC;QAED,oBAAoB;QACpB,MAAM,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC,IAAI,CACvC,CAAC,KAAK,EAAE,EAAE,CACN,CAAC,CAAC,KAAK,CAAC,WAAW;YACnB,KAAK,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;YAC9B,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,IAAI,CAC3C,CAAA;QACD,IAAI,WAAW,EAAE,CAAC;YACd,WAAW,CAAC,MAAM,CAAC,MAAM,CACrB,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,EACvC,CAAC,CACJ,CAAA;YACD,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAA;YAC/D,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAA;QACvE,CAAC;QAED,qBAAqB;QACrB,MAAM,YAAY,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CACzC,CAAC,MAAM,EAAE,EAAE,CACP,MAAM,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;YAC/B,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,IAAI,CAC5C,CAAA;QACD,IAAI,YAAY,EAAE,CAAC;YACf,WAAW,CAAC,OAAO,CAAC,MAAM,CACtB,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,EACzC,CAAC,CACJ,CAAA;YACD,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,YAAY,CAAC,IAAK,CAAC,CAAC,CAAA,CAAC,qDAAqD;YAClH,WAAW,CAAC,IAAI,CACZ,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,YAAY,CAAC,CACtD,CAAA;QACL,CAAC;QAED,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,iBACjC,MAAM,CAAC,IACX,GAAG,CACN,CACJ,CAAA;QACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,QAAQ,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE,CACtD,CACJ,CAAA;QAED,IAAI,MAAM,CAAC,kBAAkB,KAAK,WAAW,EAAE,CAAC;YAC5C,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,iBAAiB,IAAI,CAAC,UAAU,CAC5B,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,MAAM,CAAC,CACxC,EAAE,CACN,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,mBAAmB,IAAI,CAAC,UAAU,CAC9B,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,MAAM,CAAC,CACxC,EAAE,CACN,CACJ,CAAA;QACL,CAAC;QAED,IAAI,MAAM,CAAC,aAAa,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;YAC9C,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;YACnD,IAAI,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;YAClD,IAAI,CAAC,MAAM,EAAE,CAAC;gBACV,MAAM,GAAG,aAAa,CAAA;YAC1B,CAAC;YACD,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,MAAM,EAAE,MAAM;gBACd,KAAK,EAAE,KAAK,CAAC,IAAI;gBACjB,IAAI,EAAE,iBAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;aACpB,CAAC,CAAA;YACF,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,MAAM,EAAE,MAAM;gBACd,KAAK,EAAE,KAAK,CAAC,IAAI;gBACjB,IAAI,EAAE,iBAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,KAAK,EAAE,MAAM,CAAC,YAAY;aAC7B,CAAC,CAAA;YAEF,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YAC3B,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QACjC,CAAC;QAED,iBAAiB;QACjB,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;YAC1D,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;YACrD,IAAI,OAAO,EAAE,CAAC;gBACV,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAC9C,KAAK,EACL,MAAM,CACT,CAAA;gBACD,MAAM,eAAe,GAAG,IAAI,QAAQ,CAAC,MAAM,MAAM,QAAQ,CAAC,IAAI,GAAG,CAAA;gBACjE,SAAS,CAAC,IAAI,CACV,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,MAAM,EAAE,eAAe,CAAC,CACvD,CAAA;gBACD,WAAW,CAAC,IAAI,CACZ,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,MAAM,EAAE,eAAe,CAAC,CACzD,CAAA;YACL,CAAC;QACL,CAAC;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;QAEjD,WAAW,CAAC,YAAY,CAAC,MAAM,CAAC,CAAA;QAChC,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACb,WAA2B,EAC3B,OAAiC;QAEjC,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC3B,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,MAAM,CAAC,CAAA;QAC9C,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAClB,WAA2B,EAC3B,WAAqB,EACrB,cAAuB;QAEvB,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QAEjC,MAAM,EAAE,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,WAAW,EAAE,cAAc,CAAC,CAAA;QAEvE,4GAA4G;QAC5G,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YACnC,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,KAAK,MAAM,CAAC,IAAI,CAAC;gBAC5D,MAAM,CAAC,SAAS,GAAG,IAAI,CAAA;QAC/B,CAAC,CAAC,CAAA;QACF,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAA;QAEhD,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CACnB,WAA2B,EAC3B,OAAsB;QAEtB,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QACjC,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;QACxD,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAE/B,4DAA4D;QAC5D,MAAM,cAAc,GAAG,WAAW,CAAC,cAAc,CAAA;QACjD,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5B,MAAM,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;gBACrD,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;gBAC5C,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAC9C,CAAA;YAEP,MAAM,iBAAiB,GAAG,cAAc;iBACnC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC;iBACnC,IAAI,CAAC,IAAI,CAAC,CAAA;YAEf,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,MAAM,GAAG,CAClC,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBAAoB,MAAM,kBAAkB,iBAAiB,GAAG,CACpE,CACJ,CAAA;QACL,CAAC;QAED,2BAA2B;QAC3B,WAAW,CAAC,OAAO;aACd,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;aAC3D,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC,CAAA;QAEnD,MAAM,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;YACrD,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;YAC5C,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,WAAW,CACd,CAAA;QAEP,MAAM,iBAAiB,GAAG,WAAW;aAChC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,UAAU,GAAG,CAAC;aACtC,IAAI,CAAC,IAAI,CAAC,CAAA;QACf,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBAAoB,MAAM,kBAAkB,iBAAiB,GAAG,CACpE,CACJ,CAAA;QACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,MAAM,GAAG,CAClC,CACJ,CAAA;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;QACjD,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAChB,WAA2B,EAC3B,cAAuB;QAEvB,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAA;QACxC,MAAM,IAAI,GAAG,IAAI,CAAC,mBAAmB,CACjC,KAAK,EACL,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,EACjD,cAAc,CACjB,CAAA;QACD,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YACpC,MAAM,CAAC,SAAS,GAAG,KAAK,CAAA;QAC5B,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,sBAAsB,CACxB,WAA2B,EAC3B,gBAA6B;QAE7B,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAE5C,mGAAmG;QACnG,IAAI,CAAC,gBAAgB,CAAC,IAAI;YACtB,gBAAgB,CAAC,IAAI;gBACjB,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CAC/C,KAAK,EACL,gBAAgB,CAAC,WAAW,CAC/B,CAAA;QAET,MAAM,EAAE,GAAG,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAA;QAClE,mDAAmD;QACnD,iEAAiE;QACjE,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAA;QACvD,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB,CACzB,WAA2B,EAC3B,iBAAgC;QAEhC,KAAK,MAAM,gBAAgB,IAAI,iBAAiB,EAAE,CAAC;YAC/C,MAAM,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAA;QACpE,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CACtB,WAA2B,EAC3B,YAAkC;QAElC,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,gBAAgB,GAAG,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChE,CAAC,CAAC,YAAY;YACd,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,YAAY,CAAC,CAAA;QACxD,IAAI,CAAC,gBAAgB;YACjB,MAAM,IAAI,YAAY,CAClB,qDAAqD,KAAK,CAAC,IAAI,EAAE,CACpE,CAAA;QAEL,mDAAmD;QACnD,iEAAiE;QACjE,MAAM,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAA;QACrD,MAAM,IAAI,GAAG,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAA;QACpE,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,sBAAsB,CAAC,gBAAgB,CAAC,CAAA;IAClD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,qBAAqB,CACvB,WAA2B,EAC3B,iBAAgC;QAEhC,KAAK,MAAM,gBAAgB,IAAI,iBAAiB,EAAE,CAAC;YAC/C,MAAM,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAA;QAClE,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,qBAAqB,CACvB,WAA2B,EAC3B,eAA2B;QAE3B,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAE5C,mGAAmG;QACnG,IAAI,CAAC,eAAe,CAAC,IAAI;YACrB,eAAe,CAAC,IAAI;gBAChB,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,mBAAmB,CAC9C,KAAK,EACL,eAAe,CAAC,UAAW,CAC9B,CAAA;QAET,MAAM,EAAE,GAAG,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,eAAe,CAAC,CAAA;QAChE,MAAM,IAAI,GAAG,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,eAAe,CAAC,CAAA;QAChE,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAA;IAC7C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,sBAAsB,CACxB,WAA2B,EAC3B,gBAA8B;QAE9B,MAAM,QAAQ,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,EAAE,CACtD,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE,eAAe,CAAC,CAC3D,CAAA;QACD,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IAC/B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CACrB,WAA2B,EAC3B,WAAgC;QAEhC,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,eAAe,GAAG,eAAe,CAAC,YAAY,CAAC,WAAW,CAAC;YAC7D,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,CAAA;QACtD,IAAI,CAAC,eAAe;YAChB,MAAM,IAAI,YAAY,CAClB,oDAAoD,KAAK,CAAC,IAAI,EAAE,CACnE,CAAA;QAEL,MAAM,EAAE,GAAG,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,eAAe,CAAC,CAAA;QAC9D,MAAM,IAAI,GAAG,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,eAAe,CAAC,CAAA;QAClE,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,qBAAqB,CAAC,eAAe,CAAC,CAAA;IAChD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CACtB,WAA2B,EAC3B,gBAA8B;QAE9B,MAAM,QAAQ,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,EAAE,CACtD,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,eAAe,CAAC,CACzD,CAAA;QACD,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IAC/B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,yBAAyB,CAC3B,WAA2B,EAC3B,mBAAmC;QAEnC,MAAM,IAAI,YAAY,CAClB,qDAAqD,CACxD,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,0BAA0B,CAC5B,WAA2B,EAC3B,oBAAsC;QAEtC,MAAM,IAAI,YAAY,CAClB,qDAAqD,CACxD,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB,CACzB,WAA2B,EAC3B,eAAwC;QAExC,MAAM,IAAI,YAAY,CAClB,qDAAqD,CACxD,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,wBAAwB,CAC1B,WAA2B,EAC3B,oBAAsC;QAEtC,MAAM,IAAI,YAAY,CAClB,qDAAqD,CACxD,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAClB,WAA2B,EAC3B,UAA2B;QAE3B,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAE5C,gFAAgF;QAChF,IAAI,CAAC,UAAU,CAAC,IAAI;YAChB,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CAC3D,KAAK,EACL,UAAU,CAAC,WAAW,EACtB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAC7B,UAAU,CAAC,qBAAqB,CACnC,CAAA;QAEL,MAAM,EAAE,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;QACtD,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;QACtD,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,aAAa,CAAC,UAAU,CAAC,CAAA;IACnC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CACnB,WAA2B,EAC3B,WAA8B;QAE9B,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;YACnC,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,UAAU,CAAC,CAAA;QACxD,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAChB,WAA2B,EAC3B,gBAA0C;QAE1C,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,UAAU,GAAG,eAAe,CAAC,iBAAiB,CAAC,gBAAgB,CAAC;YAClE,CAAC,CAAC,gBAAgB;YAClB,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,KAAK,gBAAgB,CAAC,CAAA;QAClE,IAAI,CAAC,UAAU;YACX,MAAM,IAAI,YAAY,CAClB,+CAA+C,KAAK,CAAC,IAAI,EAAE,CAC9D,CAAA;QAEL,MAAM,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;QACpD,MAAM,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;QACxD,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAA;IACtC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CACjB,WAA2B,EAC3B,WAA8B;QAE9B,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;YACnC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,UAAU,CAAC,CAAA;QACtD,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACb,WAA2B,EAC3B,KAAiB;QAEjB,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAE5C,sFAAsF;QACtF,IAAI,CAAC,KAAK,CAAC,IAAI;YAAE,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAElE,2DAA2D;QAC3D,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;YACjB,MAAM,MAAM,GAAG,IAAI,WAAW,CAAC;gBAC3B,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,WAAW,EAAE,KAAK,CAAC,WAAW;aACjC,CAAC,CAAA;YACF,MAAM,EAAE,GAAG,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;YACxD,yDAAyD;YACzD,oGAAoG;YACpG,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;YAC7C,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;YACnC,KAAK,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAA;QACrC,CAAC;aAAM,CAAC;YACJ,MAAM,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;YAC5C,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;YAC5C,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;YACnC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;QACzB,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CACf,WAA2B,EAC3B,OAAqB;QAErB,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;YAC1B,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,CAAC,CAAA;QAC9C,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CACX,WAA2B,EAC3B,WAAgC;QAEhC,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,KAAK,GAAG,eAAe,CAAC,YAAY,CAAC,WAAW,CAAC;YACnD,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,CAAA;QACvD,IAAI,CAAC,KAAK;YACN,MAAM,IAAI,YAAY,CAClB,kBAAkB,WAAW,2BAA2B,KAAK,CAAC,IAAI,EAAE,CACvE,CAAA;QAEL,sFAAsF;QACtF,IAAI,CAAC,KAAK,CAAC,IAAI;YAAE,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAElE,MAAM,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAC1C,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAC9C,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACb,WAA2B,EAC3B,OAAqB;QAErB,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;YAC1B,MAAM,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,KAAK,CAAC,CAAA;QAC5C,CAAC;IACL,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,UAAU,CAAC,SAAiB;QAC9B,MAAM,IAAI,CAAC,KAAK,CAAC,kBAAkB,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC,CAAA;IACpE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa;QACf,MAAM,OAAO,GAAa,EAAE,CAAA;QAC5B,IAAI,CAAC,UAAU,CAAC,eAAe;aAC1B,MAAM,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC;aACrC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;YAClB,MAAM,aAAa,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,CAChC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,KAAK,QAAQ,CAAC,MAAM,CACzC,CAAA;YACD,IAAI,CAAC,aAAa;gBAAE,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAO,CAAC,CAAA;QACtD,CAAC,CAAC,CAAA;QACN,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,IAAI,kBAAkB,CAAC,CAAA;QAC9D,MAAM,iBAAiB,GAAG,OAAO;aAC5B,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;YACV,OAAO,IAAI,KAAK,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG,GAAG,CAAA;QAChE,CAAC,CAAC;aACD,IAAI,CAAC,IAAI,CAAC,CAAA;QAEf,MAAM,0BAA0B,GAAG,IAAI,CAAC,mBAAmB,CAAA;QAC3D,IAAI,CAAC,0BAA0B;YAAE,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;QAC9D,IAAI,CAAC;YACD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAA;YACvC,MAAM,oBAAoB,GACtB,+FAA+F;gBAC/F,0CAA0C,iBAAiB,GAAG,CAAA;YAClE,MAAM,eAAe,GAAoB,MAAM,IAAI,CAAC,KAAK,CACrD,oBAAoB,CACvB,CAAA;YACD,MAAM,OAAO,CAAC,GAAG,CACb,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CACrD,CAAA;YAED,MAAM,gBAAgB,GAAG,iKAAiK,iBAAiB,GAAG,CAAA;YAC9M,MAAM,WAAW,GAAoB,MAAM,IAAI,CAAC,KAAK,CACjD,gBAAgB,CACnB,CAAA;YACD,MAAM,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAA;YAEjE,MAAM,wBAAwB,GAAG,8JAA8J,iBAAiB,GAAG,CAAA;YACnN,MAAM,mBAAmB,GAAoB,MAAM,IAAI,CAAC,KAAK,CACzD,wBAAwB,CAC3B,CAAA;YACD,MAAM,OAAO,CAAC,GAAG,CACb,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CACzD,CAAA;YAED,qDAAqD;YACrD,IAAI,YAAY,CAAC,gBAAgB,CAAC,OAAO,EAAE,SAAS,CAAC,EAAE,CAAC;gBACpD,MAAM,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAA;YAC/C,CAAC;YAED,IAAI,CAAC,0BAA0B;gBAAE,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAA;QACnE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC;gBACD,2DAA2D;gBAC3D,IAAI,CAAC,0BAA0B;oBAC3B,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAA;YACxC,CAAC;YAAC,MAAM,CAAC;gBACL,QAAQ;YACZ,CAAC;YACD,MAAM,KAAK,CAAA;QACf,CAAC;IACL,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAElE,KAAK,CAAC,SAAS,CAAC,SAAoB;QAC1C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,2BAA2B,EAAE,CAAC,CAAA;QACxE,IAAI,CAAC,QAAQ,EAAE,CAAC;YACZ,OAAO,EAAE,CAAA;QACb,CAAC;QAED,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,SAAS,GAAG,EAAE,CAAA;QAClB,CAAC;QAED,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAA;QACvD,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;QAEnD,MAAM,cAAc,GAAG,SAAS;aAC3B,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE;YACd,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GACvB,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAA;YAExC,OAAO,oBACH,MAAM,IAAI,aACd,uBAAuB,SAAS,IAAI,CAAA;QACxC,CAAC,CAAC;aACD,IAAI,CAAC,MAAM,CAAC,CAAA;QAEjB,MAAM,KAAK,GACP,yCAAyC,IAAI,CAAC,UAAU,CACpD,IAAI,CAAC,2BAA2B,EAAE,CACrC,OAAO;YACR,0IACI,iBAAiB,CAAC,IACtB,KAAK,cAAc,CAAC,CAAC,CAAC,QAAQ,cAAc,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAA;QAC1D,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;QACvC,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,MAAW,EAAE,EAAE;YAC/B,MAAM,IAAI,GAAG,IAAI,IAAI,EAAE,CAAA;YACvB,MAAM,MAAM,GACR,MAAM,CAAC,QAAQ,CAAC,KAAK,aAAa;gBAClC,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM;gBACvB,CAAC,CAAC,SAAS;gBACX,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;YAC1B,IAAI,CAAC,QAAQ,GAAG,eAAe,CAAA;YAC/B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAA;YAC9B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,CAAA;YAC9D,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,CAAA;YACjC,OAAO,IAAI,CAAA;QACf,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,UAAU,CAAC,UAAqB;QAC5C,6CAA6C;QAC7C,IAAI,UAAU,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxC,OAAO,EAAE,CAAA;QACb,CAAC;QAED,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;QACnD,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAA;QAEvD,MAAM,QAAQ,GAAmD,EAAE,CAAA;QAEnE,IAAI,CAAC,UAAU,EAAE,CAAC;YACd,MAAM,SAAS,GAAG,wEAAwE,CAAA;YAC1F,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;QACnD,CAAC;aAAM,CAAC;YACJ,MAAM,eAAe,GAAG,UAAU;iBAC7B,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;iBACzD,GAAG,CAAC,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,EAAE,EAAE;gBAC3B,OAAO,sBACH,MAAM,IAAI,aACd,yBAAyB,SAAS,IAAI,CAAA;YAC1C,CAAC,CAAC;iBACD,IAAI,CAAC,MAAM,CAAC,CAAA;YACjB,MAAM,SAAS,GACX,+EAA+E;gBAC/E,eAAe,CAAA;YAEnB,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;QACnD,CAAC;QAED,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxB,OAAO,EAAE,CAAA;QACb,CAAC;QAED,MAAM,gBAAgB,GAAG,QAAQ;aAC5B,GAAG,CAAC,CAAC,EAAE,UAAU,EAAE,YAAY,EAAE,EAAE,EAAE;YAClC,OAAO,sBAAsB,YAAY,yBAAyB,UAAU,IAAI,CAAA;QACpF,CAAC,CAAC;aACD,IAAI,CAAC,MAAM,CAAC,CAAA;QACjB,MAAM,UAAU,GACZ,iEAAiE;YACjE,4JAA4J;YAC5J,sCAAsC;YACtC,kEAAkE;YAClE,4GAA4G;YAC5G,sJAAsJ;YACtJ,+BAA+B;YAC/B,gBAAgB,CAAA;QAEpB,MAAM,oBAAoB,GAAG,QAAQ;aAChC,GAAG,CAAC,CAAC,EAAE,UAAU,EAAE,YAAY,EAAE,EAAE,EAAE;YAClC,OAAO,sBAAsB,YAAY,0BAA0B,UAAU,IAAI,CAAA;QACrF,CAAC,CAAC;aACD,IAAI,CAAC,MAAM,CAAC,CAAA;QAEjB,MAAM,cAAc,GAChB,iHAAiH;YACjH,sDAAsD;YACtD,8KAA8K;YAC9K,8BAA8B;YAC9B,6DAA6D;YAC7D,uEAAuE;YACvE,8GAA8G;YAC9G,kCAAkC,oBAAoB,GAAG,CAAA;QAE7D,MAAM,UAAU,GACZ,8IAA8I;YAC9I,4IAA4I;YAC5I,mCAAmC;YACnC,sBAAsB;YACtB,4DAA4D;YAC5D,sGAAsG;YACtG,oEAAoE;YACpE,6DAA6D;YAC7D,iEAAiE;YACjE,uEAAuE;YACvE,+DAA+D,oBAAoB,GAAG,CAAA;QAE1F,MAAM,oBAAoB,GAAG,QAAQ;aAChC,GAAG,CAAC,CAAC,EAAE,UAAU,EAAE,YAAY,EAAE,EAAE,EAAE;YAClC,OAAO,sBAAsB,YAAY,2BAA2B,UAAU,IAAI,CAAA;QACtF,CAAC,CAAC;aACD,IAAI,CAAC,MAAM,CAAC,CAAA;QACjB,MAAM,cAAc,GAChB,sJAAsJ;YACtJ,8MAA8M;YAC9M,SAAS;YACT,gMAAgM;YAChM,kLAAkL;YAClL,iLAAiL;YACjL,uBAAuB;YACvB,qEAAqE;YACrE,sEAAsE;YACtE,qCAAqC,oBAAoB,IAAI;YAC7D,UAAU;YACV,6GAA6G;YAC7G,+DAA+D;YAC/D,qEAAqE;YACrE,+GAA+G,CAAA;QAEnH,MAAM,YAAY,GAAG,QAAQ;aACxB,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,IAAI,OAAO,CAAC,YAAY,GAAG,CAAC;aAC7C,IAAI,CAAC,IAAI,CAAC,CAAA;QACf,MAAM,QAAQ,GACV,8EAA8E;YAC9E,qBAAqB;YACrB,0DAA0D;YAC1D,kEAAkE;YAClE,2BAA2B,YAAY,IAAI;YAC3C,wBAAwB,CAAA;QAE5B,MAAM,CACF,SAAS,EACT,aAAa,EACb,SAAS,EACT,aAAa,EACb,OAAO,EACV,GAAsB,MAAM,OAAO,CAAC,GAAG,CAAC;YACrC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;YACtB,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;YAC1B,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;YACtB,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;YAC1B,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;SACvB,CAAC,CAAA;QAEF,kCAAkC;QAClC,OAAO,OAAO,CAAC,GAAG,CACd,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YAC3B,MAAM,KAAK,GAAG,IAAI,KAAK,EAAE,CAAA;YAEzB,MAAM,gBAAgB,GAAG,CAAC,QAAa,EAAE,GAAW,EAAE,EAAE;gBACpD,OAAO,QAAQ,CAAC,GAAG,CAAC,KAAK,aAAa;oBAClC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM;wBACxB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,KAAK,aAAa,CAAC;oBACjD,CAAC,CAAC,SAAS;oBACX,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAA;YACvB,CAAC,CAAA;YAED,mEAAmE;YACnE,MAAM,MAAM,GAAG,gBAAgB,CAAC,OAAO,EAAE,cAAc,CAAC,CAAA;YACxD,KAAK,CAAC,QAAQ,GAAG,eAAe,CAAA;YAChC,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC,cAAc,CAAC,CAAA;YACtC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CACnC,OAAO,CAAC,YAAY,CAAC,EACrB,MAAM,CACT,CAAA;YAED,yCAAyC;YACzC,KAAK,CAAC,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAC7B,SAAS;iBACJ,MAAM,CACH,CAAC,QAAQ,EAAE,EAAE,CACT,QAAQ,CAAC,YAAY,CAAC;gBAClB,OAAO,CAAC,YAAY,CAAC;gBACzB,QAAQ,CAAC,cAAc,CAAC;oBACpB,OAAO,CAAC,cAAc,CAAC,CAClC;iBACA,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE;gBACpB,MAAM,iBAAiB,GAAG,aAAa,CAAC,MAAM,CAC1C,CAAC,YAAY,EAAE,EAAE;oBACb,OAAO,CACH,YAAY,CAAC,YAAY,CAAC;wBACtB,QAAQ,CAAC,YAAY,CAAC;wBAC1B,YAAY,CAAC,cAAc,CAAC;4BACxB,QAAQ,CAAC,cAAc,CAAC;wBAC5B,YAAY,CAAC,aAAa,CAAC;4BACvB,QAAQ,CAAC,aAAa,CAAC,CAC9B,CAAA;gBACL,CAAC,CACJ,CAAA;gBAED,MAAM,WAAW,GAAG,IAAI,WAAW,EAAE,CAAA;gBACrC,WAAW,CAAC,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAA;gBAE1C,WAAW,CAAC,IAAI;oBACZ,QAAQ,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAA;gBAC3C,IACI,QAAQ,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC;oBAC5C,CAAC,CAAC,EACJ,CAAC;oBACC,WAAW,CAAC,SAAS,GAAG,QAAQ,CAC5B,eAAe,CAClB,CAAC,MAAM,CACJ,QAAQ,CAAC,eAAe,CAAC,CAAC,OAAO,CAC7B,SAAS,CACZ;wBACG,SAAS,CAAC,MAAM;wBAChB,CAAC,EACL,QAAQ,CAAC,eAAe,CAAC,CAAC,MAAM,CACnC,CAAA;oBACD,WAAW,CAAC,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC,MAAM,CACtC,CAAC,EACD,QAAQ,CAAC,eAAe,CAAC,CAAC,OAAO,CAC7B,SAAS,CACZ,GAAG,CAAC,CACR,CAAA;gBACL,CAAC;gBAED,IAAI,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;oBACpC,WAAW,CAAC,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC,MAAM,CACtC,CAAC,EACD,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAChC,CAAA;gBAEL,IACI,WAAW,CAAC,IAAI,KAAK,SAAS;oBAC9B,WAAW,CAAC,IAAI,KAAK,SAAS,EAChC,CAAC;oBACC,IACI,QAAQ,CAAC,mBAAmB,CAAC,KAAK,IAAI;wBACtC,CAAC,IAAI,CAAC,wBAAwB,CAC1B,KAAK,EACL,WAAW,EACX,QAAQ,CAAC,mBAAmB,CAAC,CAChC,EACH,CAAC;wBACC,WAAW,CAAC,SAAS,GAAG,QAAQ,CAC5B,QAAQ,CAAC,mBAAmB,CAAC,CAChC,CAAA;oBACL,CAAC;yBAAM,IACH,QAAQ,CAAC,eAAe,CAAC,KAAK,IAAI;wBAClC,CAAC,IAAI,CAAC,oBAAoB,CACtB,KAAK,EACL,WAAW,EACX,QAAQ,CAAC,eAAe,CAAC,CAC5B,EACH,CAAC;wBACC,WAAW,CAAC,SAAS,GAAG,SAAS,CAAA;oBACrC,CAAC;oBACD,IACI,QAAQ,CAAC,eAAe,CAAC,KAAK,IAAI;wBAClC,CAAC,IAAI,CAAC,oBAAoB,CACtB,KAAK,EACL,WAAW,EACX,QAAQ,CAAC,eAAe,CAAC,CAC5B,EACH,CAAC;wBACC,WAAW,CAAC,KAAK,GAAG,QAAQ,CACxB,QAAQ,CAAC,eAAe,CAAC,CAC5B,CAAA;oBACL,CAAC;yBAAM,IACH,QAAQ,CAAC,mBAAmB,CAAC,KAAK,IAAI;wBACtC,CAAC,IAAI,CAAC,wBAAwB,CAC1B,KAAK,EACL,WAAW,EACX,QAAQ,CAAC,mBAAmB,CAAC,CAChC,EACH,CAAC;wBACC,WAAW,CAAC,KAAK,GAAG,SAAS,CAAA;oBACjC,CAAC;gBACL,CAAC;gBAED,4DAA4D;gBAC5D,sGAAsG;gBACtG,2GAA2G;gBAC3G,OAAO;gBACP,mEAAmE;gBACnE,IAAI,OAAO,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAA;gBAClC,IAAI,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC7B,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,OAAO,CAAC,MAAM,CAAC,CAAA;gBAC/C,CAAC;gBAED,MAAM,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;oBACrC,OAAO,MAAM,CAAC,MAAM,CAAC,KAAK,OAAO,CAAA;gBACrC,CAAC,CAAC,CAAA;gBACF,IAAI,QAAQ,EAAE,CAAC;oBACX,2CAA2C;oBAC3C,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CACpC,KAAK,EACL,WAAW,EACX,KAAK,EACL,IAAI,CACP,CAAA;oBACD,MAAM,QAAQ,GACV,aAAa,KAAK,QAAQ,CAAC,MAAM,CAAC;wBAC9B,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC;wBAClB,CAAC,CAAC,SAAS,CAAA;oBAEnB,WAAW,CAAC,IAAI,GAAG,MAAM,CAAA;oBACzB,WAAW,CAAC,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;oBAC/C,WAAW,CAAC,QAAQ,GAAG,QAAQ,CAAA;gBACnC,CAAC;gBAED,IACI,QAAQ,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,KAAK,OAAO,EACjD,CAAC;oBACC,WAAW,CAAC,OAAO,GAAG,IAAI,CAAA;oBAC1B,IAAI,CAAC,QAAQ,EAAE,CAAC;wBACZ,MAAM,IAAI,GAAG,QAAQ,CAAC,eAAe,CAAC;6BACjC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;6BACjB,WAAW,EAAE,CAAA;wBAClB,WAAW,CAAC,IAAI;4BACZ,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa,CAAC;gCACjC,IAAI,EAAE,IAAI;6BACb,CAAC,CAAA;oBACV,CAAC;gBACL,CAAC;gBAED,+CAA+C;gBAC/C,IACI,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,OAAO,CACrC,WAAW,CAAC,IAAkB,CACjC,KAAK,CAAC,CAAC;oBACR,QAAQ,CAAC,0BAA0B,CAAC,EACtC,CAAC;oBACC,MAAM,MAAM,GACR,QAAQ,CACJ,0BAA0B,CAC7B,CAAC,QAAQ,EAAE,CAAA;oBAChB,WAAW,CAAC,MAAM;wBACd,CAAC,IAAI,CAAC,qBAAqB,CACvB,KAAK,EACL,WAAW,EACX,MAAM,CACT;4BACG,CAAC,CAAC,MAAM;4BACR,CAAC,CAAC,EAAE,CAAA;gBAChB,CAAC;gBACD,WAAW,CAAC,UAAU;oBAClB,QAAQ,CAAC,aAAa,CAAC,KAAK,KAAK,CAAA;gBAErC,MAAM,iBAAiB,GAAG,iBAAiB,CAAC,IAAI,CAC5C,CAAC,UAAU,EAAE,EAAE,CACX,UAAU,CAAC,iBAAiB,CAAC,KAAK,SAAS,CAClD,CAAA;gBACD,IAAI,iBAAiB,EAAE,CAAC;oBACpB,WAAW,CAAC,SAAS,GAAG,IAAI,CAAA;oBAC5B,0DAA0D;oBAC1D,MAAM,yBAAyB,GAC3B,aAAa,CAAC,MAAM,CAChB,CAAC,UAAU,EAAE,EAAE,CACX,UAAU,CAAC,YAAY,CAAC;wBACpB,QAAQ,CAAC,YAAY,CAAC;wBAC1B,UAAU,CAAC,cAAc,CAAC;4BACtB,QAAQ,CAAC,cAAc,CAAC;wBAC5B,UAAU,CAAC,aAAa,CAAC;4BACrB,QAAQ,CAAC,aAAa,CAAC;wBAC3B,UAAU,CAAC,iBAAiB,CAAC;4BACzB,SAAS,CACpB,CAAA;oBAEL,2BAA2B;oBAC3B,MAAM,WAAW,GACb,yBAAyB,CAAC,GAAG,CACzB,CAAC,UAAU,EAAE,EAAE,CACX,UAAU,CAAC,aAAa,CAAC,CAChC,CAAA;oBACL,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAA;oBAEzC,4CAA4C;oBAC5C,MAAM,MAAM,GACR,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,KAAK,EACL,WAAW,CACd,CAAA;oBAEL,4EAA4E;oBAC5E,IACI,iBAAiB,CAAC,iBAAiB,CAAC;wBACpC,MAAM,EACR,CAAC;wBACC,WAAW,CAAC,wBAAwB;4BAChC,iBAAiB,CAAC,iBAAiB,CAAC,CAAA;oBAC5C,CAAC;gBACL,CAAC;gBAED,MAAM,iBAAiB,GAAG,iBAAiB,CAAC,MAAM,CAC9C,CAAC,UAAU,EAAE,EAAE,CACX,UAAU,CAAC,iBAAiB,CAAC,KAAK,QAAQ,CACjD,CAAA;gBACD,MAAM,qBAAqB,GACvB,iBAAiB,CAAC,KAAK,CAAC,CAAC,gBAAgB,EAAE,EAAE;oBACzC,OAAO,aAAa,CAAC,IAAI,CACrB,CAAC,YAAY,EAAE,EAAE,CACb,YAAY,CAAC,iBAAiB,CAAC;wBAC3B,QAAQ;wBACZ,YAAY,CAAC,iBAAiB,CAAC;4BAC3B,gBAAgB,CACZ,iBAAiB,CACpB;wBACL,YAAY,CAAC,aAAa,CAAC;4BACvB,QAAQ,CAAC,aAAa,CAAC,CAClC,CAAA;gBACL,CAAC,CAAC,CAAA;gBACN,WAAW,CAAC,QAAQ;oBAChB,iBAAiB,CAAC,MAAM,GAAG,CAAC;wBAC5B,CAAC,qBAAqB,CAAA;gBAE1B,IACI,QAAQ,CAAC,gBAAgB,CAAC,KAAK,IAAI;oBACnC,QAAQ,CAAC,gBAAgB,CAAC,KAAK,SAAS,EAC1C,CAAC;oBACC,IACI,QAAQ,CAAC,gBAAgB,CAAC;wBAC1B,gBAAgB,EAClB,CAAC;wBACC,WAAW,CAAC,WAAW,GAAG,IAAI,CAAA;wBAC9B,WAAW,CAAC,kBAAkB,GAAG,OAAO,CAAA;oBAC5C,CAAC;yBAAM,IACH,QAAQ,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAC9B,SAAS,CACZ,KAAK,CAAC,CAAC,EACV,CAAC;wBACC,WAAW,CAAC,WAAW,GAAG,IAAI,CAAA;wBAC9B,WAAW,CAAC,kBAAkB,GAAG,WAAW,CAAA;oBAChD,CAAC;yBAAM,IACH,QAAQ,CAAC,gBAAgB,CAAC;wBAC1B,mBAAmB,EACrB,CAAC;wBACC,WAAW,CAAC,WAAW,GAAG,IAAI,CAAA;wBAC9B,WAAW,CAAC,kBAAkB,GAAG,MAAM,CAAA;oBAC3C,CAAC;yBAAM,CAAC;wBACJ,WAAW,CAAC,OAAO,GAAG,QAAQ,CAC1B,gBAAgB,CACnB,CAAC,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAA;wBAChC,WAAW,CAAC,OAAO;4BACf,WAAW,CAAC,OAAO,CAAC,OAAO,CACvB,cAAc,EACd,MAAM,CACT,CAAA;wBAEL,IAAI,QAAQ,EAAE,CAAC;4BACX,WAAW,CAAC,OAAO;gCACf,WAAW,CAAC,OAAO,CAAC,OAAO,CACvB,IAAI,QAAQ,CAAC,MAAM,CAAC,EAAE,EACtB,EAAE,CACL,CAAA;wBACT,CAAC;oBACL,CAAC;gBACL,CAAC;gBAED,IACI,CAAC,QAAQ,CAAC,cAAc,CAAC,KAAK,KAAK;oBAC/B,QAAQ,CAAC,cAAc,CAAC,KAAK,QAAQ,CAAC;oBAC1C,QAAQ,CAAC,uBAAuB,CAAC,EACnC,CAAC;oBACC,WAAW,CAAC,aAAa;wBACrB,QAAQ,CAAC,gBAAgB,CAAC,KAAK,GAAG;4BAC9B,CAAC,CAAC,QAAQ;4BACV,CAAC,CAAC,SAAS,CAAA;oBACnB,0GAA0G;oBAC1G,MAAM,iBAAiB,GACnB,IAAI,CAAC,wBAAwB,CAAC;wBAC1B,MAAM,EAAE,OAAO,CAAC,cAAc,CAAC;wBAC/B,KAAK,EAAE,OAAO,CAAC,YAAY,CAAC;wBAC5B,IAAI,EAAE,iBAAiB,CAAC,gBAAgB;wBACxC,IAAI,EAAE,WAAW,CAAC,IAAI;qBACzB,CAAC,CAAA;oBAEN,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,KAAK,CAC5B,iBAAiB,CAAC,KAAK,EACvB,iBAAiB,CAAC,UAAU,CAC/B,CAAA;oBACD,IAAI,OAAO,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;wBACjC,WAAW,CAAC,YAAY,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAA;oBAC/C,CAAC;yBAAM,CAAC;wBACJ,WAAW,CAAC,YAAY,GAAG,EAAE,CAAA;oBACjC,CAAC;gBACL,CAAC;gBAED,WAAW,CAAC,OAAO;oBACf,QAAQ,CAAC,aAAa,CAAC,IAAI,IAAI;wBAC3B,CAAC,CAAC,SAAS;wBACX,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAA;gBACjC,IAAI,QAAQ,CAAC,oBAAoB,CAAC;oBAC9B,WAAW,CAAC,OAAO;wBACf,QAAQ,CAAC,oBAAoB,CAAC,CAAA;gBAEtC,IACI,WAAW,CAAC,IAAI,KAAK,UAAU;oBAC/B,WAAW,CAAC,IAAI,KAAK,WAAW,EAClC,CAAC;oBACC,MAAM,GAAG,GACL,iBAAiB;wBACjB,uEAAuE;wBACvE,MAAM,WAAW,CAAC,IAAI,yCAAyC;wBAC/D,SAAS,WAAW,CAAC,IAAI,WAAW;wBACpC,SAAS;wBACT,0BAA0B,QAAQ,CAAC,aAAa,CAAC,QAAQ;wBACzD,qBAAqB,QAAQ,CAAC,cAAc,CAAC,QAAQ;wBACrD,mBAAmB,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAA;oBAEhD,MAAM,OAAO,GACT,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;oBAEzB,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBACrB,WAAW,CAAC,kBAAkB;4BAC1B,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;wBACnB,WAAW,CAAC,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI;4BAC9B,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;4BAC3B,CAAC,CAAC,SAAS,CAAA;oBACnB,CAAC;gBACL,CAAC;gBAED,OAAO,WAAW,CAAA;YACtB,CAAC,CAAC,CACT,CAAA;YAED,yFAAyF;YACzF,MAAM,sBAAsB,GAAG,QAAQ,CAAC,IAAI,CACxC,aAAa,CAAC,MAAM,CAAC,CAAC,YAAY,EAAE,EAAE;gBAClC,OAAO,CACH,YAAY,CAAC,YAAY,CAAC;oBACtB,OAAO,CAAC,YAAY,CAAC;oBACzB,YAAY,CAAC,cAAc,CAAC;wBACxB,OAAO,CAAC,cAAc,CAAC;oBAC3B,YAAY,CAAC,iBAAiB,CAAC,KAAK,QAAQ,CAC/C,CAAA;YACL,CAAC,CAAC,EACF,CAAC,YAAY,EAAE,EAAE,CAAC,YAAY,CAAC,iBAAiB,CAAC,CACpD,CAAA;YAED,KAAK,CAAC,OAAO,GAAG,sBAAsB,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;gBACtD,MAAM,OAAO,GAAG,aAAa,CAAC,MAAM,CAChC,CAAC,GAAG,EAAE,EAAE,CACJ,GAAG,CAAC,iBAAiB,CAAC;oBACtB,UAAU,CAAC,iBAAiB,CAAC,CACpC,CAAA;gBACD,OAAO,IAAI,WAAW,CAAC;oBACnB,IAAI,EAAE,UAAU,CAAC,iBAAiB,CAAC;oBACnC,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC;iBACpD,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;YAEF,uFAAuF;YACvF,MAAM,qBAAqB,GAAG,QAAQ,CAAC,IAAI,CACvC,aAAa,CAAC,MAAM,CAAC,CAAC,YAAY,EAAE,EAAE;gBAClC,OAAO,CACH,YAAY,CAAC,YAAY,CAAC;oBACtB,OAAO,CAAC,YAAY,CAAC;oBACzB,YAAY,CAAC,cAAc,CAAC;wBACxB,OAAO,CAAC,cAAc,CAAC;oBAC3B,YAAY,CAAC,iBAAiB,CAAC,KAAK,OAAO,CAC9C,CAAA;YACL,CAAC,CAAC,EACF,CAAC,YAAY,EAAE,EAAE,CAAC,YAAY,CAAC,iBAAiB,CAAC,CACpD,CAAA;YAED,KAAK,CAAC,MAAM,GAAG,qBAAqB,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;gBACpD,MAAM,MAAM,GAAG,aAAa,CAAC,MAAM,CAC/B,CAAC,GAAG,EAAE,EAAE,CACJ,GAAG,CAAC,iBAAiB,CAAC;oBACtB,UAAU,CAAC,iBAAiB,CAAC,CACpC,CAAA;gBACD,OAAO,IAAI,UAAU,CAAC;oBAClB,IAAI,EAAE,UAAU,CAAC,iBAAiB,CAAC;oBACnC,WAAW,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC;oBAChD,UAAU,EAAE,UAAU,CAAC,YAAY,CAAC,CAAC,OAAO,CACxC,2BAA2B,EAC3B,IAAI,CACP;iBACJ,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;YAEF,+FAA+F;YAC/F,MAAM,yBAAyB,GAAG,QAAQ,CAAC,IAAI,CAC3C,aAAa,CAAC,MAAM,CAAC,CAAC,YAAY,EAAE,EAAE;gBAClC,OAAO,CACH,YAAY,CAAC,YAAY,CAAC;oBACtB,OAAO,CAAC,YAAY,CAAC;oBACzB,YAAY,CAAC,cAAc,CAAC;wBACxB,OAAO,CAAC,cAAc,CAAC;oBAC3B,YAAY,CAAC,iBAAiB,CAAC,KAAK,SAAS,CAChD,CAAA;YACL,CAAC,CAAC,EACF,CAAC,YAAY,EAAE,EAAE,CAAC,YAAY,CAAC,iBAAiB,CAAC,CACpD,CAAA;YAED,KAAK,CAAC,UAAU,GAAG,yBAAyB,CAAC,GAAG,CAC5C,CAAC,UAAU,EAAE,EAAE;gBACX,OAAO,IAAI,cAAc,CAAC;oBACtB,IAAI,EAAE,UAAU,CAAC,iBAAiB,CAAC;oBACnC,UAAU,EAAE,UAAU,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,wCAAwC;iBAC9F,CAAC,CAAA;YACN,CAAC,CACJ,CAAA;YAED,kGAAkG;YAClG,MAAM,0BAA0B,GAAG,QAAQ,CAAC,IAAI,CAC5C,aAAa,CAAC,MAAM,CAAC,CAAC,YAAY,EAAE,EAAE;gBAClC,OAAO,CACH,YAAY,CAAC,YAAY,CAAC;oBACtB,OAAO,CAAC,YAAY,CAAC;oBACzB,YAAY,CAAC,cAAc,CAAC;wBACxB,OAAO,CAAC,cAAc,CAAC,CAC9B,CAAA;YACL,CAAC,CAAC,EACF,CAAC,YAAY,EAAE,EAAE,CAAC,YAAY,CAAC,iBAAiB,CAAC,CACpD,CAAA;YAED,KAAK,CAAC,WAAW,GAAG,0BAA0B,CAAC,GAAG,CAC9C,CAAC,YAAY,EAAE,EAAE;gBACb,MAAM,WAAW,GAAG,aAAa,CAAC,MAAM,CACpC,CAAC,IAAI,EAAE,EAAE,CACL,IAAI,CAAC,iBAAiB,CAAC;oBACvB,YAAY,CAAC,iBAAiB,CAAC,CACtC,CAAA;gBAED,2GAA2G;gBAC3G,MAAM,MAAM,GAAG,gBAAgB,CAC3B,YAAY,EACZ,yBAAyB,CAC5B,CAAA;gBACD,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAClD,YAAY,CAAC,uBAAuB,CAAC,EACrC,MAAM,CACT,CAAA;gBAED,OAAO,IAAI,eAAe,CAAC;oBACvB,IAAI,EAAE,YAAY,CAAC,iBAAiB,CAAC;oBACrC,WAAW,EAAE,WAAW,CAAC,GAAG,CACxB,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,CAChC;oBACD,gBAAgB,EACZ,YAAY,CAAC,yBAAyB,CAAC;oBAC3C,mBAAmB,EAAE,mBAAmB;oBACxC,qBAAqB,EAAE,WAAW,CAAC,GAAG,CAClC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAC3C;oBACD,QAAQ,EAAE,YAAY,CAAC,WAAW,CAAC;oBACnC,QAAQ,EAAE,YAAY,CAAC,WAAW,CAAC;iBACtC,CAAC,CAAA;YACN,CAAC,CACJ,CAAA;YAED,uFAAuF;YACvF,MAAM,qBAAqB,GAAG,QAAQ,CAAC,IAAI,CACvC,SAAS,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,EAAE;gBACzB,OAAO,CACH,OAAO,CAAC,YAAY,CAAC,KAAK,OAAO,CAAC,YAAY,CAAC;oBAC/C,OAAO,CAAC,cAAc,CAAC,KAAK,OAAO,CAAC,cAAc,CAAC,CACtD,CAAA;YACL,CAAC,CAAC,EACF,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAC1C,CAAA;YAED,KAAK,CAAC,OAAO,GAAG,qBAAqB,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;gBACrD,MAAM,OAAO,GAAG,SAAS,CAAC,MAAM,CAC5B,CAAC,KAAK,EAAE,EAAE,CACN,KAAK,CAAC,iBAAiB,CAAC;oBACxB,UAAU,CAAC,iBAAiB,CAAC,CACpC,CAAA;gBACD,OAAO,IAAI,UAAU,CAAoB;oBACrC,KAAK,EAAE,KAAK;oBACZ,IAAI,EAAE,UAAU,CAAC,iBAAiB,CAAC;oBACnC,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC;oBACjD,QAAQ,EAAE,UAAU,CAAC,WAAW,CAAC,KAAK,MAAM;oBAC5C,KAAK,EAAE,UAAU,CAAC,WAAW,CAAC;oBAC9B,SAAS,EAAE,OAAO,CAAC,KAAK,CACpB,CAAC,CAAC,EAAE,EAAE,CACF,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,CAC5B,CAAC,CAAC,WAAW,CAAC,CACjB,IAAI,CAAC,CACb;oBACD,UAAU,EAAE,KAAK;iBACpB,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;YAEF,OAAO,KAAK,CAAA;QAChB,CAAC,CAAC,CACL,CAAA;IACL,CAAC;IAED;;OAEG;IACO,cAAc,CAAC,KAAY,EAAE,iBAA2B;QAC9D,MAAM,iBAAiB,GAAG,KAAK,CAAC,OAAO;aAClC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;aACzD,IAAI,CAAC,IAAI,CAAC,CAAA;QACf,IAAI,GAAG,GAAG,gBAAgB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,iBAAiB,EAAE,CAAA;QAExE,KAAK,CAAC,OAAO;aACR,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC;aACnC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YAChB,MAAM,aAAa,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CACpC,CAAC,MAAM,EAAE,EAAE,CACP,MAAM,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;gBAC/B,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,IAAI,CAC5C,CAAA;YACD,IAAI,CAAC,aAAa;gBACd,KAAK,CAAC,OAAO,CAAC,IAAI,CACd,IAAI,WAAW,CAAC;oBACZ,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CACrD,KAAK,EACL,CAAC,MAAM,CAAC,IAAI,CAAC,CAChB;oBACD,WAAW,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC;iBAC7B,CAAC,CACL,CAAA;QACT,CAAC,CAAC,CAAA;QAEN,KAAK,CAAC,OAAO;aACR,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC;aACjC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YACf,KAAK,CAAC,OAAO,CAAC,IAAI,CACd,IAAI,WAAW,CAAC;gBACZ,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CACrD,KAAK,EACL,KAAK,CAAC,WAAW,CACpB;gBACD,WAAW,EAAE,KAAK,CAAC,WAAW;aACjC,CAAC,CACL,CAAA;QACL,CAAC,CAAC,CAAA;QAEN,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3B,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO;iBAC3B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;gBACZ,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI;oBAC1B,CAAC,CAAC,MAAM,CAAC,IAAI;oBACb,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CAC/C,KAAK,EACL,MAAM,CAAC,WAAW,CACrB,CAAA;gBACP,MAAM,WAAW,GAAG,MAAM,CAAC,WAAW;qBACjC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,UAAU,GAAG,CAAC;qBACtC,IAAI,CAAC,IAAI,CAAC,CAAA;gBACf,OAAO,eAAe,UAAU,aAAa,WAAW,GAAG,CAAA;YAC/D,CAAC,CAAC;iBACD,IAAI,CAAC,IAAI,CAAC,CAAA;YAEf,GAAG,IAAI,KAAK,UAAU,EAAE,CAAA;QAC5B,CAAC;QAED,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1B,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM;iBACzB,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;gBACX,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI;oBACxB,CAAC,CAAC,KAAK,CAAC,IAAI;oBACZ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,mBAAmB,CAC9C,KAAK,EACL,KAAK,CAAC,UAAW,CACpB,CAAA;gBACP,OAAO,eAAe,SAAS,YAAY,KAAK,CAAC,UAAU,GAAG,CAAA;YAClE,CAAC,CAAC;iBACD,IAAI,CAAC,IAAI,CAAC,CAAA;YAEf,GAAG,IAAI,KAAK,SAAS,EAAE,CAAA;QAC3B,CAAC;QAED,IAAI,KAAK,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,IAAI,iBAAiB,EAAE,CAAC;YACpD,MAAM,cAAc,GAAG,KAAK,CAAC,WAAW;iBACnC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE;gBACR,MAAM,WAAW,GAAG,EAAE,CAAC,WAAW;qBAC7B,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,UAAU,GAAG,CAAC;qBACtC,IAAI,CAAC,IAAI,CAAC,CAAA;gBACf,IAAI,CAAC,EAAE,CAAC,IAAI;oBACR,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACnD,KAAK,EACL,EAAE,CAAC,WAAW,EACd,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,EACrB,EAAE,CAAC,qBAAqB,CAC3B,CAAA;gBACL,MAAM,qBAAqB,GAAG,EAAE,CAAC,qBAAqB;qBACjD,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,UAAU,GAAG,CAAC;qBACtC,IAAI,CAAC,IAAI,CAAC,CAAA;gBAEf,IAAI,UAAU,GAAG,eACb,EAAE,CAAC,IACP,kBAAkB,WAAW,gBAAgB,IAAI,CAAC,UAAU,CACxD,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CACxB,KAAK,qBAAqB,GAAG,CAAA;gBAC9B,IAAI,EAAE,CAAC,QAAQ;oBAAE,UAAU,IAAI,cAAc,EAAE,CAAC,QAAQ,EAAE,CAAA;gBAC1D,IAAI,EAAE,CAAC,QAAQ;oBAAE,UAAU,IAAI,cAAc,EAAE,CAAC,QAAQ,EAAE,CAAA;gBAE1D,OAAO,UAAU,CAAA;YACrB,CAAC,CAAC;iBACD,IAAI,CAAC,IAAI,CAAC,CAAA;YAEf,GAAG,IAAI,KAAK,cAAc,EAAE,CAAA;QAChC,CAAC;QAED,MAAM,cAAc,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CACvC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,SAAS,CAC/B,CAAA;QACD,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5B,MAAM,cAAc,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;gBAC7D,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;gBAC5C,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,KAAK,EACL,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAC9C,CAAA;YAEP,MAAM,WAAW,GAAG,cAAc;iBAC7B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC;iBACnC,IAAI,CAAC,IAAI,CAAC,CAAA;YACf,GAAG,IAAI,iBAAiB,cAAc,kBAAkB,WAAW,GAAG,CAAA;QAC1E,CAAC;QAED,GAAG,IAAI,GAAG,CAAA;QAEV,KAAK,CAAC,OAAO;aACR,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC;aAC1B,OAAO,CACJ,CAAC,EAAE,EAAE,EAAE,CACH,CAAC,GAAG,IAAI,uBAAuB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KACjD,EAAE,CAAC,IACP,QAAQ,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,CAChD,CAAA;QAEL,OAAO,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;IACzB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACZ,MAAM,MAAM,GAA0B,MAAM,IAAI,CAAC,KAAK,CAClD,+BAA+B,CAClC,CAAA;QACD,MAAM,aAAa,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAA;QAEvC,OAAO,aAAa,CAAC,OAAO,CAAC,gCAAgC,EAAE,IAAI,CAAC,CAAA;IACxE,CAAC;IAED;;OAEG;IACO,YAAY,CAAC,WAA2B;QAC9C,OAAO,IAAI,KAAK,CAAC,cAAc,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC,CAAA;IAClE,CAAC;IAES,aAAa,CAAC,IAAU;QAC9B,IAAI,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ,EAAE,CAAC;YACtC,OAAO,IAAI,KAAK,CACZ,eAAe,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,UAAU,EAAE,CAC/D,CAAA;QACL,CAAC;aAAM,CAAC;YACJ,OAAO,IAAI,KAAK,CACZ,eAAe,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,IAAI;iBAC1C,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC;iBAC3B,QAAQ,EAAE,EAAE,CACpB,CAAA;QACL,CAAC;IACL,CAAC;IAES,KAAK,CAAC,uBAAuB,CAAC,IAAU;QAC9C,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;QACnD,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA;QAClE,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,MAAM,GAAG,aAAa,CAAA;QAC1B,CAAC;QAED,MAAM,UAAU,GACZ,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ;YAC/B,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE;YACxB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,QAAQ,EAAE,CAAA;QACrD,OAAO,IAAI,CAAC,wBAAwB,CAAC;YACjC,IAAI,EAAE,iBAAiB,CAAC,IAAI;YAC5B,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,IAAI;YACV,KAAK,EAAE,UAAU;SACpB,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACO,WAAW,CAAC,UAAyB;QAC3C,OAAO,IAAI,KAAK,CAAC,aAAa,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC,CAAA;IAChE,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,uBAAuB,CACnC,UAAyB;QAEzB,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;QAEnD,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,CAAA;QAExE,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,MAAM,GAAG,aAAa,CAAA;QAC1B,CAAC;QAED,OAAO,IAAI,CAAC,wBAAwB,CAAC;YACjC,IAAI,EAAE,iBAAiB,CAAC,IAAI;YAC5B,MAAM;YACN,IAAI;SACP,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,aAAa,CAAC,WAAmB;QAC7C,MAAM,gBAAgB,GAClB,0GAA0G;YAC1G,0DAA0D;YAC1D,kEAAkE;YAClE,2BAA2B,WAAW,yCAAyC,CAAA;QACnF,MAAM,WAAW,GAAoB,MAAM,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAA;QACvE,MAAM,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAA;IACrE,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,WAAW,CACvB,KAAY,EACZ,MAAmB;QAEnB,IAAI,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;QAElD,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;QAC1C,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;QAC/D,MAAM,GAAG,GACL,yDAAyD;YACzD,kEAAkE;YAClE,0BAA0B,MAAM,0BAA0B,QAAQ,GAAG,CAAA;QACzE,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QACpC,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAA;IACvC,CAAC;IAED;;OAEG;IACO,iBAAiB,CACvB,KAAY,EACZ,MAAmB,EACnB,QAAiB;QAEjB,IAAI,CAAC,QAAQ;YAAE,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;QAC3D,MAAM,UAAU,GAAG,MAAM;aACpB,IAAK,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,UAAU,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC;aACxD,IAAI,CAAC,IAAI,CAAC,CAAA;QACf,OAAO,IAAI,KAAK,CAAC,eAAe,QAAQ,YAAY,UAAU,GAAG,CAAC,CAAA;IACtE,CAAC;IAED;;OAEG;IACO,eAAe,CACrB,KAAY,EACZ,MAAmB,EACnB,QAAiB;QAEjB,IAAI,CAAC,QAAQ;YAAE,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;QAC3D,OAAO,IAAI,KAAK,CAAC,aAAa,QAAQ,EAAE,CAAC,CAAA;IAC7C,CAAC;IAED;;;OAGG;IACO,cAAc,CAAC,KAAY,EAAE,KAAiB;QACpD,MAAM,OAAO,GAAG,KAAK,CAAC,WAAW;aAC5B,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,UAAU,GAAG,CAAC;aACtC,IAAI,CAAC,IAAI,CAAC,CAAA;QACf,OAAO,IAAI,KAAK,CACZ,UAAU,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,UACrC,KAAK,CAAC,IACV,QAAQ,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAC1B,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,EACtC,IAAI,OAAO,KAAK,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAC9D,CAAA;IACL,CAAC;IAED;;OAEG;IACO,YAAY,CAClB,KAAY,EACZ,WAA8C;QAE9C,MAAM,SAAS,GACX,eAAe,CAAC,YAAY,CAAC,WAAW,CAAC;YACzC,eAAe,CAAC,aAAa,CAAC,WAAW,CAAC;YACtC,CAAC,CAAC,WAAW,CAAC,IAAI;YAClB,CAAC,CAAC,WAAW,CAAA;QACrB,OAAO,IAAI,KAAK,CACZ,cAAc,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,SAAS,WAAW,CAChE,CAAA;IACL,CAAC;IAED;;OAEG;IACO,mBAAmB,CACzB,KAAY,EACZ,WAAqB,EACrB,cAAuB;QAEvB,MAAM,cAAc,GAAG,cAAc;YACjC,CAAC,CAAC,cAAc;YAChB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,KAAK,EAAE,WAAW,CAAC,CAAA;QACvE,MAAM,iBAAiB,GAAG,WAAW;aAChC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,UAAU,GAAG,CAAC;aACtC,IAAI,CAAC,IAAI,CAAC,CAAA;QACf,OAAO,IAAI,KAAK,CACZ,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBAAoB,cAAc,kBAAkB,iBAAiB,GAAG,CAC5E,CAAA;IACL,CAAC;IAED;;OAEG;IACO,iBAAiB,CAAC,KAAY;QACpC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,MAAM;YAC5B,MAAM,IAAI,YAAY,CAAC,SAAS,KAAK,uBAAuB,CAAC,CAAA;QAEjE,MAAM,WAAW,GAAG,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;QACrE,MAAM,cAAc,GAAG,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB,CAAA;QACvE,MAAM,cAAc,GAAG,cAAc;YACjC,CAAC,CAAC,cAAc;YAChB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,KAAK,EAAE,WAAW,CAAC,CAAA;QACvE,OAAO,IAAI,KAAK,CACZ,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,cAAc,GAAG,CAC1C,CAAA;IACL,CAAC;IAED;;OAEG;IACO,yBAAyB,CAC/B,KAAY,EACZ,gBAA0C;QAE1C,MAAM,WAAW,GAAG,gBAAgB,CAAC,WAAW;aAC3C,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,MAAM,GAAG,GAAG,CAAC;aACnC,IAAI,CAAC,IAAI,CAAC,CAAA;QACf,OAAO,IAAI,KAAK,CACZ,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,oBACjC,gBAAgB,CAAC,IACrB,aAAa,WAAW,GAAG,CAC9B,CAAA;IACL,CAAC;IAED;;OAEG;IACO,uBAAuB,CAC7B,KAAY,EACZ,YAAkC;QAElC,MAAM,UAAU,GAAG,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAC1D,CAAC,CAAC,YAAY,CAAC,IAAI;YACnB,CAAC,CAAC,YAAY,CAAA;QAClB,OAAO,IAAI,KAAK,CACZ,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,UAAU,GAAG,CACtC,CAAA;IACL,CAAC;IAED;;OAEG;IACO,wBAAwB,CAC9B,KAAY,EACZ,eAA2B;QAE3B,OAAO,IAAI,KAAK,CACZ,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,oBACjC,eAAe,CAAC,IACpB,YAAY,eAAe,CAAC,UAAU,GAAG,CAC5C,CAAA;IACL,CAAC;IAED;;OAEG;IACO,sBAAsB,CAC5B,KAAY,EACZ,WAAgC;QAEhC,MAAM,SAAS,GAAG,eAAe,CAAC,YAAY,CAAC,WAAW,CAAC;YACvD,CAAC,CAAC,WAAW,CAAC,IAAI;YAClB,CAAC,CAAC,WAAW,CAAA;QACjB,OAAO,IAAI,KAAK,CACZ,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,SAAS,GAAG,CACrC,CAAA;IACL,CAAC;IAED;;OAEG;IACO,mBAAmB,CACzB,KAAY,EACZ,UAA2B;QAE3B,MAAM,WAAW,GAAG,UAAU,CAAC,WAAW;aACrC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,MAAM,GAAG,GAAG,CAAC;aACnC,IAAI,CAAC,IAAI,CAAC,CAAA;QACf,MAAM,qBAAqB,GAAG,UAAU,CAAC,qBAAqB;aACzD,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,MAAM,GAAG,GAAG,CAAC;aACnC,IAAI,CAAC,GAAG,CAAC,CAAA;QACd,IAAI,GAAG,GACH,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,oBACjC,UAAU,CAAC,IACf,kBAAkB,WAAW,IAAI;YACjC,cAAc,IAAI,CAAC,UAAU,CACzB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAChC,IAAI,qBAAqB,GAAG,CAAA;QACjC,IAAI,UAAU,CAAC,QAAQ;YAAE,GAAG,IAAI,cAAc,UAAU,CAAC,QAAQ,EAAE,CAAA;QACnE,IAAI,UAAU,CAAC,QAAQ;YAAE,GAAG,IAAI,cAAc,UAAU,CAAC,QAAQ,EAAE,CAAA;QAEnE,OAAO,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;IACzB,CAAC;IAED;;OAEG;IACO,iBAAiB,CACvB,KAAY,EACZ,gBAA0C;QAE1C,MAAM,cAAc,GAAG,eAAe,CAAC,iBAAiB,CACpD,gBAAgB,CACnB;YACG,CAAC,CAAC,gBAAgB,CAAC,IAAI;YACvB,CAAC,CAAC,gBAAgB,CAAA;QACtB,OAAO,IAAI,KAAK,CACZ,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,cAAc,GAAG,CAC1C,CAAA;IACL,CAAC;IAED;;OAEG;IACO,iBAAiB,CACvB,KAAY,EACZ,YAAkC;QAElC,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;QAEvD,MAAM,UAAU,GAAG,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAC1D,CAAC,CAAC,YAAY,CAAC,IAAI;YACnB,CAAC,CAAC,YAAY,CAAA;QAElB,OAAO,GAAG,SAAS,IAAI,UAAU,MAAM,CAAA;IAC3C,CAAC;IAES,iBAAiB,CACvB,KAAY,EACZ,YAAkC;QAElC,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;QAEpD,OAAO,MAAM;YACT,CAAC,CAAC,GAAG,MAAM,IAAI,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,YAAY,CAAC,EAAE;YAC5D,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,YAAY,CAAC,CAAA;IACrD,CAAC;IAED;;OAEG;IACO,aAAa,CACnB,KAAY,EACZ,MAAmB,EACnB,aAAsB,IAAI,EAC1B,aAAuB,EACvB,KAAe;QAEf,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;QAC/D,IAAI,QAAQ,GAAG,MAAM,CAAC,QAAQ;YAC1B,CAAC,CAAC,MAAM,CAAC,QAAQ;YACjB,CAAC,CAAC,GAAG,SAAS,IAAI,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,OAAO,CAAA;QACtD,IAAI,MAAM,IAAI,UAAU;YAAE,QAAQ,GAAG,GAAG,MAAM,IAAI,QAAQ,EAAE,CAAA;QAC5D,IAAI,KAAK;YAAE,QAAQ,GAAG,QAAQ,GAAG,MAAM,CAAA;QACvC,OAAO,QAAQ;aACV,KAAK,CAAC,GAAG,CAAC;aACV,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;YACP,OAAO,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAA;QACvC,CAAC,CAAC;aACD,IAAI,CAAC,GAAG,CAAC,CAAA;IAClB,CAAC;IAES,KAAK,CAAC,sBAAsB,CAAC,KAAY,EAAE,MAAmB;QACpE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;QAEnE,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;QAC1C,CAAC;QAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAC3B,kCAAkC;YAC9B,+DAA+D,MAAM,yBAAyB,IAAI,wBAAwB,MAAM,CAAC,IAAI,GAAG,CAC/I,CAAA;QAED,4DAA4D;QAC5D,sGAAsG;QACtG,2GAA2G;QAC3G,OAAO;QACP,mEAAmE;QACnE,IAAI,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAA;QACnC,IAAI,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;YAC7B,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,OAAO,CAAC,MAAM,CAAC,CAAA;QAC/C,CAAC;QACD,OAAO;YACH,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;YAC/B,IAAI,EAAE,OAAO;SAChB,CAAA;IACL,CAAC;IAED;;OAEG;IACO,aAAa,CAAC,OAAgB;QACpC,IAAI,OAAO,KAAK,SAAS,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChD,OAAO,MAAM,CAAA;QACjB,CAAC;QAED,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAA,CAAC,wCAAwC;QAErG,OAAO,IAAI,OAAO,GAAG,CAAA;IACzB,CAAC;IAED;;OAEG;IACO,UAAU,CAAC,MAA6B;QAC9C,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;QAEhE,IAAI,MAAM,IAAI,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;YAChD,OAAO,IAAI,MAAM,MAAM,SAAS,GAAG,CAAA;QACvC,CAAC;QAED,OAAO,IAAI,SAAS,GAAG,CAAA;IAC3B,CAAC;IAED;;OAEG;IACO,oBAAoB,CAAC,KAAY,EAAE,MAAmB;QAC5D,IAAI,CAAC,GAAG,GAAG,GAAG,MAAM,CAAC,IAAI,GAAG,GAAG,CAAA;QAE/B,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC;YACrB,IAAI,MAAM,CAAC,kBAAkB,KAAK,WAAW,EAAE,CAAC;gBAC5C,CAAC,IAAI,yBAAyB,IAAI,CAAC,UAAU,CACzC,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,MAAM,CAAC,CACxC,IAAI,CAAA;YACT,CAAC;iBAAM,IAAI,MAAM,CAAC,kBAAkB,KAAK,OAAO,EAAE,CAAC;gBAC/C,CAAC,IAAI,6BAA6B,CAAA;YACtC,CAAC;iBAAM,IAAI,MAAM,CAAC,kBAAkB,KAAK,MAAM,EAAE,CAAC;gBAC9C,CAAC,IAAI,iCAAiC,CAAA;YAC1C,CAAC;QACL,CAAC;QAED,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;YAC1D,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;YAC5C,IAAI,MAAM,CAAC,OAAO;gBAAE,CAAC,IAAI,QAAQ,CAAA;QACrC,CAAC;aAAM,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;YAC7B,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;QAC5D,CAAC;QAED,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;YACtB,CAAC,IAAI,QAAQ,MAAM,CAAC,YAAY,KAC5B,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,SAClD,EAAE,CAAA;QACN,CAAC;aAAM,CAAC;YACJ,IAAI,MAAM,CAAC,OAAO;gBAAE,CAAC,IAAI,kBAAkB,GAAG,MAAM,CAAC,OAAO,GAAG,GAAG,CAAA;YAClE,IAAI,MAAM,CAAC,SAAS;gBAAE,CAAC,IAAI,YAAY,GAAG,MAAM,CAAC,SAAS,GAAG,GAAG,CAAA;QACpE,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,UAAU;YAAE,CAAC,IAAI,WAAW,CAAA;QACxC,IACI,CAAC,MAAM,CAAC,WAAW;YACnB,MAAM,CAAC,OAAO,KAAK,SAAS;YAC5B,MAAM,CAAC,OAAO,KAAK,IAAI;YAEvB,CAAC,IAAI,WAAW,GAAG,MAAM,CAAC,OAAO,CAAA;QAErC,OAAO,CAAC,CAAA;IACZ,CAAC;IACD;;OAEG;IACH,kBAAkB,CACd,WAA2B,EAC3B,OAAgB;QAEhB,MAAM,IAAI,YAAY,CAClB,2DAA2D,CAC9D,CAAA;IACL,CAAC;CACJ", "file": "CockroachQueryRunner.js", "sourcesContent": ["import { ObjectLiteral } from \"../../common/ObjectLiteral\"\nimport { TypeORMError } from \"../../error\"\nimport { QueryFailedError } from \"../../error/QueryFailedError\"\nimport { QueryRunnerAlreadyReleasedError } from \"../../error/QueryRunnerAlreadyReleasedError\"\nimport { TransactionNotStartedError } from \"../../error/TransactionNotStartedError\"\nimport { ReadStream } from \"../../platform/PlatformTools\"\nimport { BaseQueryRunner } from \"../../query-runner/BaseQueryRunner\"\nimport { QueryResult } from \"../../query-runner/QueryResult\"\nimport { QueryRunner } from \"../../query-runner/QueryRunner\"\nimport { TableIndexOptions } from \"../../schema-builder/options/TableIndexOptions\"\nimport { Table } from \"../../schema-builder/table/Table\"\nimport { TableCheck } from \"../../schema-builder/table/TableCheck\"\nimport { TableColumn } from \"../../schema-builder/table/TableColumn\"\nimport { TableExclusion } from \"../../schema-builder/table/TableExclusion\"\nimport { TableForeignKey } from \"../../schema-builder/table/TableForeignKey\"\nimport { TableIndex } from \"../../schema-builder/table/TableIndex\"\nimport { TableUnique } from \"../../schema-builder/table/TableUnique\"\nimport { View } from \"../../schema-builder/view/View\"\nimport { Broadcaster } from \"../../subscriber/Broadcaster\"\nimport { BroadcasterResult } from \"../../subscriber/BroadcasterResult\"\nimport { InstanceChecker } from \"../../util/InstanceChecker\"\nimport { OrmUtils } from \"../../util/OrmUtils\"\nimport { VersionUtils } from \"../../util/VersionUtils\"\nimport { Query } from \"../Query\"\nimport { ColumnType } from \"../types/ColumnTypes\"\nimport { IsolationLevel } from \"../types/IsolationLevel\"\nimport { MetadataTableType } from \"../types/MetadataTableType\"\nimport { ReplicationMode } from \"../types/ReplicationMode\"\nimport { CockroachDriver } from \"./CockroachDriver\"\n\n/**\n * Runs queries on a single postgres database connection.\n */\nexport class CockroachQueryRunner\n    extends BaseQueryRunner\n    implements QueryRunner\n{\n    // -------------------------------------------------------------------------\n    // Public Implemented Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Database driver used by connection.\n     */\n    driver: CockroachDriver\n\n    // -------------------------------------------------------------------------\n    // Protected Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Promise used to obtain a database connection for a first time.\n     */\n    protected databaseConnectionPromise: Promise<any>\n\n    /**\n     * Special callback provided by a driver used to release a created connection.\n     */\n    protected releaseCallback?: (err: any) => void\n\n    /**\n     * Stores all executed queries to be able to run them again if transaction fails.\n     */\n    protected queries: { query: string; parameters?: any[] }[] = []\n\n    /**\n     * Indicates if running queries must be stored\n     */\n    protected storeQueries: boolean = false\n\n    /**\n     * Current number of transaction retries in case of 40001 error.\n     */\n    protected transactionRetries: number = 0\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(driver: CockroachDriver, mode: ReplicationMode) {\n        super()\n        this.driver = driver\n        this.connection = driver.connection\n        this.mode = mode\n        this.broadcaster = new Broadcaster(this)\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates/uses database connection from the connection pool to perform further operations.\n     * Returns obtained database connection.\n     */\n    connect(): Promise<any> {\n        if (this.databaseConnection)\n            return Promise.resolve(this.databaseConnection)\n\n        if (this.databaseConnectionPromise)\n            return this.databaseConnectionPromise\n\n        if (this.mode === \"slave\" && this.driver.isReplicated) {\n            this.databaseConnectionPromise = this.driver\n                .obtainSlaveConnection()\n                .then(([connection, release]: any[]) => {\n                    this.driver.connectedQueryRunners.push(this)\n                    this.databaseConnection = connection\n\n                    const onErrorCallback = (err: Error) =>\n                        this.releaseConnection(err)\n                    this.releaseCallback = (err?: Error) => {\n                        this.databaseConnection.removeListener(\n                            \"error\",\n                            onErrorCallback,\n                        )\n                        release(err)\n                    }\n                    this.databaseConnection.on(\"error\", onErrorCallback)\n\n                    return this.databaseConnection\n                })\n        } else {\n            // master\n            this.databaseConnectionPromise = this.driver\n                .obtainMasterConnection()\n                .then(([connection, release]: any[]) => {\n                    this.driver.connectedQueryRunners.push(this)\n                    this.databaseConnection = connection\n\n                    const onErrorCallback = (err: Error) =>\n                        this.releaseConnection(err)\n                    this.releaseCallback = (err?: Error) => {\n                        this.databaseConnection.removeListener(\n                            \"error\",\n                            onErrorCallback,\n                        )\n                        release(err)\n                    }\n                    this.databaseConnection.on(\"error\", onErrorCallback)\n\n                    return this.databaseConnection\n                })\n        }\n\n        return this.databaseConnectionPromise\n    }\n\n    /**\n     * Release a connection back to the pool, optionally specifying an Error to release with.\n     * Per pg-pool documentation this will prevent the pool from re-using the broken connection.\n     */\n    private async releaseConnection(err?: Error) {\n        if (this.isReleased) {\n            return\n        }\n\n        this.isReleased = true\n        if (this.releaseCallback) {\n            this.releaseCallback(err)\n            this.releaseCallback = undefined\n        }\n\n        const index = this.driver.connectedQueryRunners.indexOf(this)\n\n        if (index !== -1) {\n            this.driver.connectedQueryRunners.splice(index, 1)\n        }\n    }\n\n    /**\n     * Releases used database connection.\n     * You cannot use query runner methods once its released.\n     */\n    release(): Promise<void> {\n        return this.releaseConnection()\n    }\n\n    /**\n     * Starts transaction.\n     */\n    async startTransaction(isolationLevel?: IsolationLevel): Promise<void> {\n        this.isTransactionActive = true\n        this.transactionRetries = 0\n        try {\n            await this.broadcaster.broadcast(\"BeforeTransactionStart\")\n        } catch (err) {\n            this.isTransactionActive = false\n            throw err\n        }\n\n        if (this.transactionDepth === 0) {\n            await this.query(\"START TRANSACTION\")\n            await this.query(\"SAVEPOINT cockroach_restart\")\n            if (isolationLevel) {\n                await this.query(\n                    \"SET TRANSACTION ISOLATION LEVEL \" + isolationLevel,\n                )\n            }\n        } else {\n            await this.query(`SAVEPOINT typeorm_${this.transactionDepth}`)\n        }\n\n        this.transactionDepth += 1\n        this.storeQueries = true\n\n        await this.broadcaster.broadcast(\"AfterTransactionStart\")\n    }\n\n    /**\n     * Commits transaction.\n     * Error will be thrown if transaction was not started.\n     */\n    async commitTransaction(): Promise<void> {\n        if (!this.isTransactionActive) throw new TransactionNotStartedError()\n\n        await this.broadcaster.broadcast(\"BeforeTransactionCommit\")\n\n        if (this.transactionDepth > 1) {\n            await this.query(\n                `RELEASE SAVEPOINT typeorm_${this.transactionDepth - 1}`,\n            )\n            this.transactionDepth -= 1\n        } else {\n            this.storeQueries = false\n            await this.query(\"RELEASE SAVEPOINT cockroach_restart\")\n            await this.query(\"COMMIT\")\n            this.queries = []\n            this.isTransactionActive = false\n            this.transactionRetries = 0\n            this.transactionDepth -= 1\n        }\n\n        await this.broadcaster.broadcast(\"AfterTransactionCommit\")\n    }\n\n    /**\n     * Rollbacks transaction.\n     * Error will be thrown if transaction was not started.\n     */\n    async rollbackTransaction(): Promise<void> {\n        if (!this.isTransactionActive) throw new TransactionNotStartedError()\n\n        await this.broadcaster.broadcast(\"BeforeTransactionRollback\")\n\n        if (this.transactionDepth > 1) {\n            await this.query(\n                `ROLLBACK TO SAVEPOINT typeorm_${this.transactionDepth - 1}`,\n            )\n        } else {\n            this.storeQueries = false\n            await this.query(\"ROLLBACK\")\n            this.queries = []\n            this.isTransactionActive = false\n            this.transactionRetries = 0\n        }\n        this.transactionDepth -= 1\n\n        await this.broadcaster.broadcast(\"AfterTransactionRollback\")\n    }\n\n    /**\n     * Executes a given SQL query.\n     */\n    async query(\n        query: string,\n        parameters?: any[],\n        useStructuredResult = false,\n    ): Promise<any> {\n        if (this.isReleased) throw new QueryRunnerAlreadyReleasedError()\n\n        const databaseConnection = await this.connect()\n\n        this.driver.connection.logger.logQuery(query, parameters, this)\n        await this.broadcaster.broadcast(\"BeforeQuery\", query, parameters)\n\n        const broadcasterResult = new BroadcasterResult()\n        const queryStartTime = Date.now()\n\n        if (this.isTransactionActive && this.storeQueries) {\n            this.queries.push({ query, parameters })\n        }\n\n        try {\n            const raw = await new Promise<any>((ok, fail) => {\n                databaseConnection.query(\n                    query,\n                    parameters,\n                    (err: any, raw: any) => (err ? fail(err) : ok(raw)),\n                )\n            })\n\n            // log slow queries if maxQueryExecution time is set\n            const maxQueryExecutionTime =\n                this.driver.options.maxQueryExecutionTime\n            const queryEndTime = Date.now()\n            const queryExecutionTime = queryEndTime - queryStartTime\n            if (\n                maxQueryExecutionTime &&\n                queryExecutionTime > maxQueryExecutionTime\n            ) {\n                this.driver.connection.logger.logQuerySlow(\n                    queryExecutionTime,\n                    query,\n                    parameters,\n                    this,\n                )\n            }\n\n            const result = new QueryResult()\n\n            if (raw.hasOwnProperty(\"rowCount\")) {\n                result.affected = raw.rowCount\n            }\n\n            if (raw.hasOwnProperty(\"rows\")) {\n                result.records = raw.rows\n            }\n\n            switch (raw.command) {\n                case \"DELETE\":\n                    // for DELETE query additionally return number of affected rows\n                    result.raw = [raw.rows, raw.rowCount]\n                    break\n                default:\n                    result.raw = raw.rows\n            }\n\n            this.broadcaster.broadcastAfterQueryEvent(\n                broadcasterResult,\n                query,\n                parameters,\n                true,\n                queryExecutionTime,\n                raw,\n                undefined,\n            )\n\n            if (useStructuredResult) {\n                return result\n            } else {\n                return result.raw\n            }\n        } catch (err) {\n            if (\n                err.code === \"40001\" &&\n                this.isTransactionActive &&\n                this.transactionRetries <\n                    (this.driver.options.maxTransactionRetries || 5)\n            ) {\n                this.transactionRetries += 1\n                this.storeQueries = false\n                await this.query(\"ROLLBACK TO SAVEPOINT cockroach_restart\")\n                const sleepTime =\n                    2 ** this.transactionRetries *\n                    0.1 *\n                    (Math.random() + 0.5) *\n                    1000\n                await new Promise((resolve) => setTimeout(resolve, sleepTime))\n\n                let result = undefined\n                for (const q of this.queries) {\n                    this.driver.connection.logger.logQuery(\n                        `Retrying transaction for query \"${q.query}\"`,\n                        q.parameters,\n                        this,\n                    )\n                    result = await this.query(q.query, q.parameters)\n                }\n                this.transactionRetries = 0\n                this.storeQueries = true\n\n                return result\n            } else {\n                this.driver.connection.logger.logQueryError(\n                    err,\n                    query,\n                    parameters,\n                    this,\n                )\n                this.broadcaster.broadcastAfterQueryEvent(\n                    broadcasterResult,\n                    query,\n                    parameters,\n                    false,\n                    undefined,\n                    undefined,\n                    err,\n                )\n                throw new QueryFailedError(query, parameters, err)\n            }\n        } finally {\n            await broadcasterResult.wait()\n        }\n    }\n\n    /**\n     * Returns raw data stream.\n     */\n    async stream(\n        query: string,\n        parameters?: any[],\n        onEnd?: Function,\n        onError?: Function,\n    ): Promise<ReadStream> {\n        const QueryStream = this.driver.loadStreamDependency()\n        if (this.isReleased) {\n            throw new QueryRunnerAlreadyReleasedError()\n        }\n\n        const databaseConnection = await this.connect()\n        this.driver.connection.logger.logQuery(query, parameters, this)\n        const stream = databaseConnection.query(\n            new QueryStream(query, parameters),\n        )\n\n        if (onEnd) {\n            stream.on(\"end\", onEnd)\n        }\n\n        if (onError) {\n            stream.on(\"error\", onError)\n        }\n\n        return stream\n    }\n\n    /**\n     * Returns all available database names including system databases.\n     */\n    async getDatabases(): Promise<string[]> {\n        return Promise.resolve([])\n    }\n\n    /**\n     * Returns all available schema names including system schemas.\n     * If database parameter specified, returns schemas of that database.\n     */\n    async getSchemas(database?: string): Promise<string[]> {\n        return Promise.resolve([])\n    }\n\n    /**\n     * Checks if database with the given name exist.\n     */\n    async hasDatabase(database: string): Promise<boolean> {\n        const result = await this.query(\n            `SELECT * FROM \"pg_database\" WHERE \"datname\" = '${database}'`,\n        )\n        return result.length ? true : false\n    }\n\n    /**\n     * Loads currently using database\n     */\n    async getCurrentDatabase(): Promise<string> {\n        const query = await this.query(`SELECT * FROM current_database()`)\n        return query[0][\"current_database\"]\n    }\n\n    /**\n     * Checks if schema with the given name exist.\n     */\n    async hasSchema(schema: string): Promise<boolean> {\n        const result = await this.query(\n            `SELECT * FROM \"information_schema\".\"schemata\" WHERE \"schema_name\" = '${schema}'`,\n        )\n        return result.length ? true : false\n    }\n\n    /**\n     * Loads currently using database schema\n     */\n    async getCurrentSchema(): Promise<string> {\n        const query = await this.query(`SELECT * FROM current_schema()`)\n        return query[0][\"current_schema\"]\n    }\n\n    /**\n     * Checks if table with the given name exist in the database.\n     */\n    async hasTable(tableOrName: Table | string): Promise<boolean> {\n        const parsedTableName = this.driver.parseTableName(tableOrName)\n\n        if (!parsedTableName.schema) {\n            parsedTableName.schema = await this.getCurrentSchema()\n        }\n\n        const sql = `SELECT * FROM \"information_schema\".\"tables\" WHERE \"table_schema\" = '${parsedTableName.schema}' AND \"table_name\" = '${parsedTableName.tableName}'`\n        const result = await this.query(sql)\n        return result.length ? true : false\n    }\n\n    /**\n     * Checks if column with the given name exist in the given table.\n     */\n    async hasColumn(\n        tableOrName: Table | string,\n        columnName: string,\n    ): Promise<boolean> {\n        const parsedTableName = this.driver.parseTableName(tableOrName)\n\n        if (!parsedTableName.schema) {\n            parsedTableName.schema = await this.getCurrentSchema()\n        }\n\n        const sql = `SELECT * FROM \"information_schema\".\"columns\" WHERE \"table_schema\" = '${parsedTableName.schema}' AND \"table_name\" = '${parsedTableName.tableName}' AND \"column_name\" = '${columnName}'`\n        const result = await this.query(sql)\n        return result.length ? true : false\n    }\n\n    /**\n     * Creates a new database.\n     */\n    async createDatabase(\n        database: string,\n        ifNotExist?: boolean,\n    ): Promise<void> {\n        const up = `CREATE DATABASE ${\n            ifNotExist ? \"IF NOT EXISTS \" : \"\"\n        } \"${database}\"`\n        const down = `DROP DATABASE \"${database}\"`\n        await this.executeQueries(new Query(up), new Query(down))\n    }\n\n    /**\n     * Drops database.\n     */\n    async dropDatabase(database: string, ifExist?: boolean): Promise<void> {\n        const up = `DROP DATABASE ${ifExist ? \"IF EXISTS \" : \"\"} \"${database}\"`\n        const down = `CREATE DATABASE \"${database}\"`\n        await this.executeQueries(new Query(up), new Query(down))\n    }\n\n    /**\n     * Creates a new table schema.\n     */\n    async createSchema(\n        schemaPath: string,\n        ifNotExist?: boolean,\n    ): Promise<void> {\n        const schema =\n            schemaPath.indexOf(\".\") === -1\n                ? schemaPath\n                : schemaPath.split(\".\")[1]\n\n        const up = ifNotExist\n            ? `CREATE SCHEMA IF NOT EXISTS \"${schema}\"`\n            : `CREATE SCHEMA \"${schema}\"`\n        const down = `DROP SCHEMA \"${schema}\" CASCADE`\n        await this.executeQueries(new Query(up), new Query(down))\n    }\n\n    /**\n     * Drops table schema.\n     */\n    async dropSchema(\n        schemaPath: string,\n        ifExist?: boolean,\n        isCascade?: boolean,\n    ): Promise<void> {\n        const schema =\n            schemaPath.indexOf(\".\") === -1\n                ? schemaPath\n                : schemaPath.split(\".\")[1]\n\n        const up = ifExist\n            ? `DROP SCHEMA IF EXISTS \"${schema}\" ${isCascade ? \"CASCADE\" : \"\"}`\n            : `DROP SCHEMA \"${schema}\" ${isCascade ? \"CASCADE\" : \"\"}`\n        const down = `CREATE SCHEMA \"${schema}\"`\n        await this.executeQueries(new Query(up), new Query(down))\n    }\n\n    /**\n     * Creates a new table.\n     */\n    async createTable(\n        table: Table,\n        ifNotExist: boolean = false,\n        createForeignKeys: boolean = true,\n        createIndices: boolean = true,\n    ): Promise<void> {\n        if (ifNotExist) {\n            const isTableExist = await this.hasTable(table)\n            if (isTableExist) return Promise.resolve()\n        }\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n\n        // if table have column with ENUM type, we must create this type in postgres.\n        const enumColumns = table.columns.filter(\n            (column) => column.type === \"enum\" || column.type === \"simple-enum\",\n        )\n        const createdEnumTypes: string[] = []\n        for (const column of enumColumns) {\n            // TODO: Should also check if values of existing type matches expected ones\n            const hasEnum = await this.hasEnumType(table, column)\n            const enumName = this.buildEnumName(table, column)\n\n            // if enum with the same \"enumName\" is defined more then once, me must prevent double creation\n            if (!hasEnum && createdEnumTypes.indexOf(enumName) === -1) {\n                createdEnumTypes.push(enumName)\n                upQueries.push(this.createEnumTypeSql(table, column, enumName))\n                downQueries.push(this.dropEnumTypeSql(table, column, enumName))\n            }\n        }\n\n        table.columns\n            .filter(\n                (column) =>\n                    column.isGenerated &&\n                    column.generationStrategy === \"increment\",\n            )\n            .forEach((column) => {\n                upQueries.push(\n                    new Query(\n                        `CREATE SEQUENCE ${this.escapePath(\n                            this.buildSequencePath(table, column),\n                        )}`,\n                    ),\n                )\n                downQueries.push(\n                    new Query(\n                        `DROP SEQUENCE ${this.escapePath(\n                            this.buildSequencePath(table, column),\n                        )}`,\n                    ),\n                )\n            })\n\n        upQueries.push(this.createTableSql(table, createForeignKeys))\n        downQueries.push(this.dropTableSql(table))\n\n        // if createForeignKeys is true, we must drop created foreign keys in down query.\n        // createTable does not need separate method to create foreign keys, because it create fk's in the same query with table creation.\n        if (createForeignKeys)\n            table.foreignKeys.forEach((foreignKey) =>\n                downQueries.push(this.dropForeignKeySql(table, foreignKey)),\n            )\n\n        if (createIndices) {\n            table.indices\n                .filter((index) => !index.isUnique)\n                .forEach((index) => {\n                    // new index may be passed without name. In this case we generate index name manually.\n                    if (!index.name)\n                        index.name = this.connection.namingStrategy.indexName(\n                            table,\n                            index.columnNames,\n                            index.where,\n                        )\n                    upQueries.push(this.createIndexSql(table, index))\n                    downQueries.push(this.dropIndexSql(table, index))\n                })\n        }\n\n        // if table have column with generated type, we must add the expression to the metadata table\n        const generatedColumns = table.columns.filter(\n            (column) => column.generatedType && column.asExpression,\n        )\n\n        for (const column of generatedColumns) {\n            const currentSchema = await this.getCurrentSchema()\n            let { schema } = this.driver.parseTableName(table)\n            if (!schema) {\n                schema = currentSchema\n            }\n\n            const insertQuery = this.insertTypeormMetadataSql({\n                schema: schema,\n                table: table.name,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n                value: column.asExpression,\n            })\n\n            const deleteQuery = this.deleteTypeormMetadataSql({\n                schema: schema,\n                table: table.name,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n            })\n\n            upQueries.push(insertQuery)\n            downQueries.push(deleteQuery)\n        }\n\n        await this.executeQueries(upQueries, downQueries)\n    }\n\n    /**\n     * Drops the table.\n     */\n    async dropTable(\n        target: Table | string,\n        ifExist?: boolean,\n        dropForeignKeys: boolean = true,\n        dropIndices: boolean = true,\n    ): Promise<void> {\n        // It needs because if table does not exist and dropForeignKeys or dropIndices is true, we don't need\n        // to perform drop queries for foreign keys and indices.\n        if (ifExist) {\n            const isTableExist = await this.hasTable(target)\n            if (!isTableExist) return Promise.resolve()\n        }\n\n        // if dropTable called with dropForeignKeys = true, we must create foreign keys in down query.\n        const createForeignKeys: boolean = dropForeignKeys\n        const tablePath = this.getTablePath(target)\n        const table = await this.getCachedTable(tablePath)\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n\n        // foreign keys must be dropped before indices, because fk's rely on indices\n        if (dropForeignKeys)\n            table.foreignKeys.forEach((foreignKey) =>\n                upQueries.push(this.dropForeignKeySql(table, foreignKey)),\n            )\n\n        if (dropIndices) {\n            table.indices.forEach((index) => {\n                upQueries.push(this.dropIndexSql(table, index))\n                downQueries.push(this.createIndexSql(table, index))\n            })\n        }\n\n        upQueries.push(this.dropTableSql(table))\n        downQueries.push(this.createTableSql(table, createForeignKeys))\n\n        table.columns\n            .filter(\n                (column) =>\n                    column.isGenerated &&\n                    column.generationStrategy === \"increment\",\n            )\n            .forEach((column) => {\n                upQueries.push(\n                    new Query(\n                        `DROP SEQUENCE ${this.escapePath(\n                            this.buildSequencePath(table, column),\n                        )}`,\n                    ),\n                )\n                downQueries.push(\n                    new Query(\n                        `CREATE SEQUENCE ${this.escapePath(\n                            this.buildSequencePath(table, column),\n                        )}`,\n                    ),\n                )\n            })\n\n        // if table had columns with generated type, we must remove the expression from the metadata table\n        const generatedColumns = table.columns.filter(\n            (column) => column.generatedType && column.asExpression,\n        )\n\n        for (const column of generatedColumns) {\n            const currentSchema = await this.getCurrentSchema()\n            let { schema } = this.driver.parseTableName(table)\n            if (!schema) {\n                schema = currentSchema\n            }\n\n            const deleteQuery = this.deleteTypeormMetadataSql({\n                schema: schema,\n                table: table.name,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n            })\n\n            const insertQuery = this.insertTypeormMetadataSql({\n                schema: schema,\n                table: table.name,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n                value: column.asExpression,\n            })\n\n            upQueries.push(deleteQuery)\n            downQueries.push(insertQuery)\n        }\n\n        await this.executeQueries(upQueries, downQueries)\n    }\n\n    /**\n     * Creates a new view.\n     */\n    async createView(\n        view: View,\n        syncWithMetadata: boolean = false,\n    ): Promise<void> {\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n        upQueries.push(this.createViewSql(view))\n        if (syncWithMetadata)\n            upQueries.push(await this.insertViewDefinitionSql(view))\n        downQueries.push(this.dropViewSql(view))\n        if (syncWithMetadata)\n            downQueries.push(await this.deleteViewDefinitionSql(view))\n        await this.executeQueries(upQueries, downQueries)\n    }\n\n    /**\n     * Drops the view.\n     */\n    async dropView(target: View | string): Promise<void> {\n        const viewName = InstanceChecker.isView(target) ? target.name : target\n        const view = await this.getCachedView(viewName)\n\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n        upQueries.push(await this.deleteViewDefinitionSql(view))\n        upQueries.push(this.dropViewSql(view))\n        downQueries.push(await this.insertViewDefinitionSql(view))\n        downQueries.push(this.createViewSql(view))\n        await this.executeQueries(upQueries, downQueries)\n    }\n\n    /**\n     * Renames the given table.\n     */\n    async renameTable(\n        oldTableOrName: Table | string,\n        newTableName: string,\n    ): Promise<void> {\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n        const oldTable = InstanceChecker.isTable(oldTableOrName)\n            ? oldTableOrName\n            : await this.getCachedTable(oldTableOrName)\n        const newTable = oldTable.clone()\n\n        const { schema: schemaName, tableName: oldTableName } =\n            this.driver.parseTableName(oldTable)\n\n        newTable.name = schemaName\n            ? `${schemaName}.${newTableName}`\n            : newTableName\n\n        upQueries.push(\n            new Query(\n                `ALTER TABLE ${this.escapePath(\n                    oldTable,\n                )} RENAME TO \"${newTableName}\"`,\n            ),\n        )\n        downQueries.push(\n            new Query(\n                `ALTER TABLE ${this.escapePath(\n                    newTable,\n                )} RENAME TO \"${oldTableName}\"`,\n            ),\n        )\n\n        // rename column primary key constraint\n        if (\n            newTable.primaryColumns.length > 0 &&\n            !newTable.primaryColumns[0].primaryKeyConstraintName\n        ) {\n            const columnNames = newTable.primaryColumns.map(\n                (column) => column.name,\n            )\n\n            const oldPkName = this.connection.namingStrategy.primaryKeyName(\n                oldTable,\n                columnNames,\n            )\n            const newPkName = this.connection.namingStrategy.primaryKeyName(\n                newTable,\n                columnNames,\n            )\n\n            upQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        newTable,\n                    )} RENAME CONSTRAINT \"${oldPkName}\" TO \"${newPkName}\"`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        newTable,\n                    )} RENAME CONSTRAINT \"${newPkName}\" TO \"${oldPkName}\"`,\n                ),\n            )\n        }\n\n        // rename unique constraints\n        newTable.uniques.forEach((unique) => {\n            const oldUniqueName =\n                this.connection.namingStrategy.uniqueConstraintName(\n                    oldTable,\n                    unique.columnNames,\n                )\n\n            // Skip renaming if Unique has user defined constraint name\n            if (unique.name !== oldUniqueName) return\n\n            // build new constraint name\n            const newUniqueName =\n                this.connection.namingStrategy.uniqueConstraintName(\n                    newTable,\n                    unique.columnNames,\n                )\n\n            // build queries\n            upQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        newTable,\n                    )} RENAME CONSTRAINT \"${\n                        unique.name\n                    }\" TO \"${newUniqueName}\"`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        newTable,\n                    )} RENAME CONSTRAINT \"${newUniqueName}\" TO \"${\n                        unique.name\n                    }\"`,\n                ),\n            )\n\n            // replace constraint name\n            unique.name = newUniqueName\n        })\n\n        // rename index constraints\n        newTable.indices.forEach((index) => {\n            const oldIndexName = this.connection.namingStrategy.indexName(\n                oldTable,\n                index.columnNames,\n                index.where,\n            )\n\n            // Skip renaming if Index has user defined constraint name\n            if (index.name !== oldIndexName) return\n\n            // build new constraint name\n            const { schema } = this.driver.parseTableName(newTable)\n            const newIndexName = this.connection.namingStrategy.indexName(\n                newTable,\n                index.columnNames,\n                index.where,\n            )\n\n            // build queries\n            const up = schema\n                ? `ALTER INDEX \"${schema}\".\"${index.name}\" RENAME TO \"${newIndexName}\"`\n                : `ALTER INDEX \"${index.name}\" RENAME TO \"${newIndexName}\"`\n            const down = schema\n                ? `ALTER INDEX \"${schema}\".\"${newIndexName}\" RENAME TO \"${index.name}\"`\n                : `ALTER INDEX \"${newIndexName}\" RENAME TO \"${index.name}\"`\n            upQueries.push(new Query(up))\n            downQueries.push(new Query(down))\n\n            // replace constraint name\n            index.name = newIndexName\n        })\n\n        // rename foreign key constraints\n        newTable.foreignKeys.forEach((foreignKey) => {\n            const oldForeignKeyName =\n                this.connection.namingStrategy.foreignKeyName(\n                    oldTable,\n                    foreignKey.columnNames,\n                    this.getTablePath(foreignKey),\n                    foreignKey.referencedColumnNames,\n                )\n\n            // Skip renaming if foreign key has user defined constraint name\n            if (foreignKey.name !== oldForeignKeyName) return\n\n            // build new constraint name\n            const newForeignKeyName =\n                this.connection.namingStrategy.foreignKeyName(\n                    newTable,\n                    foreignKey.columnNames,\n                    this.getTablePath(foreignKey),\n                    foreignKey.referencedColumnNames,\n                )\n\n            // build queries\n            upQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        newTable,\n                    )} RENAME CONSTRAINT \"${\n                        foreignKey.name\n                    }\" TO \"${newForeignKeyName}\"`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        newTable,\n                    )} RENAME CONSTRAINT \"${newForeignKeyName}\" TO \"${\n                        foreignKey.name\n                    }\"`,\n                ),\n            )\n\n            // replace constraint name\n            foreignKey.name = newForeignKeyName\n        })\n\n        // rename ENUM types\n        const enumColumns = newTable.columns.filter(\n            (column) => column.type === \"enum\" || column.type === \"simple-enum\",\n        )\n        for (const column of enumColumns) {\n            // skip renaming for user-defined enum name\n            if (column.enumName) continue\n\n            const oldEnumType = await this.getUserDefinedTypeName(\n                oldTable,\n                column,\n            )\n            upQueries.push(\n                new Query(\n                    `ALTER TYPE \"${oldEnumType.schema}\".\"${\n                        oldEnumType.name\n                    }\" RENAME TO ${this.buildEnumName(\n                        newTable,\n                        column,\n                        false,\n                    )}`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `ALTER TYPE ${this.buildEnumName(\n                        newTable,\n                        column,\n                    )} RENAME TO \"${oldEnumType.name}\"`,\n                ),\n            )\n        }\n\n        await this.executeQueries(upQueries, downQueries)\n    }\n\n    /**\n     * Creates a new column from the column in the table.\n     */\n    async addColumn(\n        tableOrName: Table | string,\n        column: TableColumn,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const clonedTable = table.clone()\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n\n        if (column.generationStrategy === \"increment\") {\n            throw new TypeORMError(\n                `Adding sequential generated columns into existing table is not supported`,\n            )\n        }\n\n        if (column.type === \"enum\" || column.type === \"simple-enum\") {\n            const hasEnum = await this.hasEnumType(table, column)\n            if (!hasEnum) {\n                upQueries.push(this.createEnumTypeSql(table, column))\n                downQueries.push(this.dropEnumTypeSql(table, column))\n            }\n        }\n\n        upQueries.push(\n            new Query(\n                `ALTER TABLE ${this.escapePath(\n                    table,\n                )} ADD ${this.buildCreateColumnSql(table, column)}`,\n            ),\n        )\n        downQueries.push(\n            new Query(\n                `ALTER TABLE ${this.escapePath(table)} DROP COLUMN \"${\n                    column.name\n                }\"`,\n            ),\n        )\n\n        // create or update primary key constraint\n        if (column.isPrimary) {\n            const primaryColumns = clonedTable.primaryColumns\n            // if table already have primary key, me must drop it and recreate again\n            // todo: https://go.crdb.dev/issue-v/48026/v21.1\n            if (primaryColumns.length > 0) {\n                const pkName = primaryColumns[0].primaryKeyConstraintName\n                    ? primaryColumns[0].primaryKeyConstraintName\n                    : this.connection.namingStrategy.primaryKeyName(\n                          clonedTable,\n                          primaryColumns.map((column) => column.name),\n                      )\n\n                const columnNames = primaryColumns\n                    .map((column) => `\"${column.name}\"`)\n                    .join(\", \")\n                upQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(\n                            table,\n                        )} DROP CONSTRAINT \"${pkName}\"`,\n                    ),\n                )\n                downQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(\n                            table,\n                        )} ADD CONSTRAINT \"${pkName}\" PRIMARY KEY (${columnNames})`,\n                    ),\n                )\n            }\n\n            primaryColumns.push(column)\n            const pkName = primaryColumns[0].primaryKeyConstraintName\n                ? primaryColumns[0].primaryKeyConstraintName\n                : this.connection.namingStrategy.primaryKeyName(\n                      clonedTable,\n                      primaryColumns.map((column) => column.name),\n                  )\n\n            const columnNames = primaryColumns\n                .map((column) => `\"${column.name}\"`)\n                .join(\", \")\n            upQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        table,\n                    )} ADD CONSTRAINT \"${pkName}\" PRIMARY KEY (${columnNames})`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        table,\n                    )} DROP CONSTRAINT \"${pkName}\"`,\n                ),\n            )\n        }\n\n        if (column.generatedType && column.asExpression) {\n            const currentSchema = await this.getCurrentSchema()\n            let { schema } = this.driver.parseTableName(table)\n            if (!schema) {\n                schema = currentSchema\n            }\n            const insertQuery = this.insertTypeormMetadataSql({\n                schema: schema,\n                table: table.name,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n                value: column.asExpression,\n            })\n\n            const deleteQuery = this.deleteTypeormMetadataSql({\n                schema: schema,\n                table: table.name,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n            })\n\n            upQueries.push(insertQuery)\n            downQueries.push(deleteQuery)\n        }\n\n        // create column index\n        const columnIndex = clonedTable.indices.find(\n            (index) =>\n                index.columnNames.length === 1 &&\n                index.columnNames[0] === column.name,\n        )\n        if (columnIndex) {\n            // CockroachDB stores unique indices as UNIQUE constraints\n            if (columnIndex.isUnique) {\n                const unique = new TableUnique({\n                    name: this.connection.namingStrategy.uniqueConstraintName(\n                        table,\n                        columnIndex.columnNames,\n                    ),\n                    columnNames: columnIndex.columnNames,\n                })\n                upQueries.push(this.createUniqueConstraintSql(table, unique))\n                downQueries.push(this.dropIndexSql(table, unique))\n                clonedTable.uniques.push(unique)\n            } else {\n                upQueries.push(this.createIndexSql(table, columnIndex))\n                downQueries.push(this.dropIndexSql(table, columnIndex))\n            }\n        }\n\n        // create unique constraint\n        if (column.isUnique) {\n            const uniqueConstraint = new TableUnique({\n                name: this.connection.namingStrategy.uniqueConstraintName(\n                    table,\n                    [column.name],\n                ),\n                columnNames: [column.name],\n            })\n            clonedTable.uniques.push(uniqueConstraint)\n            upQueries.push(\n                this.createUniqueConstraintSql(table, uniqueConstraint),\n            )\n            downQueries.push(this.dropIndexSql(table, uniqueConstraint.name!)) // CockroachDB creates indices for unique constraints\n        }\n\n        // create column's comment\n        if (column.comment) {\n            upQueries.push(\n                new Query(\n                    `COMMENT ON COLUMN ${this.escapePath(table)}.\"${\n                        column.name\n                    }\" IS ${this.escapeComment(column.comment)}`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `COMMENT ON COLUMN ${this.escapePath(table)}.\"${\n                        column.name\n                    }\" IS ${this.escapeComment(column.comment)}`,\n                ),\n            )\n        }\n\n        await this.executeQueries(upQueries, downQueries)\n\n        clonedTable.addColumn(column)\n        this.replaceCachedTable(table, clonedTable)\n    }\n\n    /**\n     * Creates a new columns from the column in the table.\n     */\n    async addColumns(\n        tableOrName: Table | string,\n        columns: TableColumn[],\n    ): Promise<void> {\n        for (const column of columns) {\n            await this.addColumn(tableOrName, column)\n        }\n    }\n\n    /**\n     * Renames column in the given table.\n     */\n    async renameColumn(\n        tableOrName: Table | string,\n        oldTableColumnOrName: TableColumn | string,\n        newTableColumnOrName: TableColumn | string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const oldColumn = InstanceChecker.isTableColumn(oldTableColumnOrName)\n            ? oldTableColumnOrName\n            : table.columns.find((c) => c.name === oldTableColumnOrName)\n        if (!oldColumn)\n            throw new TypeORMError(\n                `Column \"${oldTableColumnOrName}\" was not found in the \"${table.name}\" table.`,\n            )\n\n        let newColumn\n        if (InstanceChecker.isTableColumn(newTableColumnOrName)) {\n            newColumn = newTableColumnOrName\n        } else {\n            newColumn = oldColumn.clone()\n            newColumn.name = newTableColumnOrName\n        }\n\n        return this.changeColumn(table, oldColumn, newColumn)\n    }\n\n    /**\n     * Changes a column in the table.\n     */\n    async changeColumn(\n        tableOrName: Table | string,\n        oldTableColumnOrName: TableColumn | string,\n        newColumn: TableColumn,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        let clonedTable = table.clone()\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n        let defaultValueChanged = false\n\n        const oldColumn = InstanceChecker.isTableColumn(oldTableColumnOrName)\n            ? oldTableColumnOrName\n            : table.columns.find(\n                  (column) => column.name === oldTableColumnOrName,\n              )\n        if (!oldColumn)\n            throw new TypeORMError(\n                `Column \"${oldTableColumnOrName}\" was not found in the \"${table.name}\" table.`,\n            )\n\n        if (\n            oldColumn.type !== newColumn.type ||\n            oldColumn.length !== newColumn.length ||\n            newColumn.isArray !== oldColumn.isArray ||\n            oldColumn.generatedType !== newColumn.generatedType ||\n            oldColumn.asExpression !== newColumn.asExpression\n        ) {\n            // To avoid data conversion, we just recreate column\n            await this.dropColumn(table, oldColumn)\n            await this.addColumn(table, newColumn)\n\n            // update cloned table\n            clonedTable = table.clone()\n        } else {\n            if (oldColumn.name !== newColumn.name) {\n                // rename column\n                upQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(table)} RENAME COLUMN \"${\n                            oldColumn.name\n                        }\" TO \"${newColumn.name}\"`,\n                    ),\n                )\n                downQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(table)} RENAME COLUMN \"${\n                            newColumn.name\n                        }\" TO \"${oldColumn.name}\"`,\n                    ),\n                )\n\n                // rename ENUM type\n                if (\n                    oldColumn.type === \"enum\" ||\n                    oldColumn.type === \"simple-enum\"\n                ) {\n                    const oldEnumType = await this.getUserDefinedTypeName(\n                        table,\n                        oldColumn,\n                    )\n                    upQueries.push(\n                        new Query(\n                            `ALTER TYPE \"${oldEnumType.schema}\".\"${\n                                oldEnumType.name\n                            }\" RENAME TO ${this.buildEnumName(\n                                table,\n                                newColumn,\n                                false,\n                            )}`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TYPE ${this.buildEnumName(\n                                table,\n                                newColumn,\n                            )} RENAME TO \"${oldEnumType.name}\"`,\n                        ),\n                    )\n                }\n\n                // rename column primary key constraint\n                if (\n                    oldColumn.isPrimary === true &&\n                    !oldColumn.primaryKeyConstraintName\n                ) {\n                    const primaryColumns = clonedTable.primaryColumns\n\n                    // build old primary constraint name\n                    const columnNames = primaryColumns.map(\n                        (column) => column.name,\n                    )\n                    const oldPkName =\n                        this.connection.namingStrategy.primaryKeyName(\n                            clonedTable,\n                            columnNames,\n                        )\n\n                    // replace old column name with new column name\n                    columnNames.splice(columnNames.indexOf(oldColumn.name), 1)\n                    columnNames.push(newColumn.name)\n\n                    // build new primary constraint name\n                    const newPkName =\n                        this.connection.namingStrategy.primaryKeyName(\n                            clonedTable,\n                            columnNames,\n                        )\n\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} RENAME CONSTRAINT \"${oldPkName}\" TO \"${newPkName}\"`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} RENAME CONSTRAINT \"${newPkName}\" TO \"${oldPkName}\"`,\n                        ),\n                    )\n                }\n\n                // rename unique constraints\n                clonedTable.findColumnUniques(oldColumn).forEach((unique) => {\n                    const oldUniqueName =\n                        this.connection.namingStrategy.uniqueConstraintName(\n                            clonedTable,\n                            unique.columnNames,\n                        )\n\n                    // Skip renaming if Unique has user defined constraint name\n                    if (unique.name !== oldUniqueName) return\n\n                    // build new constraint name\n                    unique.columnNames.splice(\n                        unique.columnNames.indexOf(oldColumn.name),\n                        1,\n                    )\n                    unique.columnNames.push(newColumn.name)\n                    const newUniqueName =\n                        this.connection.namingStrategy.uniqueConstraintName(\n                            clonedTable,\n                            unique.columnNames,\n                        )\n\n                    // build queries\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} RENAME CONSTRAINT \"${\n                                unique.name\n                            }\" TO \"${newUniqueName}\"`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} RENAME CONSTRAINT \"${newUniqueName}\" TO \"${\n                                unique.name\n                            }\"`,\n                        ),\n                    )\n\n                    // replace constraint name\n                    unique.name = newUniqueName\n                })\n\n                // rename index constraints\n                clonedTable.findColumnIndices(oldColumn).forEach((index) => {\n                    const oldIndexName =\n                        this.connection.namingStrategy.indexName(\n                            clonedTable,\n                            index.columnNames,\n                            index.where,\n                        )\n\n                    // Skip renaming if Index has user defined constraint name\n                    if (index.name !== oldIndexName) return\n\n                    // build new constraint name\n                    index.columnNames.splice(\n                        index.columnNames.indexOf(oldColumn.name),\n                        1,\n                    )\n                    index.columnNames.push(newColumn.name)\n                    const { schema } = this.driver.parseTableName(table)\n                    const newIndexName =\n                        this.connection.namingStrategy.indexName(\n                            clonedTable,\n                            index.columnNames,\n                            index.where,\n                        )\n\n                    // build queries\n                    const up = schema\n                        ? `ALTER INDEX \"${schema}\".\"${index.name}\" RENAME TO \"${newIndexName}\"`\n                        : `ALTER INDEX \"${index.name}\" RENAME TO \"${newIndexName}\"`\n                    const down = schema\n                        ? `ALTER INDEX \"${schema}\".\"${newIndexName}\" RENAME TO \"${index.name}\"`\n                        : `ALTER INDEX \"${newIndexName}\" RENAME TO \"${index.name}\"`\n                    upQueries.push(new Query(up))\n                    downQueries.push(new Query(down))\n\n                    // replace constraint name\n                    index.name = newIndexName\n                })\n\n                // rename foreign key constraints\n                clonedTable\n                    .findColumnForeignKeys(oldColumn)\n                    .forEach((foreignKey) => {\n                        const foreignKeyName =\n                            this.connection.namingStrategy.foreignKeyName(\n                                clonedTable,\n                                foreignKey.columnNames,\n                                this.getTablePath(foreignKey),\n                                foreignKey.referencedColumnNames,\n                            )\n\n                        // Skip renaming if foreign key has user defined constraint name\n                        if (foreignKey.name !== foreignKeyName) return\n\n                        // build new constraint name\n                        foreignKey.columnNames.splice(\n                            foreignKey.columnNames.indexOf(oldColumn.name),\n                            1,\n                        )\n                        foreignKey.columnNames.push(newColumn.name)\n                        const newForeignKeyName =\n                            this.connection.namingStrategy.foreignKeyName(\n                                clonedTable,\n                                foreignKey.columnNames,\n                                this.getTablePath(foreignKey),\n                                foreignKey.referencedColumnNames,\n                            )\n\n                        // build queries\n                        upQueries.push(\n                            new Query(\n                                `ALTER TABLE ${this.escapePath(\n                                    table,\n                                )} RENAME CONSTRAINT \"${\n                                    foreignKey.name\n                                }\" TO \"${newForeignKeyName}\"`,\n                            ),\n                        )\n                        downQueries.push(\n                            new Query(\n                                `ALTER TABLE ${this.escapePath(\n                                    table,\n                                )} RENAME CONSTRAINT \"${newForeignKeyName}\" TO \"${\n                                    foreignKey.name\n                                }\"`,\n                            ),\n                        )\n\n                        // replace constraint name\n                        foreignKey.name = newForeignKeyName\n                    })\n\n                // rename old column in the Table object\n                const oldTableColumn = clonedTable.columns.find(\n                    (column) => column.name === oldColumn.name,\n                )\n                clonedTable.columns[\n                    clonedTable.columns.indexOf(oldTableColumn!)\n                ].name = newColumn.name\n                oldColumn.name = newColumn.name\n            }\n\n            if (\n                newColumn.precision !== oldColumn.precision ||\n                newColumn.scale !== oldColumn.scale\n            ) {\n                upQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(table)} ALTER COLUMN \"${\n                            newColumn.name\n                        }\" TYPE ${this.driver.createFullType(newColumn)}`,\n                    ),\n                )\n                downQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(table)} ALTER COLUMN \"${\n                            newColumn.name\n                        }\" TYPE ${this.driver.createFullType(oldColumn)}`,\n                    ),\n                )\n            }\n\n            if (oldColumn.isNullable !== newColumn.isNullable) {\n                if (newColumn.isNullable) {\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ALTER COLUMN \"${oldColumn.name}\" DROP NOT NULL`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ALTER COLUMN \"${oldColumn.name}\" SET NOT NULL`,\n                        ),\n                    )\n                } else {\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ALTER COLUMN \"${oldColumn.name}\" SET NOT NULL`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ALTER COLUMN \"${oldColumn.name}\" DROP NOT NULL`,\n                        ),\n                    )\n                }\n            }\n\n            if (oldColumn.comment !== newColumn.comment) {\n                upQueries.push(\n                    new Query(\n                        `COMMENT ON COLUMN ${this.escapePath(table)}.\"${\n                            oldColumn.name\n                        }\" IS ${this.escapeComment(newColumn.comment)}`,\n                    ),\n                )\n                downQueries.push(\n                    new Query(\n                        `COMMENT ON COLUMN ${this.escapePath(table)}.\"${\n                            newColumn.name\n                        }\" IS ${this.escapeComment(oldColumn.comment)}`,\n                    ),\n                )\n            }\n\n            if (newColumn.isPrimary !== oldColumn.isPrimary) {\n                const primaryColumns = clonedTable.primaryColumns\n\n                // if primary column state changed, we must always drop existed constraint.\n                if (primaryColumns.length > 0) {\n                    const pkName = primaryColumns[0].primaryKeyConstraintName\n                        ? primaryColumns[0].primaryKeyConstraintName\n                        : this.connection.namingStrategy.primaryKeyName(\n                              clonedTable,\n                              primaryColumns.map((column) => column.name),\n                          )\n\n                    const columnNames = primaryColumns\n                        .map((column) => `\"${column.name}\"`)\n                        .join(\", \")\n\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} DROP CONSTRAINT \"${pkName}\"`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ADD CONSTRAINT \"${pkName}\" PRIMARY KEY (${columnNames})`,\n                        ),\n                    )\n                }\n\n                if (newColumn.isPrimary === true) {\n                    primaryColumns.push(newColumn)\n                    // update column in table\n                    const column = clonedTable.columns.find(\n                        (column) => column.name === newColumn.name,\n                    )\n                    column!.isPrimary = true\n                    const pkName = primaryColumns[0].primaryKeyConstraintName\n                        ? primaryColumns[0].primaryKeyConstraintName\n                        : this.connection.namingStrategy.primaryKeyName(\n                              clonedTable,\n                              primaryColumns.map((column) => column.name),\n                          )\n\n                    const columnNames = primaryColumns\n                        .map((column) => `\"${column.name}\"`)\n                        .join(\", \")\n\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ADD CONSTRAINT \"${pkName}\" PRIMARY KEY (${columnNames})`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} DROP CONSTRAINT \"${pkName}\"`,\n                        ),\n                    )\n                } else {\n                    const primaryColumn = primaryColumns.find(\n                        (c) => c.name === newColumn.name,\n                    )\n                    primaryColumns.splice(\n                        primaryColumns.indexOf(primaryColumn!),\n                        1,\n                    )\n\n                    // update column in table\n                    const column = clonedTable.columns.find(\n                        (column) => column.name === newColumn.name,\n                    )\n                    column!.isPrimary = false\n\n                    // if we have another primary keys, we must recreate constraint.\n                    if (primaryColumns.length > 0) {\n                        const pkName = primaryColumns[0]\n                            .primaryKeyConstraintName\n                            ? primaryColumns[0].primaryKeyConstraintName\n                            : this.connection.namingStrategy.primaryKeyName(\n                                  clonedTable,\n                                  primaryColumns.map((column) => column.name),\n                              )\n\n                        const columnNames = primaryColumns\n                            .map((column) => `\"${column.name}\"`)\n                            .join(\", \")\n                        upQueries.push(\n                            new Query(\n                                `ALTER TABLE ${this.escapePath(\n                                    table,\n                                )} ADD CONSTRAINT \"${pkName}\" PRIMARY KEY (${columnNames})`,\n                            ),\n                        )\n                        downQueries.push(\n                            new Query(\n                                `ALTER TABLE ${this.escapePath(\n                                    table,\n                                )} DROP CONSTRAINT \"${pkName}\"`,\n                            ),\n                        )\n                    }\n                }\n            }\n\n            if (newColumn.isUnique !== oldColumn.isUnique) {\n                if (newColumn.isUnique) {\n                    const uniqueConstraint = new TableUnique({\n                        name: this.connection.namingStrategy.uniqueConstraintName(\n                            table,\n                            [newColumn.name],\n                        ),\n                        columnNames: [newColumn.name],\n                    })\n                    clonedTable.uniques.push(uniqueConstraint)\n                    upQueries.push(\n                        this.createUniqueConstraintSql(table, uniqueConstraint),\n                    )\n                    // CockroachDB creates index for UNIQUE constraint.\n                    // We must use DROP INDEX ... CASCADE instead of DROP CONSTRAINT.\n                    downQueries.push(this.dropIndexSql(table, uniqueConstraint))\n                } else {\n                    const uniqueConstraint = clonedTable.uniques.find(\n                        (unique) => {\n                            return (\n                                unique.columnNames.length === 1 &&\n                                !!unique.columnNames.find(\n                                    (columnName) =>\n                                        columnName === newColumn.name,\n                                )\n                            )\n                        },\n                    )\n                    clonedTable.uniques.splice(\n                        clonedTable.uniques.indexOf(uniqueConstraint!),\n                        1,\n                    )\n                    // CockroachDB creates index for UNIQUE constraint.\n                    // We must use DROP INDEX ... CASCADE instead of DROP CONSTRAINT.\n                    upQueries.push(this.dropIndexSql(table, uniqueConstraint!))\n                    downQueries.push(\n                        this.createUniqueConstraintSql(\n                            table,\n                            uniqueConstraint!,\n                        ),\n                    )\n                }\n            }\n\n            if (\n                (newColumn.type === \"enum\" ||\n                    newColumn.type === \"simple-enum\") &&\n                (oldColumn.type === \"enum\" ||\n                    oldColumn.type === \"simple-enum\") &&\n                (!OrmUtils.isArraysEqual(newColumn.enum!, oldColumn.enum!) ||\n                    newColumn.enumName !== oldColumn.enumName)\n            ) {\n                const arraySuffix = newColumn.isArray ? \"[]\" : \"\"\n\n                // \"public\".\"new_enum\"\n                const newEnumName = this.buildEnumName(table, newColumn)\n\n                // \"public\".\"old_enum\"\n                const oldEnumName = this.buildEnumName(table, oldColumn)\n\n                // \"old_enum\"\n                const oldEnumNameWithoutSchema = this.buildEnumName(\n                    table,\n                    oldColumn,\n                    false,\n                )\n\n                //\"public\".\"old_enum_old\"\n                const oldEnumNameWithSchema_old = this.buildEnumName(\n                    table,\n                    oldColumn,\n                    true,\n                    false,\n                    true,\n                )\n\n                //\"old_enum_old\"\n                const oldEnumNameWithoutSchema_old = this.buildEnumName(\n                    table,\n                    oldColumn,\n                    false,\n                    false,\n                    true,\n                )\n\n                // rename old ENUM\n                upQueries.push(\n                    new Query(\n                        `ALTER TYPE ${oldEnumName} RENAME TO ${oldEnumNameWithoutSchema_old}`,\n                    ),\n                )\n                downQueries.push(\n                    new Query(\n                        `ALTER TYPE ${oldEnumNameWithSchema_old} RENAME TO ${oldEnumNameWithoutSchema}`,\n                    ),\n                )\n\n                // create new ENUM\n                upQueries.push(\n                    this.createEnumTypeSql(table, newColumn, newEnumName),\n                )\n                downQueries.push(\n                    this.dropEnumTypeSql(table, newColumn, newEnumName),\n                )\n\n                // if column have default value, we must drop it to avoid issues with type casting\n                if (\n                    oldColumn.default !== null &&\n                    oldColumn.default !== undefined\n                ) {\n                    // mark default as changed to prevent double update\n                    defaultValueChanged = true\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ALTER COLUMN \"${oldColumn.name}\" DROP DEFAULT`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ALTER COLUMN \"${oldColumn.name}\" SET DEFAULT ${\n                                oldColumn.default\n                            }`,\n                        ),\n                    )\n                }\n\n                // build column types\n                const upType = `${newEnumName}${arraySuffix} USING \"${newColumn.name}\"::\"text\"::${newEnumName}${arraySuffix}`\n                const downType = `${oldEnumNameWithSchema_old}${arraySuffix} USING \"${newColumn.name}\"::\"text\"::${oldEnumNameWithSchema_old}${arraySuffix}`\n\n                upQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(table)} ALTER COLUMN \"${\n                            newColumn.name\n                        }\" TYPE ${upType}`,\n                    ),\n                )\n\n                // we add a delay here since for some reason cockroachdb fails with\n                // \"cannot drop type because other objects still depend on it\" error\n                // if we are trying to drop type right after we altered it.\n                upQueries.push(new Query(`SELECT pg_sleep(0.1)`))\n                downQueries.push(new Query(`SELECT pg_sleep(0.1)`))\n\n                downQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(table)} ALTER COLUMN \"${\n                            newColumn.name\n                        }\" TYPE ${downType}`,\n                    ),\n                )\n\n                // restore column default or create new one\n                if (\n                    newColumn.default !== null &&\n                    newColumn.default !== undefined\n                ) {\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ALTER COLUMN \"${newColumn.name}\" SET DEFAULT ${\n                                newColumn.default\n                            }`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ALTER COLUMN \"${newColumn.name}\" DROP DEFAULT`,\n                        ),\n                    )\n                }\n\n                // remove old ENUM\n                upQueries.push(\n                    this.dropEnumTypeSql(\n                        table,\n                        oldColumn,\n                        oldEnumNameWithSchema_old,\n                    ),\n                )\n                downQueries.push(\n                    this.createEnumTypeSql(\n                        table,\n                        oldColumn,\n                        oldEnumNameWithSchema_old,\n                    ),\n                )\n            }\n\n            if (\n                oldColumn.isGenerated !== newColumn.isGenerated &&\n                newColumn.generationStrategy !== \"uuid\"\n            ) {\n                if (newColumn.isGenerated) {\n                    if (newColumn.generationStrategy === \"increment\") {\n                        throw new TypeORMError(\n                            `Adding sequential generated columns into existing table is not supported`,\n                        )\n                    } else if (newColumn.generationStrategy === \"rowid\") {\n                        upQueries.push(\n                            new Query(\n                                `ALTER TABLE ${this.escapePath(\n                                    table,\n                                )} ALTER COLUMN \"${\n                                    newColumn.name\n                                }\" SET DEFAULT unique_rowid()`,\n                            ),\n                        )\n                        downQueries.push(\n                            new Query(\n                                `ALTER TABLE ${this.escapePath(\n                                    table,\n                                )} ALTER COLUMN \"${\n                                    newColumn.name\n                                }\" DROP DEFAULT`,\n                            ),\n                        )\n                    }\n                } else {\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ALTER COLUMN \"${newColumn.name}\" DROP DEFAULT`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ALTER COLUMN \"${\n                                newColumn.name\n                            }\" SET DEFAULT unique_rowid()`,\n                        ),\n                    )\n                }\n            }\n\n            if (\n                newColumn.default !== oldColumn.default &&\n                !defaultValueChanged\n            ) {\n                if (\n                    newColumn.default !== null &&\n                    newColumn.default !== undefined\n                ) {\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ALTER COLUMN \"${newColumn.name}\" SET DEFAULT ${\n                                newColumn.default\n                            }`,\n                        ),\n                    )\n\n                    if (\n                        oldColumn.default !== null &&\n                        oldColumn.default !== undefined\n                    ) {\n                        downQueries.push(\n                            new Query(\n                                `ALTER TABLE ${this.escapePath(\n                                    table,\n                                )} ALTER COLUMN \"${\n                                    newColumn.name\n                                }\" SET DEFAULT ${oldColumn.default}`,\n                            ),\n                        )\n                    } else {\n                        downQueries.push(\n                            new Query(\n                                `ALTER TABLE ${this.escapePath(\n                                    table,\n                                )} ALTER COLUMN \"${\n                                    newColumn.name\n                                }\" DROP DEFAULT`,\n                            ),\n                        )\n                    }\n                } else if (\n                    oldColumn.default !== null &&\n                    oldColumn.default !== undefined\n                ) {\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ALTER COLUMN \"${newColumn.name}\" DROP DEFAULT`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ALTER COLUMN \"${newColumn.name}\" SET DEFAULT ${\n                                oldColumn.default\n                            }`,\n                        ),\n                    )\n                }\n            }\n        }\n\n        if (\n            (newColumn.spatialFeatureType || \"\").toLowerCase() !==\n                (oldColumn.spatialFeatureType || \"\").toLowerCase() ||\n            newColumn.srid !== oldColumn.srid\n        ) {\n            upQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(table)} ALTER COLUMN \"${\n                        newColumn.name\n                    }\" TYPE ${this.driver.createFullType(newColumn)}`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(table)} ALTER COLUMN \"${\n                        newColumn.name\n                    }\" TYPE ${this.driver.createFullType(oldColumn)}`,\n                ),\n            )\n        }\n\n        await this.executeQueries(upQueries, downQueries)\n        this.replaceCachedTable(table, clonedTable)\n    }\n\n    /**\n     * Changes a column in the table.\n     */\n    async changeColumns(\n        tableOrName: Table | string,\n        changedColumns: { newColumn: TableColumn; oldColumn: TableColumn }[],\n    ): Promise<void> {\n        for (const { oldColumn, newColumn } of changedColumns) {\n            await this.changeColumn(tableOrName, oldColumn, newColumn)\n        }\n    }\n\n    /**\n     * Drops column in the table.\n     */\n    async dropColumn(\n        tableOrName: Table | string,\n        columnOrName: TableColumn | string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const column = InstanceChecker.isTableColumn(columnOrName)\n            ? columnOrName\n            : table.findColumnByName(columnOrName)\n        if (!column)\n            throw new TypeORMError(\n                `Column \"${columnOrName}\" was not found in table \"${table.name}\"`,\n            )\n\n        const clonedTable = table.clone()\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n\n        // drop primary key constraint\n        // todo: https://go.crdb.dev/issue-v/48026/v21.1\n        if (column.isPrimary) {\n            const pkName = column.primaryKeyConstraintName\n                ? column.primaryKeyConstraintName\n                : this.connection.namingStrategy.primaryKeyName(\n                      clonedTable,\n                      clonedTable.primaryColumns.map((column) => column.name),\n                  )\n\n            const columnNames = clonedTable.primaryColumns\n                .map((primaryColumn) => `\"${primaryColumn.name}\"`)\n                .join(\", \")\n            upQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        clonedTable,\n                    )} DROP CONSTRAINT \"${pkName}\"`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        clonedTable,\n                    )} ADD CONSTRAINT \"${pkName}\" PRIMARY KEY (${columnNames})`,\n                ),\n            )\n\n            // update column in table\n            const tableColumn = clonedTable.findColumnByName(column.name)\n            tableColumn!.isPrimary = false\n\n            // if primary key have multiple columns, we must recreate it without dropped column\n            if (clonedTable.primaryColumns.length > 0) {\n                const pkName = clonedTable.primaryColumns[0]\n                    .primaryKeyConstraintName\n                    ? clonedTable.primaryColumns[0].primaryKeyConstraintName\n                    : this.connection.namingStrategy.primaryKeyName(\n                          clonedTable,\n                          clonedTable.primaryColumns.map(\n                              (column) => column.name,\n                          ),\n                      )\n\n                const columnNames = clonedTable.primaryColumns\n                    .map((primaryColumn) => `\"${primaryColumn.name}\"`)\n                    .join(\", \")\n\n                upQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(\n                            clonedTable,\n                        )} ADD CONSTRAINT \"${pkName}\" PRIMARY KEY (${columnNames})`,\n                    ),\n                )\n                downQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(\n                            clonedTable,\n                        )} DROP CONSTRAINT \"${pkName}\"`,\n                    ),\n                )\n            }\n        }\n\n        // drop column index\n        const columnIndex = clonedTable.indices.find(\n            (index) =>\n                index.columnNames.length === 1 &&\n                index.columnNames[0] === column.name,\n        )\n        if (columnIndex) {\n            clonedTable.indices.splice(\n                clonedTable.indices.indexOf(columnIndex),\n                1,\n            )\n            upQueries.push(this.dropIndexSql(table, columnIndex))\n            downQueries.push(this.createIndexSql(table, columnIndex))\n        }\n\n        // drop column check\n        const columnCheck = clonedTable.checks.find(\n            (check) =>\n                !!check.columnNames &&\n                check.columnNames.length === 1 &&\n                check.columnNames[0] === column.name,\n        )\n        if (columnCheck) {\n            clonedTable.checks.splice(\n                clonedTable.checks.indexOf(columnCheck),\n                1,\n            )\n            upQueries.push(this.dropCheckConstraintSql(table, columnCheck))\n            downQueries.push(this.createCheckConstraintSql(table, columnCheck))\n        }\n\n        // drop column unique\n        const columnUnique = clonedTable.uniques.find(\n            (unique) =>\n                unique.columnNames.length === 1 &&\n                unique.columnNames[0] === column.name,\n        )\n        if (columnUnique) {\n            clonedTable.uniques.splice(\n                clonedTable.uniques.indexOf(columnUnique),\n                1,\n            )\n            upQueries.push(this.dropIndexSql(table, columnUnique.name!)) // CockroachDB creates indices for unique constraints\n            downQueries.push(\n                this.createUniqueConstraintSql(table, columnUnique),\n            )\n        }\n\n        upQueries.push(\n            new Query(\n                `ALTER TABLE ${this.escapePath(table)} DROP COLUMN \"${\n                    column.name\n                }\"`,\n            ),\n        )\n        downQueries.push(\n            new Query(\n                `ALTER TABLE ${this.escapePath(\n                    table,\n                )} ADD ${this.buildCreateColumnSql(table, column)}`,\n            ),\n        )\n\n        if (column.generationStrategy === \"increment\") {\n            upQueries.push(\n                new Query(\n                    `DROP SEQUENCE ${this.escapePath(\n                        this.buildSequencePath(table, column),\n                    )}`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `CREATE SEQUENCE ${this.escapePath(\n                        this.buildSequencePath(table, column),\n                    )}`,\n                ),\n            )\n        }\n\n        if (column.generatedType && column.asExpression) {\n            const currentSchema = await this.getCurrentSchema()\n            let { schema } = this.driver.parseTableName(table)\n            if (!schema) {\n                schema = currentSchema\n            }\n            const deleteQuery = this.deleteTypeormMetadataSql({\n                schema: schema,\n                table: table.name,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n            })\n            const insertQuery = this.insertTypeormMetadataSql({\n                schema: schema,\n                table: table.name,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n                value: column.asExpression,\n            })\n\n            upQueries.push(deleteQuery)\n            downQueries.push(insertQuery)\n        }\n\n        // drop enum type\n        if (column.type === \"enum\" || column.type === \"simple-enum\") {\n            const hasEnum = await this.hasEnumType(table, column)\n            if (hasEnum) {\n                const enumType = await this.getUserDefinedTypeName(\n                    table,\n                    column,\n                )\n                const escapedEnumName = `\"${enumType.schema}\".\"${enumType.name}\"`\n                upQueries.push(\n                    this.dropEnumTypeSql(table, column, escapedEnumName),\n                )\n                downQueries.push(\n                    this.createEnumTypeSql(table, column, escapedEnumName),\n                )\n            }\n        }\n\n        await this.executeQueries(upQueries, downQueries)\n\n        clonedTable.removeColumn(column)\n        this.replaceCachedTable(table, clonedTable)\n    }\n\n    /**\n     * Drops the columns in the table.\n     */\n    async dropColumns(\n        tableOrName: Table | string,\n        columns: TableColumn[] | string[],\n    ): Promise<void> {\n        for (const column of columns) {\n            await this.dropColumn(tableOrName, column)\n        }\n    }\n\n    /**\n     * Creates a new primary key.\n     */\n    async createPrimaryKey(\n        tableOrName: Table | string,\n        columnNames: string[],\n        constraintName?: string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const clonedTable = table.clone()\n\n        const up = this.createPrimaryKeySql(table, columnNames, constraintName)\n\n        // mark columns as primary, because dropPrimaryKeySql build constraint name from table primary column names.\n        clonedTable.columns.forEach((column) => {\n            if (columnNames.find((columnName) => columnName === column.name))\n                column.isPrimary = true\n        })\n        const down = this.dropPrimaryKeySql(clonedTable)\n\n        await this.executeQueries(up, down)\n        this.replaceCachedTable(table, clonedTable)\n    }\n\n    /**\n     * Updates composite primary keys.\n     */\n    async updatePrimaryKeys(\n        tableOrName: Table | string,\n        columns: TableColumn[],\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const clonedTable = table.clone()\n        const columnNames = columns.map((column) => column.name)\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n\n        // if table already have primary columns, we must drop them.\n        const primaryColumns = clonedTable.primaryColumns\n        if (primaryColumns.length > 0) {\n            const pkName = primaryColumns[0].primaryKeyConstraintName\n                ? primaryColumns[0].primaryKeyConstraintName\n                : this.connection.namingStrategy.primaryKeyName(\n                      clonedTable,\n                      primaryColumns.map((column) => column.name),\n                  )\n\n            const columnNamesString = primaryColumns\n                .map((column) => `\"${column.name}\"`)\n                .join(\", \")\n\n            upQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        table,\n                    )} DROP CONSTRAINT \"${pkName}\"`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        table,\n                    )} ADD CONSTRAINT \"${pkName}\" PRIMARY KEY (${columnNamesString})`,\n                ),\n            )\n        }\n\n        // update columns in table.\n        clonedTable.columns\n            .filter((column) => columnNames.indexOf(column.name) !== -1)\n            .forEach((column) => (column.isPrimary = true))\n\n        const pkName = primaryColumns[0].primaryKeyConstraintName\n            ? primaryColumns[0].primaryKeyConstraintName\n            : this.connection.namingStrategy.primaryKeyName(\n                  clonedTable,\n                  columnNames,\n              )\n\n        const columnNamesString = columnNames\n            .map((columnName) => `\"${columnName}\"`)\n            .join(\", \")\n        upQueries.push(\n            new Query(\n                `ALTER TABLE ${this.escapePath(\n                    table,\n                )} ADD CONSTRAINT \"${pkName}\" PRIMARY KEY (${columnNamesString})`,\n            ),\n        )\n        downQueries.push(\n            new Query(\n                `ALTER TABLE ${this.escapePath(\n                    table,\n                )} DROP CONSTRAINT \"${pkName}\"`,\n            ),\n        )\n\n        await this.executeQueries(upQueries, downQueries)\n        this.replaceCachedTable(table, clonedTable)\n    }\n\n    /**\n     * Drops a primary key.\n     */\n    async dropPrimaryKey(\n        tableOrName: Table | string,\n        constraintName?: string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const up = this.dropPrimaryKeySql(table)\n        const down = this.createPrimaryKeySql(\n            table,\n            table.primaryColumns.map((column) => column.name),\n            constraintName,\n        )\n        await this.executeQueries(up, down)\n        table.primaryColumns.forEach((column) => {\n            column.isPrimary = false\n        })\n    }\n\n    /**\n     * Creates new unique constraint.\n     */\n    async createUniqueConstraint(\n        tableOrName: Table | string,\n        uniqueConstraint: TableUnique,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n\n        // new unique constraint may be passed without name. In this case we generate unique name manually.\n        if (!uniqueConstraint.name)\n            uniqueConstraint.name =\n                this.connection.namingStrategy.uniqueConstraintName(\n                    table,\n                    uniqueConstraint.columnNames,\n                )\n\n        const up = this.createUniqueConstraintSql(table, uniqueConstraint)\n        // CockroachDB creates index for UNIQUE constraint.\n        // We must use DROP INDEX ... CASCADE instead of DROP CONSTRAINT.\n        const down = this.dropIndexSql(table, uniqueConstraint)\n        await this.executeQueries(up, down)\n        table.addUniqueConstraint(uniqueConstraint)\n    }\n\n    /**\n     * Creates new unique constraints.\n     */\n    async createUniqueConstraints(\n        tableOrName: Table | string,\n        uniqueConstraints: TableUnique[],\n    ): Promise<void> {\n        for (const uniqueConstraint of uniqueConstraints) {\n            await this.createUniqueConstraint(tableOrName, uniqueConstraint)\n        }\n    }\n\n    /**\n     * Drops unique constraint.\n     */\n    async dropUniqueConstraint(\n        tableOrName: Table | string,\n        uniqueOrName: TableUnique | string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const uniqueConstraint = InstanceChecker.isTableUnique(uniqueOrName)\n            ? uniqueOrName\n            : table.uniques.find((u) => u.name === uniqueOrName)\n        if (!uniqueConstraint)\n            throw new TypeORMError(\n                `Supplied unique constraint was not found in table ${table.name}`,\n            )\n\n        // CockroachDB creates index for UNIQUE constraint.\n        // We must use DROP INDEX ... CASCADE instead of DROP CONSTRAINT.\n        const up = this.dropIndexSql(table, uniqueConstraint)\n        const down = this.createUniqueConstraintSql(table, uniqueConstraint)\n        await this.executeQueries(up, down)\n        table.removeUniqueConstraint(uniqueConstraint)\n    }\n\n    /**\n     * Drops unique constraints.\n     */\n    async dropUniqueConstraints(\n        tableOrName: Table | string,\n        uniqueConstraints: TableUnique[],\n    ): Promise<void> {\n        for (const uniqueConstraint of uniqueConstraints) {\n            await this.dropUniqueConstraint(tableOrName, uniqueConstraint)\n        }\n    }\n\n    /**\n     * Creates new check constraint.\n     */\n    async createCheckConstraint(\n        tableOrName: Table | string,\n        checkConstraint: TableCheck,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n\n        // new unique constraint may be passed without name. In this case we generate unique name manually.\n        if (!checkConstraint.name)\n            checkConstraint.name =\n                this.connection.namingStrategy.checkConstraintName(\n                    table,\n                    checkConstraint.expression!,\n                )\n\n        const up = this.createCheckConstraintSql(table, checkConstraint)\n        const down = this.dropCheckConstraintSql(table, checkConstraint)\n        await this.executeQueries(up, down)\n        table.addCheckConstraint(checkConstraint)\n    }\n\n    /**\n     * Creates new check constraints.\n     */\n    async createCheckConstraints(\n        tableOrName: Table | string,\n        checkConstraints: TableCheck[],\n    ): Promise<void> {\n        const promises = checkConstraints.map((checkConstraint) =>\n            this.createCheckConstraint(tableOrName, checkConstraint),\n        )\n        await Promise.all(promises)\n    }\n\n    /**\n     * Drops check constraint.\n     */\n    async dropCheckConstraint(\n        tableOrName: Table | string,\n        checkOrName: TableCheck | string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const checkConstraint = InstanceChecker.isTableCheck(checkOrName)\n            ? checkOrName\n            : table.checks.find((c) => c.name === checkOrName)\n        if (!checkConstraint)\n            throw new TypeORMError(\n                `Supplied check constraint was not found in table ${table.name}`,\n            )\n\n        const up = this.dropCheckConstraintSql(table, checkConstraint)\n        const down = this.createCheckConstraintSql(table, checkConstraint)\n        await this.executeQueries(up, down)\n        table.removeCheckConstraint(checkConstraint)\n    }\n\n    /**\n     * Drops check constraints.\n     */\n    async dropCheckConstraints(\n        tableOrName: Table | string,\n        checkConstraints: TableCheck[],\n    ): Promise<void> {\n        const promises = checkConstraints.map((checkConstraint) =>\n            this.dropCheckConstraint(tableOrName, checkConstraint),\n        )\n        await Promise.all(promises)\n    }\n\n    /**\n     * Creates new exclusion constraint.\n     */\n    async createExclusionConstraint(\n        tableOrName: Table | string,\n        exclusionConstraint: TableExclusion,\n    ): Promise<void> {\n        throw new TypeORMError(\n            `CockroachDB does not support exclusion constraints.`,\n        )\n    }\n\n    /**\n     * Creates new exclusion constraints.\n     */\n    async createExclusionConstraints(\n        tableOrName: Table | string,\n        exclusionConstraints: TableExclusion[],\n    ): Promise<void> {\n        throw new TypeORMError(\n            `CockroachDB does not support exclusion constraints.`,\n        )\n    }\n\n    /**\n     * Drops exclusion constraint.\n     */\n    async dropExclusionConstraint(\n        tableOrName: Table | string,\n        exclusionOrName: TableExclusion | string,\n    ): Promise<void> {\n        throw new TypeORMError(\n            `CockroachDB does not support exclusion constraints.`,\n        )\n    }\n\n    /**\n     * Drops exclusion constraints.\n     */\n    async dropExclusionConstraints(\n        tableOrName: Table | string,\n        exclusionConstraints: TableExclusion[],\n    ): Promise<void> {\n        throw new TypeORMError(\n            `CockroachDB does not support exclusion constraints.`,\n        )\n    }\n\n    /**\n     * Creates a new foreign key.\n     */\n    async createForeignKey(\n        tableOrName: Table | string,\n        foreignKey: TableForeignKey,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n\n        // new FK may be passed without name. In this case we generate FK name manually.\n        if (!foreignKey.name)\n            foreignKey.name = this.connection.namingStrategy.foreignKeyName(\n                table,\n                foreignKey.columnNames,\n                this.getTablePath(foreignKey),\n                foreignKey.referencedColumnNames,\n            )\n\n        const up = this.createForeignKeySql(table, foreignKey)\n        const down = this.dropForeignKeySql(table, foreignKey)\n        await this.executeQueries(up, down)\n        table.addForeignKey(foreignKey)\n    }\n\n    /**\n     * Creates a new foreign keys.\n     */\n    async createForeignKeys(\n        tableOrName: Table | string,\n        foreignKeys: TableForeignKey[],\n    ): Promise<void> {\n        for (const foreignKey of foreignKeys) {\n            await this.createForeignKey(tableOrName, foreignKey)\n        }\n    }\n\n    /**\n     * Drops a foreign key from the table.\n     */\n    async dropForeignKey(\n        tableOrName: Table | string,\n        foreignKeyOrName: TableForeignKey | string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const foreignKey = InstanceChecker.isTableForeignKey(foreignKeyOrName)\n            ? foreignKeyOrName\n            : table.foreignKeys.find((fk) => fk.name === foreignKeyOrName)\n        if (!foreignKey)\n            throw new TypeORMError(\n                `Supplied foreign key was not found in table ${table.name}`,\n            )\n\n        const up = this.dropForeignKeySql(table, foreignKey)\n        const down = this.createForeignKeySql(table, foreignKey)\n        await this.executeQueries(up, down)\n        table.removeForeignKey(foreignKey)\n    }\n\n    /**\n     * Drops a foreign keys from the table.\n     */\n    async dropForeignKeys(\n        tableOrName: Table | string,\n        foreignKeys: TableForeignKey[],\n    ): Promise<void> {\n        for (const foreignKey of foreignKeys) {\n            await this.dropForeignKey(tableOrName, foreignKey)\n        }\n    }\n\n    /**\n     * Creates a new index.\n     */\n    async createIndex(\n        tableOrName: Table | string,\n        index: TableIndex,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n\n        // new index may be passed without name. In this case we generate index name manually.\n        if (!index.name) index.name = this.generateIndexName(table, index)\n\n        // CockroachDB stores unique indices and UNIQUE constraints\n        if (index.isUnique) {\n            const unique = new TableUnique({\n                name: index.name,\n                columnNames: index.columnNames,\n            })\n            const up = this.createUniqueConstraintSql(table, unique)\n            // CockroachDB also creates index for UNIQUE constraints.\n            // We can't drop UNIQUE constraint with DROP CONSTRAINT. We must use DROP INDEX ... CASCADE instead.\n            const down = this.dropIndexSql(table, unique)\n            await this.executeQueries(up, down)\n            table.addUniqueConstraint(unique)\n        } else {\n            const up = this.createIndexSql(table, index)\n            const down = this.dropIndexSql(table, index)\n            await this.executeQueries(up, down)\n            table.addIndex(index)\n        }\n    }\n\n    /**\n     * Creates a new indices\n     */\n    async createIndices(\n        tableOrName: Table | string,\n        indices: TableIndex[],\n    ): Promise<void> {\n        for (const index of indices) {\n            await this.createIndex(tableOrName, index)\n        }\n    }\n\n    /**\n     * Drops an index from the table.\n     */\n    async dropIndex(\n        tableOrName: Table | string,\n        indexOrName: TableIndex | string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const index = InstanceChecker.isTableIndex(indexOrName)\n            ? indexOrName\n            : table.indices.find((i) => i.name === indexOrName)\n        if (!index)\n            throw new TypeORMError(\n                `Supplied index ${indexOrName} was not found in table ${table.name}`,\n            )\n\n        // old index may be passed without name. In this case we generate index name manually.\n        if (!index.name) index.name = this.generateIndexName(table, index)\n\n        const up = this.dropIndexSql(table, index)\n        const down = this.createIndexSql(table, index)\n        await this.executeQueries(up, down)\n        table.removeIndex(index)\n    }\n\n    /**\n     * Drops an indices from the table.\n     */\n    async dropIndices(\n        tableOrName: Table | string,\n        indices: TableIndex[],\n    ): Promise<void> {\n        for (const index of indices) {\n            await this.dropIndex(tableOrName, index)\n        }\n    }\n\n    /**\n     * Clears all table contents.\n     * Note: this operation uses SQL's TRUNCATE query which cannot be reverted in transactions.\n     */\n    async clearTable(tableName: string): Promise<void> {\n        await this.query(`TRUNCATE TABLE ${this.escapePath(tableName)}`)\n    }\n\n    /**\n     * Removes all tables from the currently connected database.\n     */\n    async clearDatabase(): Promise<void> {\n        const schemas: string[] = []\n        this.connection.entityMetadatas\n            .filter((metadata) => metadata.schema)\n            .forEach((metadata) => {\n                const isSchemaExist = !!schemas.find(\n                    (schema) => schema === metadata.schema,\n                )\n                if (!isSchemaExist) schemas.push(metadata.schema!)\n            })\n        schemas.push(this.driver.options.schema || \"current_schema()\")\n        const schemaNamesString = schemas\n            .map((name) => {\n                return name === \"current_schema()\" ? name : \"'\" + name + \"'\"\n            })\n            .join(\", \")\n\n        const isAnotherTransactionActive = this.isTransactionActive\n        if (!isAnotherTransactionActive) await this.startTransaction()\n        try {\n            const version = await this.getVersion()\n            const selectViewDropsQuery =\n                `SELECT 'DROP VIEW IF EXISTS \"' || schemaname || '\".\"' || viewname || '\" CASCADE;' as \"query\" ` +\n                `FROM \"pg_views\" WHERE \"schemaname\" IN (${schemaNamesString})`\n            const dropViewQueries: ObjectLiteral[] = await this.query(\n                selectViewDropsQuery,\n            )\n            await Promise.all(\n                dropViewQueries.map((q) => this.query(q[\"query\"])),\n            )\n\n            const selectDropsQuery = `SELECT 'DROP TABLE IF EXISTS \"' || table_schema || '\".\"' || table_name || '\" CASCADE;' as \"query\" FROM \"information_schema\".\"tables\" WHERE \"table_schema\" IN (${schemaNamesString})`\n            const dropQueries: ObjectLiteral[] = await this.query(\n                selectDropsQuery,\n            )\n            await Promise.all(dropQueries.map((q) => this.query(q[\"query\"])))\n\n            const selectSequenceDropsQuery = `SELECT 'DROP SEQUENCE \"' || sequence_schema || '\".\"' || sequence_name || '\";' as \"query\" FROM \"information_schema\".\"sequences\" WHERE \"sequence_schema\" IN (${schemaNamesString})`\n            const sequenceDropQueries: ObjectLiteral[] = await this.query(\n                selectSequenceDropsQuery,\n            )\n            await Promise.all(\n                sequenceDropQueries.map((q) => this.query(q[\"query\"])),\n            )\n\n            // drop enum types. Supported starting from v20.2.19.\n            if (VersionUtils.isGreaterOrEqual(version, \"20.2.19\")) {\n                await this.dropEnumTypes(schemaNamesString)\n            }\n\n            if (!isAnotherTransactionActive) await this.commitTransaction()\n        } catch (error) {\n            try {\n                // we throw original error even if rollback thrown an error\n                if (!isAnotherTransactionActive)\n                    await this.rollbackTransaction()\n            } catch {\n                // no-op\n            }\n            throw error\n        }\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    protected async loadViews(viewNames?: string[]): Promise<View[]> {\n        const hasTable = await this.hasTable(this.getTypeormMetadataTableName())\n        if (!hasTable) {\n            return []\n        }\n\n        if (!viewNames) {\n            viewNames = []\n        }\n\n        const currentDatabase = await this.getCurrentDatabase()\n        const currentSchema = await this.getCurrentSchema()\n\n        const viewsCondition = viewNames\n            .map((viewName) => {\n                const { schema, tableName } =\n                    this.driver.parseTableName(viewName)\n\n                return `(\"t\".\"schema\" = '${\n                    schema || currentSchema\n                }' AND \"t\".\"name\" = '${tableName}')`\n            })\n            .join(\" OR \")\n\n        const query =\n            `SELECT \"t\".*, \"v\".\"check_option\" FROM ${this.escapePath(\n                this.getTypeormMetadataTableName(),\n            )} \"t\" ` +\n            `INNER JOIN \"information_schema\".\"views\" \"v\" ON \"v\".\"table_schema\" = \"t\".\"schema\" AND \"v\".\"table_name\" = \"t\".\"name\" WHERE \"t\".\"type\" = '${\n                MetadataTableType.VIEW\n            }' ${viewsCondition ? `AND (${viewsCondition})` : \"\"}`\n        const dbViews = await this.query(query)\n        return dbViews.map((dbView: any) => {\n            const view = new View()\n            const schema =\n                dbView[\"schema\"] === currentSchema &&\n                !this.driver.options.schema\n                    ? undefined\n                    : dbView[\"schema\"]\n            view.database = currentDatabase\n            view.schema = dbView[\"schema\"]\n            view.name = this.driver.buildTableName(dbView[\"name\"], schema)\n            view.expression = dbView[\"value\"]\n            return view\n        })\n    }\n\n    /**\n     * Loads all tables (with given names) from the database and creates a Table from them.\n     */\n    protected async loadTables(tableNames?: string[]): Promise<Table[]> {\n        // if no tables given then no need to proceed\n        if (tableNames && tableNames.length === 0) {\n            return []\n        }\n\n        const currentSchema = await this.getCurrentSchema()\n        const currentDatabase = await this.getCurrentDatabase()\n\n        const dbTables: { table_schema: string; table_name: string }[] = []\n\n        if (!tableNames) {\n            const tablesSql = `SELECT \"table_schema\", \"table_name\" FROM \"information_schema\".\"tables\"`\n            dbTables.push(...(await this.query(tablesSql)))\n        } else {\n            const tablesCondition = tableNames\n                .map((tableName) => this.driver.parseTableName(tableName))\n                .map(({ schema, tableName }) => {\n                    return `(\"table_schema\" = '${\n                        schema || currentSchema\n                    }' AND \"table_name\" = '${tableName}')`\n                })\n                .join(\" OR \")\n            const tablesSql =\n                `SELECT \"table_schema\", \"table_name\" FROM \"information_schema\".\"tables\" WHERE ` +\n                tablesCondition\n\n            dbTables.push(...(await this.query(tablesSql)))\n        }\n\n        if (dbTables.length === 0) {\n            return []\n        }\n\n        const columnsCondiiton = dbTables\n            .map(({ table_name, table_schema }) => {\n                return `(\"table_schema\" = '${table_schema}' AND \"table_name\" = '${table_name}')`\n            })\n            .join(\" OR \")\n        const columnsSql =\n            `SELECT \"columns\".*, \"attr\".\"attgenerated\" as \"generated_type\", ` +\n            `pg_catalog.col_description(('\"' || table_catalog || '\".\"' || table_schema || '\".\"' || table_name || '\"')::regclass::oid, ordinal_position) as description ` +\n            `FROM \"information_schema\".\"columns\" ` +\n            `LEFT JOIN \"pg_class\" AS \"cls\" ON \"cls\".\"relname\" = \"table_name\" ` +\n            `LEFT JOIN \"pg_namespace\" AS \"ns\" ON \"ns\".\"oid\" = \"cls\".\"relnamespace\" AND \"ns\".\"nspname\" = \"table_schema\" ` +\n            `LEFT JOIN \"pg_attribute\" AS \"attr\" ON \"attr\".\"attrelid\" = \"cls\".\"oid\" AND \"attr\".\"attname\" = \"column_name\" AND \"attr\".\"attnum\" = \"ordinal_position\" ` +\n            `WHERE \"is_hidden\" = 'NO' AND ` +\n            columnsCondiiton\n\n        const constraintsCondition = dbTables\n            .map(({ table_name, table_schema }) => {\n                return `(\"ns\".\"nspname\" = '${table_schema}' AND \"t\".\"relname\" = '${table_name}')`\n            })\n            .join(\" OR \")\n\n        const constraintsSql =\n            `SELECT \"ns\".\"nspname\" AS \"table_schema\", \"t\".\"relname\" AS \"table_name\", \"cnst\".\"conname\" AS \"constraint_name\", ` +\n            `pg_get_constraintdef(\"cnst\".\"oid\") AS \"expression\", ` +\n            `CASE \"cnst\".\"contype\" WHEN 'p' THEN 'PRIMARY' WHEN 'u' THEN 'UNIQUE' WHEN 'c' THEN 'CHECK' WHEN 'x' THEN 'EXCLUDE' END AS \"constraint_type\", \"a\".\"attname\" AS \"column_name\" ` +\n            `FROM \"pg_constraint\" \"cnst\" ` +\n            `INNER JOIN \"pg_class\" \"t\" ON \"t\".\"oid\" = \"cnst\".\"conrelid\" ` +\n            `INNER JOIN \"pg_namespace\" \"ns\" ON \"ns\".\"oid\" = \"cnst\".\"connamespace\" ` +\n            `LEFT JOIN \"pg_attribute\" \"a\" ON \"a\".\"attrelid\" = \"cnst\".\"conrelid\" AND \"a\".\"attnum\" = ANY (\"cnst\".\"conkey\") ` +\n            `WHERE \"t\".\"relkind\" = 'r' AND (${constraintsCondition})`\n\n        const indicesSql =\n            `SELECT \"ns\".\"nspname\" AS \"table_schema\", \"t\".\"relname\" AS \"table_name\", \"i\".\"relname\" AS \"constraint_name\", \"a\".\"attname\" AS \"column_name\", ` +\n            `CASE \"ix\".\"indisunique\" WHEN 't' THEN 'TRUE' ELSE'FALSE' END AS \"is_unique\", pg_get_expr(\"ix\".\"indpred\", \"ix\".\"indrelid\") AS \"condition\", ` +\n            `\"types\".\"typname\" AS \"type_name\" ` +\n            `FROM \"pg_class\" \"t\" ` +\n            `INNER JOIN \"pg_index\" \"ix\" ON \"ix\".\"indrelid\" = \"t\".\"oid\" ` +\n            `INNER JOIN \"pg_attribute\" \"a\" ON \"a\".\"attrelid\" = \"t\".\"oid\"  AND \"a\".\"attnum\" = ANY (\"ix\".\"indkey\") ` +\n            `INNER JOIN \"pg_namespace\" \"ns\" ON \"ns\".\"oid\" = \"t\".\"relnamespace\" ` +\n            `INNER JOIN \"pg_class\" \"i\" ON \"i\".\"oid\" = \"ix\".\"indexrelid\" ` +\n            `INNER JOIN \"pg_type\" \"types\" ON \"types\".\"oid\" = \"a\".\"atttypid\" ` +\n            `LEFT JOIN \"pg_constraint\" \"cnst\" ON \"cnst\".\"conname\" = \"i\".\"relname\" ` +\n            `WHERE \"t\".\"relkind\" = 'r' AND \"cnst\".\"contype\" IS NULL AND (${constraintsCondition})`\n\n        const foreignKeysCondition = dbTables\n            .map(({ table_name, table_schema }) => {\n                return `(\"ns\".\"nspname\" = '${table_schema}' AND \"cl\".\"relname\" = '${table_name}')`\n            })\n            .join(\" OR \")\n        const foreignKeysSql =\n            `SELECT \"con\".\"conname\" AS \"constraint_name\", \"con\".\"nspname\" AS \"table_schema\", \"con\".\"relname\" AS \"table_name\", \"att2\".\"attname\" AS \"column_name\", ` +\n            `\"ns\".\"nspname\" AS \"referenced_table_schema\", \"cl\".\"relname\" AS \"referenced_table_name\", \"att\".\"attname\" AS \"referenced_column_name\", \"con\".\"confdeltype\" AS \"on_delete\", \"con\".\"confupdtype\" AS \"on_update\" ` +\n            `FROM ( ` +\n            `SELECT UNNEST (\"con1\".\"conkey\") AS \"parent\", UNNEST (\"con1\".\"confkey\") AS \"child\", \"con1\".\"confrelid\", \"con1\".\"conrelid\", \"con1\".\"conname\", \"con1\".\"contype\", \"ns\".\"nspname\", \"cl\".\"relname\", ` +\n            `CASE \"con1\".\"confdeltype\" WHEN 'a' THEN 'NO ACTION' WHEN 'r' THEN 'RESTRICT' WHEN 'c' THEN 'CASCADE' WHEN 'n' THEN 'SET NULL' WHEN 'd' THEN 'SET DEFAULT' END as \"confdeltype\", ` +\n            `CASE \"con1\".\"confupdtype\" WHEN 'a' THEN 'NO ACTION' WHEN 'r' THEN 'RESTRICT' WHEN 'c' THEN 'CASCADE' WHEN 'n' THEN 'SET NULL' WHEN 'd' THEN 'SET DEFAULT' END as \"confupdtype\" ` +\n            `FROM \"pg_class\" \"cl\" ` +\n            `INNER JOIN \"pg_namespace\" \"ns\" ON \"cl\".\"relnamespace\" = \"ns\".\"oid\" ` +\n            `INNER JOIN \"pg_constraint\" \"con1\" ON \"con1\".\"conrelid\" = \"cl\".\"oid\" ` +\n            `WHERE \"con1\".\"contype\" = 'f' AND (${foreignKeysCondition}) ` +\n            `) \"con\" ` +\n            `INNER JOIN \"pg_attribute\" \"att\" ON \"att\".\"attrelid\" = \"con\".\"confrelid\" AND \"att\".\"attnum\" = \"con\".\"child\" ` +\n            `INNER JOIN \"pg_class\" \"cl\" ON \"cl\".\"oid\" = \"con\".\"confrelid\" ` +\n            `INNER JOIN \"pg_namespace\" \"ns\" ON \"cl\".\"relnamespace\" = \"ns\".\"oid\" ` +\n            `INNER JOIN \"pg_attribute\" \"att2\" ON \"att2\".\"attrelid\" = \"con\".\"conrelid\" AND \"att2\".\"attnum\" = \"con\".\"parent\"`\n\n        const tableSchemas = dbTables\n            .map((dbTable) => `'${dbTable.table_schema}'`)\n            .join(\", \")\n        const enumsSql =\n            `SELECT \"t\".\"typname\" AS \"name\", string_agg(\"e\".\"enumlabel\", '|') AS \"value\" ` +\n            `FROM \"pg_enum\" \"e\" ` +\n            `INNER JOIN \"pg_type\" \"t\" ON \"t\".\"oid\" = \"e\".\"enumtypid\" ` +\n            `INNER JOIN \"pg_namespace\" \"n\" ON \"n\".\"oid\" = \"t\".\"typnamespace\" ` +\n            `WHERE \"n\".\"nspname\" IN (${tableSchemas}) ` +\n            `GROUP BY \"t\".\"typname\"`\n\n        const [\n            dbColumns,\n            dbConstraints,\n            dbIndices,\n            dbForeignKeys,\n            dbEnums,\n        ]: ObjectLiteral[][] = await Promise.all([\n            this.query(columnsSql),\n            this.query(constraintsSql),\n            this.query(indicesSql),\n            this.query(foreignKeysSql),\n            this.query(enumsSql),\n        ])\n\n        // create tables for loaded tables\n        return Promise.all(\n            dbTables.map(async (dbTable) => {\n                const table = new Table()\n\n                const getSchemaFromKey = (dbObject: any, key: string) => {\n                    return dbObject[key] === currentSchema &&\n                        (!this.driver.options.schema ||\n                            this.driver.options.schema === currentSchema)\n                        ? undefined\n                        : dbObject[key]\n                }\n\n                // We do not need to join schema name, when database is by default.\n                const schema = getSchemaFromKey(dbTable, \"table_schema\")\n                table.database = currentDatabase\n                table.schema = dbTable[\"table_schema\"]\n                table.name = this.driver.buildTableName(\n                    dbTable[\"table_name\"],\n                    schema,\n                )\n\n                // create columns from the loaded columns\n                table.columns = await Promise.all(\n                    dbColumns\n                        .filter(\n                            (dbColumn) =>\n                                dbColumn[\"table_name\"] ===\n                                    dbTable[\"table_name\"] &&\n                                dbColumn[\"table_schema\"] ===\n                                    dbTable[\"table_schema\"],\n                        )\n                        .map(async (dbColumn) => {\n                            const columnConstraints = dbConstraints.filter(\n                                (dbConstraint) => {\n                                    return (\n                                        dbConstraint[\"table_name\"] ===\n                                            dbColumn[\"table_name\"] &&\n                                        dbConstraint[\"table_schema\"] ===\n                                            dbColumn[\"table_schema\"] &&\n                                        dbConstraint[\"column_name\"] ===\n                                            dbColumn[\"column_name\"]\n                                    )\n                                },\n                            )\n\n                            const tableColumn = new TableColumn()\n                            tableColumn.name = dbColumn[\"column_name\"]\n\n                            tableColumn.type =\n                                dbColumn[\"crdb_sql_type\"].toLowerCase()\n                            if (\n                                dbColumn[\"crdb_sql_type\"].indexOf(\"COLLATE\") !==\n                                -1\n                            ) {\n                                tableColumn.collation = dbColumn[\n                                    \"crdb_sql_type\"\n                                ].substr(\n                                    dbColumn[\"crdb_sql_type\"].indexOf(\n                                        \"COLLATE\",\n                                    ) +\n                                        \"COLLATE\".length +\n                                        1,\n                                    dbColumn[\"crdb_sql_type\"].length,\n                                )\n                                tableColumn.type = tableColumn.type.substr(\n                                    0,\n                                    dbColumn[\"crdb_sql_type\"].indexOf(\n                                        \"COLLATE\",\n                                    ) - 1,\n                                )\n                            }\n\n                            if (tableColumn.type.indexOf(\"(\") !== -1)\n                                tableColumn.type = tableColumn.type.substr(\n                                    0,\n                                    tableColumn.type.indexOf(\"(\"),\n                                )\n\n                            if (\n                                tableColumn.type === \"numeric\" ||\n                                tableColumn.type === \"decimal\"\n                            ) {\n                                if (\n                                    dbColumn[\"numeric_precision\"] !== null &&\n                                    !this.isDefaultColumnPrecision(\n                                        table,\n                                        tableColumn,\n                                        dbColumn[\"numeric_precision\"],\n                                    )\n                                ) {\n                                    tableColumn.precision = parseInt(\n                                        dbColumn[\"numeric_precision\"],\n                                    )\n                                } else if (\n                                    dbColumn[\"numeric_scale\"] !== null &&\n                                    !this.isDefaultColumnScale(\n                                        table,\n                                        tableColumn,\n                                        dbColumn[\"numeric_scale\"],\n                                    )\n                                ) {\n                                    tableColumn.precision = undefined\n                                }\n                                if (\n                                    dbColumn[\"numeric_scale\"] !== null &&\n                                    !this.isDefaultColumnScale(\n                                        table,\n                                        tableColumn,\n                                        dbColumn[\"numeric_scale\"],\n                                    )\n                                ) {\n                                    tableColumn.scale = parseInt(\n                                        dbColumn[\"numeric_scale\"],\n                                    )\n                                } else if (\n                                    dbColumn[\"numeric_precision\"] !== null &&\n                                    !this.isDefaultColumnPrecision(\n                                        table,\n                                        tableColumn,\n                                        dbColumn[\"numeric_precision\"],\n                                    )\n                                ) {\n                                    tableColumn.scale = undefined\n                                }\n                            }\n\n                            // docs: https://www.postgresql.org/docs/current/xtypes.html\n                            // When you define a new base type, PostgreSQL automatically provides support for arrays of that type.\n                            // The array type typically has the same name as the base type with the underscore character (_) prepended.\n                            // ----\n                            // so, we must remove this underscore character from enum type name\n                            let udtName = dbColumn[\"udt_name\"]\n                            if (udtName.indexOf(\"_\") === 0) {\n                                udtName = udtName.substr(1, udtName.length)\n                            }\n\n                            const enumType = dbEnums.find((dbEnum) => {\n                                return dbEnum[\"name\"] === udtName\n                            })\n                            if (enumType) {\n                                // check if `enumName` is specified by user\n                                const builtEnumName = this.buildEnumName(\n                                    table,\n                                    tableColumn,\n                                    false,\n                                    true,\n                                )\n                                const enumName =\n                                    builtEnumName !== enumType[\"name\"]\n                                        ? enumType[\"name\"]\n                                        : undefined\n\n                                tableColumn.type = \"enum\"\n                                tableColumn.enum = enumType[\"value\"].split(\"|\")\n                                tableColumn.enumName = enumName\n                            }\n\n                            if (\n                                dbColumn[\"data_type\"].toLowerCase() === \"array\"\n                            ) {\n                                tableColumn.isArray = true\n                                if (!enumType) {\n                                    const type = dbColumn[\"crdb_sql_type\"]\n                                        .replace(\"[]\", \"\")\n                                        .toLowerCase()\n                                    tableColumn.type =\n                                        this.connection.driver.normalizeType({\n                                            type: type,\n                                        })\n                                }\n                            }\n\n                            // check only columns that have length property\n                            if (\n                                this.driver.withLengthColumnTypes.indexOf(\n                                    tableColumn.type as ColumnType,\n                                ) !== -1 &&\n                                dbColumn[\"character_maximum_length\"]\n                            ) {\n                                const length =\n                                    dbColumn[\n                                        \"character_maximum_length\"\n                                    ].toString()\n                                tableColumn.length =\n                                    !this.isDefaultColumnLength(\n                                        table,\n                                        tableColumn,\n                                        length,\n                                    )\n                                        ? length\n                                        : \"\"\n                            }\n                            tableColumn.isNullable =\n                                dbColumn[\"is_nullable\"] === \"YES\"\n\n                            const primaryConstraint = columnConstraints.find(\n                                (constraint) =>\n                                    constraint[\"constraint_type\"] === \"PRIMARY\",\n                            )\n                            if (primaryConstraint) {\n                                tableColumn.isPrimary = true\n                                // find another columns involved in primary key constraint\n                                const anotherPrimaryConstraints =\n                                    dbConstraints.filter(\n                                        (constraint) =>\n                                            constraint[\"table_name\"] ===\n                                                dbColumn[\"table_name\"] &&\n                                            constraint[\"table_schema\"] ===\n                                                dbColumn[\"table_schema\"] &&\n                                            constraint[\"column_name\"] !==\n                                                dbColumn[\"column_name\"] &&\n                                            constraint[\"constraint_type\"] ===\n                                                \"PRIMARY\",\n                                    )\n\n                                // collect all column names\n                                const columnNames =\n                                    anotherPrimaryConstraints.map(\n                                        (constraint) =>\n                                            constraint[\"column_name\"],\n                                    )\n                                columnNames.push(dbColumn[\"column_name\"])\n\n                                // build default primary key constraint name\n                                const pkName =\n                                    this.connection.namingStrategy.primaryKeyName(\n                                        table,\n                                        columnNames,\n                                    )\n\n                                // if primary key has user-defined constraint name, write it in table column\n                                if (\n                                    primaryConstraint[\"constraint_name\"] !==\n                                    pkName\n                                ) {\n                                    tableColumn.primaryKeyConstraintName =\n                                        primaryConstraint[\"constraint_name\"]\n                                }\n                            }\n\n                            const uniqueConstraints = columnConstraints.filter(\n                                (constraint) =>\n                                    constraint[\"constraint_type\"] === \"UNIQUE\",\n                            )\n                            const isConstraintComposite =\n                                uniqueConstraints.every((uniqueConstraint) => {\n                                    return dbConstraints.some(\n                                        (dbConstraint) =>\n                                            dbConstraint[\"constraint_type\"] ===\n                                                \"UNIQUE\" &&\n                                            dbConstraint[\"constraint_name\"] ===\n                                                uniqueConstraint[\n                                                    \"constraint_name\"\n                                                ] &&\n                                            dbConstraint[\"column_name\"] !==\n                                                dbColumn[\"column_name\"],\n                                    )\n                                })\n                            tableColumn.isUnique =\n                                uniqueConstraints.length > 0 &&\n                                !isConstraintComposite\n\n                            if (\n                                dbColumn[\"column_default\"] !== null &&\n                                dbColumn[\"column_default\"] !== undefined\n                            ) {\n                                if (\n                                    dbColumn[\"column_default\"] ===\n                                    \"unique_rowid()\"\n                                ) {\n                                    tableColumn.isGenerated = true\n                                    tableColumn.generationStrategy = \"rowid\"\n                                } else if (\n                                    dbColumn[\"column_default\"].indexOf(\n                                        \"nextval\",\n                                    ) !== -1\n                                ) {\n                                    tableColumn.isGenerated = true\n                                    tableColumn.generationStrategy = \"increment\"\n                                } else if (\n                                    dbColumn[\"column_default\"] ===\n                                    \"gen_random_uuid()\"\n                                ) {\n                                    tableColumn.isGenerated = true\n                                    tableColumn.generationStrategy = \"uuid\"\n                                } else {\n                                    tableColumn.default = dbColumn[\n                                        \"column_default\"\n                                    ].replace(/:::[\\w\\s[\\]\"]+/g, \"\")\n                                    tableColumn.default =\n                                        tableColumn.default.replace(\n                                            /^(-?[\\d.]+)$/,\n                                            \"($1)\",\n                                        )\n\n                                    if (enumType) {\n                                        tableColumn.default =\n                                            tableColumn.default.replace(\n                                                `.${enumType[\"name\"]}`,\n                                                \"\",\n                                            )\n                                    }\n                                }\n                            }\n\n                            if (\n                                (dbColumn[\"is_generated\"] === \"YES\" ||\n                                    dbColumn[\"is_generated\"] === \"ALWAYS\") &&\n                                dbColumn[\"generation_expression\"]\n                            ) {\n                                tableColumn.generatedType =\n                                    dbColumn[\"generated_type\"] === \"s\"\n                                        ? \"STORED\"\n                                        : \"VIRTUAL\"\n                                // We cannot relay on information_schema.columns.generation_expression, because it is formatted different.\n                                const asExpressionQuery =\n                                    this.selectTypeormMetadataSql({\n                                        schema: dbTable[\"table_schema\"],\n                                        table: dbTable[\"table_name\"],\n                                        type: MetadataTableType.GENERATED_COLUMN,\n                                        name: tableColumn.name,\n                                    })\n\n                                const results = await this.query(\n                                    asExpressionQuery.query,\n                                    asExpressionQuery.parameters,\n                                )\n                                if (results[0] && results[0].value) {\n                                    tableColumn.asExpression = results[0].value\n                                } else {\n                                    tableColumn.asExpression = \"\"\n                                }\n                            }\n\n                            tableColumn.comment =\n                                dbColumn[\"description\"] == null\n                                    ? undefined\n                                    : dbColumn[\"description\"]\n                            if (dbColumn[\"character_set_name\"])\n                                tableColumn.charset =\n                                    dbColumn[\"character_set_name\"]\n\n                            if (\n                                tableColumn.type === \"geometry\" ||\n                                tableColumn.type === \"geography\"\n                            ) {\n                                const sql =\n                                    `SELECT * FROM (` +\n                                    `SELECT \"f_table_schema\" \"table_schema\", \"f_table_name\" \"table_name\", ` +\n                                    `\"f_${tableColumn.type}_column\" \"column_name\", \"srid\", \"type\" ` +\n                                    `FROM \"${tableColumn.type}_columns\"` +\n                                    `) AS _ ` +\n                                    `WHERE \"column_name\" = '${dbColumn[\"column_name\"]}' AND ` +\n                                    `\"table_schema\" = '${dbColumn[\"table_schema\"]}' AND ` +\n                                    `\"table_name\" = '${dbColumn[\"table_name\"]}'`\n\n                                const results: ObjectLiteral[] =\n                                    await this.query(sql)\n\n                                if (results.length > 0) {\n                                    tableColumn.spatialFeatureType =\n                                        results[0].type\n                                    tableColumn.srid = results[0].srid\n                                        ? parseInt(results[0].srid)\n                                        : undefined\n                                }\n                            }\n\n                            return tableColumn\n                        }),\n                )\n\n                // find unique constraints of table, group them by constraint name and build TableUnique.\n                const tableUniqueConstraints = OrmUtils.uniq(\n                    dbConstraints.filter((dbConstraint) => {\n                        return (\n                            dbConstraint[\"table_name\"] ===\n                                dbTable[\"table_name\"] &&\n                            dbConstraint[\"table_schema\"] ===\n                                dbTable[\"table_schema\"] &&\n                            dbConstraint[\"constraint_type\"] === \"UNIQUE\"\n                        )\n                    }),\n                    (dbConstraint) => dbConstraint[\"constraint_name\"],\n                )\n\n                table.uniques = tableUniqueConstraints.map((constraint) => {\n                    const uniques = dbConstraints.filter(\n                        (dbC) =>\n                            dbC[\"constraint_name\"] ===\n                            constraint[\"constraint_name\"],\n                    )\n                    return new TableUnique({\n                        name: constraint[\"constraint_name\"],\n                        columnNames: uniques.map((u) => u[\"column_name\"]),\n                    })\n                })\n\n                // find check constraints of table, group them by constraint name and build TableCheck.\n                const tableCheckConstraints = OrmUtils.uniq(\n                    dbConstraints.filter((dbConstraint) => {\n                        return (\n                            dbConstraint[\"table_name\"] ===\n                                dbTable[\"table_name\"] &&\n                            dbConstraint[\"table_schema\"] ===\n                                dbTable[\"table_schema\"] &&\n                            dbConstraint[\"constraint_type\"] === \"CHECK\"\n                        )\n                    }),\n                    (dbConstraint) => dbConstraint[\"constraint_name\"],\n                )\n\n                table.checks = tableCheckConstraints.map((constraint) => {\n                    const checks = dbConstraints.filter(\n                        (dbC) =>\n                            dbC[\"constraint_name\"] ===\n                            constraint[\"constraint_name\"],\n                    )\n                    return new TableCheck({\n                        name: constraint[\"constraint_name\"],\n                        columnNames: checks.map((c) => c[\"column_name\"]),\n                        expression: constraint[\"expression\"].replace(\n                            /^\\s*CHECK\\s*\\((.*)\\)\\s*$/i,\n                            \"$1\",\n                        ),\n                    })\n                })\n\n                // find exclusion constraints of table, group them by constraint name and build TableExclusion.\n                const tableExclusionConstraints = OrmUtils.uniq(\n                    dbConstraints.filter((dbConstraint) => {\n                        return (\n                            dbConstraint[\"table_name\"] ===\n                                dbTable[\"table_name\"] &&\n                            dbConstraint[\"table_schema\"] ===\n                                dbTable[\"table_schema\"] &&\n                            dbConstraint[\"constraint_type\"] === \"EXCLUDE\"\n                        )\n                    }),\n                    (dbConstraint) => dbConstraint[\"constraint_name\"],\n                )\n\n                table.exclusions = tableExclusionConstraints.map(\n                    (constraint) => {\n                        return new TableExclusion({\n                            name: constraint[\"constraint_name\"],\n                            expression: constraint[\"expression\"].substring(8), // trim EXCLUDE from start of expression\n                        })\n                    },\n                )\n\n                // find foreign key constraints of table, group them by constraint name and build TableForeignKey.\n                const tableForeignKeyConstraints = OrmUtils.uniq(\n                    dbForeignKeys.filter((dbForeignKey) => {\n                        return (\n                            dbForeignKey[\"table_name\"] ===\n                                dbTable[\"table_name\"] &&\n                            dbForeignKey[\"table_schema\"] ===\n                                dbTable[\"table_schema\"]\n                        )\n                    }),\n                    (dbForeignKey) => dbForeignKey[\"constraint_name\"],\n                )\n\n                table.foreignKeys = tableForeignKeyConstraints.map(\n                    (dbForeignKey) => {\n                        const foreignKeys = dbForeignKeys.filter(\n                            (dbFk) =>\n                                dbFk[\"constraint_name\"] ===\n                                dbForeignKey[\"constraint_name\"],\n                        )\n\n                        // if referenced table located in currently used schema, we don't need to concat schema name to table name.\n                        const schema = getSchemaFromKey(\n                            dbForeignKey,\n                            \"referenced_table_schema\",\n                        )\n                        const referencedTableName = this.driver.buildTableName(\n                            dbForeignKey[\"referenced_table_name\"],\n                            schema,\n                        )\n\n                        return new TableForeignKey({\n                            name: dbForeignKey[\"constraint_name\"],\n                            columnNames: foreignKeys.map(\n                                (dbFk) => dbFk[\"column_name\"],\n                            ),\n                            referencedSchema:\n                                dbForeignKey[\"referenced_table_schema\"],\n                            referencedTableName: referencedTableName,\n                            referencedColumnNames: foreignKeys.map(\n                                (dbFk) => dbFk[\"referenced_column_name\"],\n                            ),\n                            onDelete: dbForeignKey[\"on_delete\"],\n                            onUpdate: dbForeignKey[\"on_update\"],\n                        })\n                    },\n                )\n\n                // find index constraints of table, group them by constraint name and build TableIndex.\n                const tableIndexConstraints = OrmUtils.uniq(\n                    dbIndices.filter((dbIndex) => {\n                        return (\n                            dbIndex[\"table_name\"] === dbTable[\"table_name\"] &&\n                            dbIndex[\"table_schema\"] === dbTable[\"table_schema\"]\n                        )\n                    }),\n                    (dbIndex) => dbIndex[\"constraint_name\"],\n                )\n\n                table.indices = tableIndexConstraints.map((constraint) => {\n                    const indices = dbIndices.filter(\n                        (index) =>\n                            index[\"constraint_name\"] ===\n                            constraint[\"constraint_name\"],\n                    )\n                    return new TableIndex(<TableIndexOptions>{\n                        table: table,\n                        name: constraint[\"constraint_name\"],\n                        columnNames: indices.map((i) => i[\"column_name\"]),\n                        isUnique: constraint[\"is_unique\"] === \"TRUE\",\n                        where: constraint[\"condition\"],\n                        isSpatial: indices.every(\n                            (i) =>\n                                this.driver.spatialTypes.indexOf(\n                                    i[\"type_name\"],\n                                ) >= 0,\n                        ),\n                        isFulltext: false,\n                    })\n                })\n\n                return table\n            }),\n        )\n    }\n\n    /**\n     * Builds create table sql.\n     */\n    protected createTableSql(table: Table, createForeignKeys?: boolean): Query {\n        const columnDefinitions = table.columns\n            .map((column) => this.buildCreateColumnSql(table, column))\n            .join(\", \")\n        let sql = `CREATE TABLE ${this.escapePath(table)} (${columnDefinitions}`\n\n        table.columns\n            .filter((column) => column.isUnique)\n            .forEach((column) => {\n                const isUniqueExist = table.uniques.some(\n                    (unique) =>\n                        unique.columnNames.length === 1 &&\n                        unique.columnNames[0] === column.name,\n                )\n                if (!isUniqueExist)\n                    table.uniques.push(\n                        new TableUnique({\n                            name: this.connection.namingStrategy.uniqueConstraintName(\n                                table,\n                                [column.name],\n                            ),\n                            columnNames: [column.name],\n                        }),\n                    )\n            })\n\n        table.indices\n            .filter((index) => index.isUnique)\n            .forEach((index) => {\n                table.uniques.push(\n                    new TableUnique({\n                        name: this.connection.namingStrategy.uniqueConstraintName(\n                            table,\n                            index.columnNames,\n                        ),\n                        columnNames: index.columnNames,\n                    }),\n                )\n            })\n\n        if (table.uniques.length > 0) {\n            const uniquesSql = table.uniques\n                .map((unique) => {\n                    const uniqueName = unique.name\n                        ? unique.name\n                        : this.connection.namingStrategy.uniqueConstraintName(\n                              table,\n                              unique.columnNames,\n                          )\n                    const columnNames = unique.columnNames\n                        .map((columnName) => `\"${columnName}\"`)\n                        .join(\", \")\n                    return `CONSTRAINT \"${uniqueName}\" UNIQUE (${columnNames})`\n                })\n                .join(\", \")\n\n            sql += `, ${uniquesSql}`\n        }\n\n        if (table.checks.length > 0) {\n            const checksSql = table.checks\n                .map((check) => {\n                    const checkName = check.name\n                        ? check.name\n                        : this.connection.namingStrategy.checkConstraintName(\n                              table,\n                              check.expression!,\n                          )\n                    return `CONSTRAINT \"${checkName}\" CHECK (${check.expression})`\n                })\n                .join(\", \")\n\n            sql += `, ${checksSql}`\n        }\n\n        if (table.foreignKeys.length > 0 && createForeignKeys) {\n            const foreignKeysSql = table.foreignKeys\n                .map((fk) => {\n                    const columnNames = fk.columnNames\n                        .map((columnName) => `\"${columnName}\"`)\n                        .join(\", \")\n                    if (!fk.name)\n                        fk.name = this.connection.namingStrategy.foreignKeyName(\n                            table,\n                            fk.columnNames,\n                            this.getTablePath(fk),\n                            fk.referencedColumnNames,\n                        )\n                    const referencedColumnNames = fk.referencedColumnNames\n                        .map((columnName) => `\"${columnName}\"`)\n                        .join(\", \")\n\n                    let constraint = `CONSTRAINT \"${\n                        fk.name\n                    }\" FOREIGN KEY (${columnNames}) REFERENCES ${this.escapePath(\n                        this.getTablePath(fk),\n                    )} (${referencedColumnNames})`\n                    if (fk.onDelete) constraint += ` ON DELETE ${fk.onDelete}`\n                    if (fk.onUpdate) constraint += ` ON UPDATE ${fk.onUpdate}`\n\n                    return constraint\n                })\n                .join(\", \")\n\n            sql += `, ${foreignKeysSql}`\n        }\n\n        const primaryColumns = table.columns.filter(\n            (column) => column.isPrimary,\n        )\n        if (primaryColumns.length > 0) {\n            const primaryKeyName = primaryColumns[0].primaryKeyConstraintName\n                ? primaryColumns[0].primaryKeyConstraintName\n                : this.connection.namingStrategy.primaryKeyName(\n                      table,\n                      primaryColumns.map((column) => column.name),\n                  )\n\n            const columnNames = primaryColumns\n                .map((column) => `\"${column.name}\"`)\n                .join(\", \")\n            sql += `, CONSTRAINT \"${primaryKeyName}\" PRIMARY KEY (${columnNames})`\n        }\n\n        sql += `)`\n\n        table.columns\n            .filter((it) => it.comment)\n            .forEach(\n                (it) =>\n                    (sql += `; COMMENT ON COLUMN ${this.escapePath(table)}.\"${\n                        it.name\n                    }\" IS ${this.escapeComment(it.comment)}`),\n            )\n\n        return new Query(sql)\n    }\n\n    /**\n     * Loads Cockroachdb version.\n     */\n    async getVersion(): Promise<string> {\n        const result: [{ version: string }] = await this.query(\n            `SELECT version() AS \"version\"`,\n        )\n        const versionString = result[0].version\n\n        return versionString.replace(/^CockroachDB CCL v([\\d.]+) .*$/, \"$1\")\n    }\n\n    /**\n     * Builds drop table sql.\n     */\n    protected dropTableSql(tableOrPath: Table | string): Query {\n        return new Query(`DROP TABLE ${this.escapePath(tableOrPath)}`)\n    }\n\n    protected createViewSql(view: View): Query {\n        if (typeof view.expression === \"string\") {\n            return new Query(\n                `CREATE VIEW ${this.escapePath(view)} AS ${view.expression}`,\n            )\n        } else {\n            return new Query(\n                `CREATE VIEW ${this.escapePath(view)} AS ${view\n                    .expression(this.connection)\n                    .getQuery()}`,\n            )\n        }\n    }\n\n    protected async insertViewDefinitionSql(view: View): Promise<Query> {\n        const currentSchema = await this.getCurrentSchema()\n        let { schema, tableName: name } = this.driver.parseTableName(view)\n        if (!schema) {\n            schema = currentSchema\n        }\n\n        const expression =\n            typeof view.expression === \"string\"\n                ? view.expression.trim()\n                : view.expression(this.connection).getQuery()\n        return this.insertTypeormMetadataSql({\n            type: MetadataTableType.VIEW,\n            schema: schema,\n            name: name,\n            value: expression,\n        })\n    }\n\n    /**\n     * Builds drop view sql.\n     */\n    protected dropViewSql(viewOrPath: View | string): Query {\n        return new Query(`DROP VIEW ${this.escapePath(viewOrPath)}`)\n    }\n\n    /**\n     * Builds remove view sql.\n     */\n    protected async deleteViewDefinitionSql(\n        viewOrPath: View | string,\n    ): Promise<Query> {\n        const currentSchema = await this.getCurrentSchema()\n\n        let { schema, tableName: name } = this.driver.parseTableName(viewOrPath)\n\n        if (!schema) {\n            schema = currentSchema\n        }\n\n        return this.deleteTypeormMetadataSql({\n            type: MetadataTableType.VIEW,\n            schema,\n            name,\n        })\n    }\n\n    /**\n     * Drops ENUM type from given schemas.\n     */\n    protected async dropEnumTypes(schemaNames: string): Promise<void> {\n        const selectDropsQuery =\n            `SELECT 'DROP TYPE IF EXISTS \"' || n.nspname || '\".\"' || t.typname || '\";' as \"query\" FROM \"pg_type\" \"t\" ` +\n            `INNER JOIN \"pg_enum\" \"e\" ON \"e\".\"enumtypid\" = \"t\".\"oid\" ` +\n            `INNER JOIN \"pg_namespace\" \"n\" ON \"n\".\"oid\" = \"t\".\"typnamespace\" ` +\n            `WHERE \"n\".\"nspname\" IN (${schemaNames}) GROUP BY \"n\".\"nspname\", \"t\".\"typname\"`\n        const dropQueries: ObjectLiteral[] = await this.query(selectDropsQuery)\n        await Promise.all(dropQueries.map((q) => this.query(q[\"query\"])))\n    }\n\n    /**\n     * Checks if enum with the given name exist in the database.\n     */\n    protected async hasEnumType(\n        table: Table,\n        column: TableColumn,\n    ): Promise<boolean> {\n        let { schema } = this.driver.parseTableName(table)\n\n        if (!schema) {\n            schema = await this.getCurrentSchema()\n        }\n\n        const enumName = this.buildEnumName(table, column, false, true)\n        const sql =\n            `SELECT \"n\".\"nspname\", \"t\".\"typname\" FROM \"pg_type\" \"t\" ` +\n            `INNER JOIN \"pg_namespace\" \"n\" ON \"n\".\"oid\" = \"t\".\"typnamespace\" ` +\n            `WHERE \"n\".\"nspname\" = '${schema}' AND \"t\".\"typname\" = '${enumName}'`\n        const result = await this.query(sql)\n        return result.length ? true : false\n    }\n\n    /**\n     * Builds create ENUM type sql.\n     */\n    protected createEnumTypeSql(\n        table: Table,\n        column: TableColumn,\n        enumName?: string,\n    ): Query {\n        if (!enumName) enumName = this.buildEnumName(table, column)\n        const enumValues = column\n            .enum!.map((value) => `'${value.replaceAll(\"'\", \"''\")}'`)\n            .join(\", \")\n        return new Query(`CREATE TYPE ${enumName} AS ENUM(${enumValues})`)\n    }\n\n    /**\n     * Builds create ENUM type sql.\n     */\n    protected dropEnumTypeSql(\n        table: Table,\n        column: TableColumn,\n        enumName?: string,\n    ): Query {\n        if (!enumName) enumName = this.buildEnumName(table, column)\n        return new Query(`DROP TYPE ${enumName}`)\n    }\n\n    /**\n     * Builds create index sql.\n     * UNIQUE indices creates as UNIQUE constraints.\n     */\n    protected createIndexSql(table: Table, index: TableIndex): Query {\n        const columns = index.columnNames\n            .map((columnName) => `\"${columnName}\"`)\n            .join(\", \")\n        return new Query(\n            `CREATE ${index.isUnique ? \"UNIQUE \" : \"\"}INDEX \"${\n                index.name\n            }\" ON ${this.escapePath(table)} ${\n                index.isSpatial ? \"USING GiST \" : \"\"\n            }(${columns}) ${index.where ? \"WHERE \" + index.where : \"\"}`,\n        )\n    }\n\n    /**\n     * Builds drop index sql.\n     */\n    protected dropIndexSql(\n        table: Table,\n        indexOrName: TableIndex | TableUnique | string,\n    ): Query {\n        const indexName =\n            InstanceChecker.isTableIndex(indexOrName) ||\n            InstanceChecker.isTableUnique(indexOrName)\n                ? indexOrName.name\n                : indexOrName\n        return new Query(\n            `DROP INDEX ${this.escapePath(table)}@\"${indexName}\" CASCADE`,\n        )\n    }\n\n    /**\n     * Builds create primary key sql.\n     */\n    protected createPrimaryKeySql(\n        table: Table,\n        columnNames: string[],\n        constraintName?: string,\n    ): Query {\n        const primaryKeyName = constraintName\n            ? constraintName\n            : this.connection.namingStrategy.primaryKeyName(table, columnNames)\n        const columnNamesString = columnNames\n            .map((columnName) => `\"${columnName}\"`)\n            .join(\", \")\n        return new Query(\n            `ALTER TABLE ${this.escapePath(\n                table,\n            )} ADD CONSTRAINT \"${primaryKeyName}\" PRIMARY KEY (${columnNamesString})`,\n        )\n    }\n\n    /**\n     * Builds drop primary key sql.\n     */\n    protected dropPrimaryKeySql(table: Table): Query {\n        if (!table.primaryColumns.length)\n            throw new TypeORMError(`Table ${table} has no primary keys.`)\n\n        const columnNames = table.primaryColumns.map((column) => column.name)\n        const constraintName = table.primaryColumns[0].primaryKeyConstraintName\n        const primaryKeyName = constraintName\n            ? constraintName\n            : this.connection.namingStrategy.primaryKeyName(table, columnNames)\n        return new Query(\n            `ALTER TABLE ${this.escapePath(\n                table,\n            )} DROP CONSTRAINT \"${primaryKeyName}\"`,\n        )\n    }\n\n    /**\n     * Builds create unique constraint sql.\n     */\n    protected createUniqueConstraintSql(\n        table: Table,\n        uniqueConstraint: TableUnique | TableIndex,\n    ): Query {\n        const columnNames = uniqueConstraint.columnNames\n            .map((column) => `\"` + column + `\"`)\n            .join(\", \")\n        return new Query(\n            `ALTER TABLE ${this.escapePath(table)} ADD CONSTRAINT \"${\n                uniqueConstraint.name\n            }\" UNIQUE (${columnNames})`,\n        )\n    }\n\n    /**\n     * Builds drop unique constraint sql.\n     */\n    protected dropUniqueConstraintSql(\n        table: Table,\n        uniqueOrName: TableUnique | string,\n    ): Query {\n        const uniqueName = InstanceChecker.isTableUnique(uniqueOrName)\n            ? uniqueOrName.name\n            : uniqueOrName\n        return new Query(\n            `ALTER TABLE ${this.escapePath(\n                table,\n            )} DROP CONSTRAINT \"${uniqueName}\"`,\n        )\n    }\n\n    /**\n     * Builds create check constraint sql.\n     */\n    protected createCheckConstraintSql(\n        table: Table,\n        checkConstraint: TableCheck,\n    ): Query {\n        return new Query(\n            `ALTER TABLE ${this.escapePath(table)} ADD CONSTRAINT \"${\n                checkConstraint.name\n            }\" CHECK (${checkConstraint.expression})`,\n        )\n    }\n\n    /**\n     * Builds drop check constraint sql.\n     */\n    protected dropCheckConstraintSql(\n        table: Table,\n        checkOrName: TableCheck | string,\n    ): Query {\n        const checkName = InstanceChecker.isTableCheck(checkOrName)\n            ? checkOrName.name\n            : checkOrName\n        return new Query(\n            `ALTER TABLE ${this.escapePath(\n                table,\n            )} DROP CONSTRAINT \"${checkName}\"`,\n        )\n    }\n\n    /**\n     * Builds create foreign key sql.\n     */\n    protected createForeignKeySql(\n        table: Table,\n        foreignKey: TableForeignKey,\n    ): Query {\n        const columnNames = foreignKey.columnNames\n            .map((column) => `\"` + column + `\"`)\n            .join(\", \")\n        const referencedColumnNames = foreignKey.referencedColumnNames\n            .map((column) => `\"` + column + `\"`)\n            .join(\",\")\n        let sql =\n            `ALTER TABLE ${this.escapePath(table)} ADD CONSTRAINT \"${\n                foreignKey.name\n            }\" FOREIGN KEY (${columnNames}) ` +\n            `REFERENCES ${this.escapePath(\n                this.getTablePath(foreignKey),\n            )}(${referencedColumnNames})`\n        if (foreignKey.onDelete) sql += ` ON DELETE ${foreignKey.onDelete}`\n        if (foreignKey.onUpdate) sql += ` ON UPDATE ${foreignKey.onUpdate}`\n\n        return new Query(sql)\n    }\n\n    /**\n     * Builds drop foreign key sql.\n     */\n    protected dropForeignKeySql(\n        table: Table,\n        foreignKeyOrName: TableForeignKey | string,\n    ): Query {\n        const foreignKeyName = InstanceChecker.isTableForeignKey(\n            foreignKeyOrName,\n        )\n            ? foreignKeyOrName.name\n            : foreignKeyOrName\n        return new Query(\n            `ALTER TABLE ${this.escapePath(\n                table,\n            )} DROP CONSTRAINT \"${foreignKeyName}\"`,\n        )\n    }\n\n    /**\n     * Builds sequence name from given table and column.\n     */\n    protected buildSequenceName(\n        table: Table,\n        columnOrName: TableColumn | string,\n    ): string {\n        const { tableName } = this.driver.parseTableName(table)\n\n        const columnName = InstanceChecker.isTableColumn(columnOrName)\n            ? columnOrName.name\n            : columnOrName\n\n        return `${tableName}_${columnName}_seq`\n    }\n\n    protected buildSequencePath(\n        table: Table,\n        columnOrName: TableColumn | string,\n    ): string {\n        const { schema } = this.driver.parseTableName(table)\n\n        return schema\n            ? `${schema}.${this.buildSequenceName(table, columnOrName)}`\n            : this.buildSequenceName(table, columnOrName)\n    }\n\n    /**\n     * Builds ENUM type name from given table and column.\n     */\n    protected buildEnumName(\n        table: Table,\n        column: TableColumn,\n        withSchema: boolean = true,\n        disableEscape?: boolean,\n        toOld?: boolean,\n    ): string {\n        const { schema, tableName } = this.driver.parseTableName(table)\n        let enumName = column.enumName\n            ? column.enumName\n            : `${tableName}_${column.name.toLowerCase()}_enum`\n        if (schema && withSchema) enumName = `${schema}.${enumName}`\n        if (toOld) enumName = enumName + \"_old\"\n        return enumName\n            .split(\".\")\n            .map((i) => {\n                return disableEscape ? i : `\"${i}\"`\n            })\n            .join(\".\")\n    }\n\n    protected async getUserDefinedTypeName(table: Table, column: TableColumn) {\n        let { schema, tableName: name } = this.driver.parseTableName(table)\n\n        if (!schema) {\n            schema = await this.getCurrentSchema()\n        }\n\n        const result = await this.query(\n            `SELECT \"udt_schema\", \"udt_name\" ` +\n                `FROM \"information_schema\".\"columns\" WHERE \"table_schema\" = '${schema}' AND \"table_name\" = '${name}' AND \"column_name\"='${column.name}'`,\n        )\n\n        // docs: https://www.postgresql.org/docs/current/xtypes.html\n        // When you define a new base type, PostgreSQL automatically provides support for arrays of that type.\n        // The array type typically has the same name as the base type with the underscore character (_) prepended.\n        // ----\n        // so, we must remove this underscore character from enum type name\n        let udtName = result[0][\"udt_name\"]\n        if (udtName.indexOf(\"_\") === 0) {\n            udtName = udtName.substr(1, udtName.length)\n        }\n        return {\n            schema: result[0][\"udt_schema\"],\n            name: udtName,\n        }\n    }\n\n    /**\n     * Escapes a given comment so it's safe to include in a query.\n     */\n    protected escapeComment(comment?: string) {\n        if (comment === undefined || comment.length === 0) {\n            return \"NULL\"\n        }\n\n        comment = comment.replace(/'/g, \"''\").replace(/\\u0000/g, \"\") // Null bytes aren't allowed in comments\n\n        return `'${comment}'`\n    }\n\n    /**\n     * Escapes given table or view path.\n     */\n    protected escapePath(target: Table | View | string): string {\n        const { schema, tableName } = this.driver.parseTableName(target)\n\n        if (schema && schema !== this.driver.searchSchema) {\n            return `\"${schema}\".\"${tableName}\"`\n        }\n\n        return `\"${tableName}\"`\n    }\n\n    /**\n     * Builds a query for create column.\n     */\n    protected buildCreateColumnSql(table: Table, column: TableColumn) {\n        let c = '\"' + column.name + '\"'\n\n        if (column.isGenerated) {\n            if (column.generationStrategy === \"increment\") {\n                c += ` INT DEFAULT nextval('${this.escapePath(\n                    this.buildSequencePath(table, column),\n                )}')`\n            } else if (column.generationStrategy === \"rowid\") {\n                c += \" INT DEFAULT unique_rowid()\"\n            } else if (column.generationStrategy === \"uuid\") {\n                c += \" UUID DEFAULT gen_random_uuid()\"\n            }\n        }\n\n        if (column.type === \"enum\" || column.type === \"simple-enum\") {\n            c += \" \" + this.buildEnumName(table, column)\n            if (column.isArray) c += \" array\"\n        } else if (!column.isGenerated) {\n            c += \" \" + this.connection.driver.createFullType(column)\n        }\n\n        if (column.asExpression) {\n            c += ` AS (${column.asExpression}) ${\n                column.generatedType ? column.generatedType : \"VIRTUAL\"\n            }`\n        } else {\n            if (column.charset) c += ' CHARACTER SET \"' + column.charset + '\"'\n            if (column.collation) c += ' COLLATE \"' + column.collation + '\"'\n        }\n\n        if (!column.isNullable) c += \" NOT NULL\"\n        if (\n            !column.isGenerated &&\n            column.default !== undefined &&\n            column.default !== null\n        )\n            c += \" DEFAULT \" + column.default\n\n        return c\n    }\n    /**\n     * Change table comment.\n     */\n    changeTableComment(\n        tableOrName: Table | string,\n        comment?: string,\n    ): Promise<void> {\n        throw new TypeORMError(\n            `cockroachdb driver does not support change table comment.`,\n        )\n    }\n}\n"], "sourceRoot": "../.."}