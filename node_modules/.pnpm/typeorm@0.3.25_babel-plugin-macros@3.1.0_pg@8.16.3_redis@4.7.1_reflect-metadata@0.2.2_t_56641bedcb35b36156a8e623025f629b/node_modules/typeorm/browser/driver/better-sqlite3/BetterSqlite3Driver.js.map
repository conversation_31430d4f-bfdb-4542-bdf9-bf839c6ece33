{"version": 3, "sources": ["../browser/src/driver/better-sqlite3/BetterSqlite3Driver.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,aAAa,CAAA;AAC5B,OAAO,IAAI,MAAM,MAAM,CAAA;AACvB,OAAO,EAAE,8BAA8B,EAAE,MAAM,aAAa,CAAA;AAC5D,OAAO,EAAE,aAAa,EAAE,MAAM,8BAA8B,CAAA;AAI5D,OAAO,EAAE,oBAAoB,EAAE,MAAM,yCAAyC,CAAA;AAE9E,OAAO,EAAE,wBAAwB,EAAE,MAAM,4BAA4B,CAAA;AAErE,OAAO,EAAE,cAAc,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAA;AAEjE;;GAEG;AACH,MAAM,OAAO,mBAAoB,SAAQ,oBAAoB;IAezD,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAY,UAAsB;QAC9B,KAAK,CAAC,UAAU,CAAC,CAAA;QAEjB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAA;QAC5B,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,OAAyC,CAAA;QACnE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAA;QAErC,sBAAsB;QACtB,IAAI,CAAC,gBAAgB,EAAE,CAAA;IAC3B,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,KAAK,CAAC,UAAU;QACZ,IAAI,CAAC,WAAW,GAAG,SAAS,CAAA;QAC5B,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAA;IACnC,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,IAAqB;QACnC,IAAI,CAAC,IAAI,CAAC,WAAW;YACjB,IAAI,CAAC,WAAW,GAAG,IAAI,wBAAwB,CAAC,IAAI,CAAC,CAAA;QAEzD,OAAO,IAAI,CAAC,WAAW,CAAA;IAC3B,CAAC;IAED,aAAa,CAAC,MAKb;QACG,IAAK,MAAM,CAAC,IAAY,KAAK,MAAM,EAAE,CAAC;YAClC,OAAO,MAAM,CAAA;QACjB,CAAC;QAED,OAAO,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;IACtC,CAAC;IAED,KAAK,CAAC,YAAY;QACd,OAAO,IAAI,CAAC,eAAe,EAAE,CAAA;IACjC,CAAC;IAED;;OAEG;IACH,cAAc,CACV,SAAiB,EACjB,OAAgB,EAChB,QAAiB;QAEjB,IAAI,CAAC,QAAQ;YAAE,OAAO,SAAS,CAAA;QAC/B,IAAI,IAAI,CAAC,uCAAuC,CAAC,QAAQ,CAAC;YACtD,OAAO,GAAG,IAAI,CAAC,uCAAuC,CAClD,QAAQ,CACX,IAAI,SAAS,EAAE,CAAA;QAEpB,IAAI,QAAQ,KAAK,IAAI,CAAC,OAAO,CAAC,QAAQ;YAAE,OAAO,SAAS,CAAA;QAExD,iHAAiH;QACjH,MAAM,cAAc,GAAG,cAAc,CAAC,QAAQ,CAAC,CAAA;QAC/C,gIAAgI;QAChI,MAAM,WAAW,GAAG,UAAU,CAAC,QAAQ,CAAC;YACpC,CAAC,CAAC,QAAQ;YACV,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE,QAAQ,CAAC,CAAA;QAErD,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,GAAG;YAC/B,sBAAsB,EAAE,WAAW;YACnC,sBAAsB,EAAE,QAAQ;YAChC,YAAY,EAAE,cAAc;SAC/B,CAAA;QAED,OAAO,GAAG,cAAc,IAAI,SAAS,EAAE,CAAA;IAC3C,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;OAEG;IACO,KAAK,CAAC,wBAAwB;QACpC,mDAAmD;QACnD,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,UAAU;YACpC,MAAM,IAAI,CAAC,uBAAuB,CAC9B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CACtC,CAAA;QAEL,MAAM,EACF,QAAQ,EACR,QAAQ,GAAG,KAAK,EAChB,aAAa,GAAG,KAAK,EACrB,OAAO,GAAG,IAAI,EACd,OAAO,GAAG,IAAI,EACd,aAAa,GAAG,IAAI,EACpB,eAAe,GAClB,GAAG,IAAI,CAAC,OAAO,CAAA;QAChB,MAAM,kBAAkB,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;YAC7C,QAAQ;YACR,aAAa;YACb,OAAO;YACP,OAAO;YACP,aAAa;SAChB,CAAC,CAAA;QACF,6DAA6D;QAC7D,sFAAsF;QACtF,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;YACnB,kBAAkB,CAAC,IAAI,CACnB,gBAAgB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CACrD,CAAA;QACL,CAAC;QAED,wDAAwD;QACxD,IAAI,OAAO,eAAe,KAAK,UAAU,EAAE,CAAC;YACxC,eAAe,CAAC,kBAAkB,CAAC,CAAA;QACvC,CAAC;QAED,yFAAyF;QACzF,kEAAkE;QAClE,kBAAkB,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAA;QAEnD,0CAA0C;QAC1C,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;YACzB,kBAAkB,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAA;QACxD,CAAC;QAED,OAAO,kBAAkB,CAAA;IAC7B,CAAC;IAED;;OAEG;IACO,gBAAgB;QACtB,IAAI,CAAC;YACD,MAAM,MAAM,GACR,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;YAC/D,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACxB,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACT,MAAM,IAAI,8BAA8B,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAA;QACxE,CAAC;IACL,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,uBAAuB,CAAC,MAAc;QAClD,MAAM,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAA;IAC/C,CAAC;IAED;;;;;OAKG;IACO,KAAK,CAAC,eAAe;QAC3B,kIAAkI;QAClI,IAAI,KAAK,EAAE,MAAM,EACb,YAAY,EACZ,sBAAsB,GACzB,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC;YACzC,MAAM,IAAI,CAAC,uBAAuB,CAC9B,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,CACvC,CAAA;YACD,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CACvB,WAAW,sBAAsB,SAAS,YAAY,GAAG,CAC5D,CAAA;QACL,CAAC;IACL,CAAC;IAES,mBAAmB;QACzB,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAA;QACvC,OAAO,IAAI,CAAC,OAAO,CACf,UAAU,CAAC,SAAS,CAAC;YACjB,CAAC,CAAC,SAAS;YACX,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAc,EAAE,SAAS,CAAC,CAC1D,CAAA;IACL,CAAC;CACJ", "file": "BetterSqlite3Driver.js", "sourcesContent": ["import fs from \"fs/promises\"\nimport path from \"path\"\nimport { DriverPackageNotInstalledError } from \"../../error\"\nimport { PlatformTools } from \"../../platform/PlatformTools\"\nimport { DataSource } from \"../../data-source\"\nimport { ColumnType } from \"../types/ColumnTypes\"\nimport { QueryRunner } from \"../../query-runner/QueryRunner\"\nimport { AbstractSqliteDriver } from \"../sqlite-abstract/AbstractSqliteDriver\"\nimport { BetterSqlite3ConnectionOptions } from \"./BetterSqlite3ConnectionOptions\"\nimport { BetterSqlite3QueryRunner } from \"./BetterSqlite3QueryRunner\"\nimport { ReplicationMode } from \"../types/ReplicationMode\"\nimport { filepathToName, isAbsolute } from \"../../util/PathUtils\"\n\n/**\n * Organizes communication with sqlite DBMS.\n */\nexport class BetterSqlite3Driver extends AbstractSqliteDriver {\n    // -------------------------------------------------------------------------\n    // Public Implemented Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Connection options.\n     */\n    options: BetterSqlite3ConnectionOptions\n\n    /**\n     * SQLite underlying library.\n     */\n    sqlite: any\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(connection: DataSource) {\n        super(connection)\n\n        this.connection = connection\n        this.options = connection.options as BetterSqlite3ConnectionOptions\n        this.database = this.options.database\n\n        // load sqlite package\n        this.loadDependencies()\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Closes connection with database.\n     */\n    async disconnect(): Promise<void> {\n        this.queryRunner = undefined\n        this.databaseConnection.close()\n    }\n\n    /**\n     * Creates a query runner used to execute database queries.\n     */\n    createQueryRunner(mode: ReplicationMode): QueryRunner {\n        if (!this.queryRunner)\n            this.queryRunner = new BetterSqlite3QueryRunner(this)\n\n        return this.queryRunner\n    }\n\n    normalizeType(column: {\n        type?: ColumnType\n        length?: number | string\n        precision?: number | null\n        scale?: number\n    }): string {\n        if ((column.type as any) === Buffer) {\n            return \"blob\"\n        }\n\n        return super.normalizeType(column)\n    }\n\n    async afterConnect(): Promise<void> {\n        return this.attachDatabases()\n    }\n\n    /**\n     * For SQLite, the database may be added in the decorator metadata. It will be a filepath to a database file.\n     */\n    buildTableName(\n        tableName: string,\n        _schema?: string,\n        database?: string,\n    ): string {\n        if (!database) return tableName\n        if (this.getAttachedDatabaseHandleByRelativePath(database))\n            return `${this.getAttachedDatabaseHandleByRelativePath(\n                database,\n            )}.${tableName}`\n\n        if (database === this.options.database) return tableName\n\n        // we use the decorated name as supplied when deriving attach handle (ideally without non-portable absolute path)\n        const identifierHash = filepathToName(database)\n        // decorated name will be assumed relative to main database file when non absolute. Paths supplied as absolute won't be portable\n        const absFilepath = isAbsolute(database)\n            ? database\n            : path.join(this.getMainDatabasePath(), database)\n\n        this.attachedDatabases[database] = {\n            attachFilepathAbsolute: absFilepath,\n            attachFilepathRelative: database,\n            attachHandle: identifierHash,\n        }\n\n        return `${identifierHash}.${tableName}`\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates connection with the database.\n     */\n    protected async createDatabaseConnection() {\n        // not to create database directory if is in memory\n        if (this.options.database !== \":memory:\")\n            await this.createDatabaseDirectory(\n                path.dirname(this.options.database),\n            )\n\n        const {\n            database,\n            readonly = false,\n            fileMustExist = false,\n            timeout = 5000,\n            verbose = null,\n            nativeBinding = null,\n            prepareDatabase,\n        } = this.options\n        const databaseConnection = this.sqlite(database, {\n            readonly,\n            fileMustExist,\n            timeout,\n            verbose,\n            nativeBinding,\n        })\n        // in the options, if encryption key for SQLCipher is setted.\n        // Must invoke key pragma before trying to do any other interaction with the database.\n        if (this.options.key) {\n            databaseConnection.exec(\n                `PRAGMA key = ${JSON.stringify(this.options.key)}`,\n            )\n        }\n\n        // function to run before a database is used in typeorm.\n        if (typeof prepareDatabase === \"function\") {\n            prepareDatabase(databaseConnection)\n        }\n\n        // we need to enable foreign keys in sqlite to make sure all foreign key related features\n        // working properly. this also makes onDelete to work with sqlite.\n        databaseConnection.exec(`PRAGMA foreign_keys = ON`)\n\n        // turn on WAL mode to enhance performance\n        if (this.options.enableWAL) {\n            databaseConnection.exec(`PRAGMA journal_mode = WAL`)\n        }\n\n        return databaseConnection\n    }\n\n    /**\n     * If driver dependency is not given explicitly, then try to load it via \"require\".\n     */\n    protected loadDependencies(): void {\n        try {\n            const sqlite =\n                this.options.driver || PlatformTools.load(\"better-sqlite3\")\n            this.sqlite = sqlite\n        } catch (e) {\n            throw new DriverPackageNotInstalledError(\"SQLite\", \"better-sqlite3\")\n        }\n    }\n\n    /**\n     * Auto creates database directory if it does not exist.\n     */\n    protected async createDatabaseDirectory(dbPath: string): Promise<void> {\n        await fs.mkdir(dbPath, { recursive: true })\n    }\n\n    /**\n     * Performs the attaching of the database files. The attachedDatabase should have been populated during calls to #buildTableName\n     * during EntityMetadata production (see EntityMetadata#buildTablePath)\n     *\n     * https://sqlite.org/lang_attach.html\n     */\n    protected async attachDatabases() {\n        // @todo - possibly check number of databases (but unqueriable at runtime sadly) - https://www.sqlite.org/limits.html#max_attached\n        for await (const {\n            attachHandle,\n            attachFilepathAbsolute,\n        } of Object.values(this.attachedDatabases)) {\n            await this.createDatabaseDirectory(\n                path.dirname(attachFilepathAbsolute),\n            )\n            await this.connection.query(\n                `ATTACH \"${attachFilepathAbsolute}\" AS \"${attachHandle}\"`,\n            )\n        }\n    }\n\n    protected getMainDatabasePath(): string {\n        const optionsDb = this.options.database\n        return path.dirname(\n            isAbsolute(optionsDb)\n                ? optionsDb\n                : path.join(this.options.baseDirectory!, optionsDb),\n        )\n    }\n}\n"], "sourceRoot": "../.."}