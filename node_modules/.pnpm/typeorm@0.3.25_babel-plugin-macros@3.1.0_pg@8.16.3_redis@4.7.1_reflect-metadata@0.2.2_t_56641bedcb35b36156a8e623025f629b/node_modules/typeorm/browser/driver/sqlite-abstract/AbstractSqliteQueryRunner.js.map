{"version": 3, "sources": ["../browser/src/driver/sqlite-abstract/AbstractSqliteQueryRunner.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,0BAA0B,EAAE,MAAM,wCAAwC,CAAA;AACnF,OAAO,EAAE,WAAW,EAAE,MAAM,wCAAwC,CAAA;AACpE,OAAO,EAAE,KAAK,EAAE,MAAM,kCAAkC,CAAA;AACxD,OAAO,EAAE,UAAU,EAAE,MAAM,uCAAuC,CAAA;AAClE,OAAO,EAAE,eAAe,EAAE,MAAM,4CAA4C,CAAA;AAC5E,OAAO,EAAE,IAAI,EAAE,MAAM,gCAAgC,CAAA;AACrD,OAAO,EAAE,KAAK,EAAE,MAAM,UAAU,CAAA;AAIhC,OAAO,EAAE,WAAW,EAAE,MAAM,wCAAwC,CAAA;AACpE,OAAO,EAAE,eAAe,EAAE,MAAM,oCAAoC,CAAA;AACpE,OAAO,EAAE,QAAQ,EAAE,MAAM,qBAAqB,CAAA;AAC9C,OAAO,EAAE,UAAU,EAAE,MAAM,uCAAuC,CAAA;AAGlE,OAAO,EAAE,8BAA8B,EAAE,YAAY,EAAE,MAAM,aAAa,CAAA;AAC1E,OAAO,EAAE,iBAAiB,EAAE,MAAM,4BAA4B,CAAA;AAC9D,OAAO,EAAE,eAAe,EAAE,MAAM,4BAA4B,CAAA;AAE5D;;GAEG;AACH,MAAM,OAAgB,yBAClB,SAAQ,eAAe;IAcvB,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E;QACI,KAAK,EAAE,CAAA;QAPD,uBAAkB,GAAwB,IAAI,CAAA;IAQxD,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;;OAGG;IACH,OAAO;QACH,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAA;IAC1D,CAAC;IAED;;;OAGG;IACH,OAAO;QACH,IAAI,CAAC,YAAY,GAAG,EAAE,CAAA;QACtB,IAAI,CAAC,cAAc,EAAE,CAAA;QACrB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAAC,cAA+B;QAClD,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB,KAAK,MAAM;YACzC,MAAM,IAAI,YAAY,CAClB,oCAAoC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,GAAG,CAC7E,CAAA;QAEL,IACI,IAAI,CAAC,mBAAmB;YACxB,IAAI,CAAC,MAAM,CAAC,kBAAkB,KAAK,QAAQ;YAE3C,MAAM,IAAI,8BAA8B,EAAE,CAAA;QAE9C,IACI,cAAc;YACd,cAAc,KAAK,kBAAkB;YACrC,cAAc,KAAK,cAAc;YAEjC,MAAM,IAAI,YAAY,CAClB,kEAAkE,CACrE,CAAA;QAEL,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAA;QAC/B,IAAI,CAAC;YACD,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAA;QAC9D,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAA;YAChC,MAAM,GAAG,CAAA;QACb,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,KAAK,CAAC,EAAE,CAAC;YAC9B,IAAI,cAAc,EAAE,CAAC;gBACjB,IAAI,cAAc,KAAK,kBAAkB,EAAE,CAAC;oBACxC,MAAM,IAAI,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAA;gBACtD,CAAC;qBAAM,CAAC;oBACJ,MAAM,IAAI,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAA;gBACvD,CAAC;YACL,CAAC;YACD,MAAM,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAA;QACzC,CAAC;aAAM,CAAC;YACJ,MAAM,IAAI,CAAC,KAAK,CAAC,qBAAqB,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAA;QAClE,CAAC;QACD,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAA;QAE1B,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAA;IAC7D,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,iBAAiB;QACnB,IAAI,CAAC,IAAI,CAAC,mBAAmB;YAAE,MAAM,IAAI,0BAA0B,EAAE,CAAA;QAErE,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAA;QAE3D,IAAI,IAAI,CAAC,gBAAgB,GAAG,CAAC,EAAE,CAAC;YAC5B,MAAM,IAAI,CAAC,KAAK,CACZ,6BAA6B,IAAI,CAAC,gBAAgB,GAAG,CAAC,EAAE,CAC3D,CAAA;QACL,CAAC;aAAM,CAAC;YACJ,MAAM,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;YAC1B,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAA;QACpC,CAAC;QACD,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAA;QAE1B,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAA;IAC9D,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,mBAAmB;QACrB,IAAI,CAAC,IAAI,CAAC,mBAAmB;YAAE,MAAM,IAAI,0BAA0B,EAAE,CAAA;QAErE,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAA;QAE7D,IAAI,IAAI,CAAC,gBAAgB,GAAG,CAAC,EAAE,CAAC;YAC5B,MAAM,IAAI,CAAC,KAAK,CACZ,iCAAiC,IAAI,CAAC,gBAAgB,GAAG,CAAC,EAAE,CAC/D,CAAA;QACL,CAAC;aAAM,CAAC;YACJ,MAAM,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAA;YAC5B,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAA;QACpC,CAAC;QACD,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAA;QAE1B,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAA;IAChE,CAAC;IAED;;OAEG;IACH,MAAM,CACF,KAAa,EACb,UAAkB,EAClB,KAAgB,EAChB,OAAkB;QAElB,MAAM,IAAI,YAAY,CAAC,2CAA2C,CAAC,CAAA;IACvE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY;QACd,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAA;IAC9B,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,UAAU,CAAC,QAAiB;QAC9B,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAA;IAC9B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,QAAgB;QAC9B,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IACjC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB;QACpB,OAAO,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA;IACrC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CAAC,MAAc;QAC1B,MAAM,IAAI,YAAY,CAAC,4CAA4C,CAAC,CAAA;IACxE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB;QAClB,OAAO,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA;IACrC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ,CAAC,WAA2B;QACtC,MAAM,SAAS,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAClD,CAAC,CAAC,WAAW,CAAC,IAAI;YAClB,CAAC,CAAC,WAAW,CAAA;QACjB,MAAM,GAAG,GAAG,sEAAsE,SAAS,GAAG,CAAA;QAC9F,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QACpC,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAA;IACvC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CACX,WAA2B,EAC3B,UAAkB;QAElB,MAAM,SAAS,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAClD,CAAC,CAAC,WAAW,CAAC,IAAI;YAClB,CAAC,CAAC,WAAW,CAAA;QACjB,MAAM,GAAG,GAAG,sBAAsB,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,GAAG,CAAA;QAC/D,MAAM,OAAO,GAAoB,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QACtD,OAAO,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,UAAU,CAAC,CAAA;IACpE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAChB,QAAgB,EAChB,UAAoB;QAEpB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,QAAgB,EAAE,OAAiB;QAClD,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CACd,UAAkB,EAClB,UAAoB;QAEpB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,UAAkB,EAAE,OAAiB;QAClD,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACb,KAAY,EACZ,aAAsB,KAAK,EAC3B,oBAA6B,IAAI,EACjC,gBAAyB,IAAI;QAE7B,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAE/B,IAAI,UAAU,EAAE,CAAC;YACb,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;YAC/C,IAAI,YAAY;gBAAE,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;QAC9C,CAAC;QAED,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,iBAAiB,CAAC,CAAC,CAAA;QAC7D,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAA;QAE1C,IAAI,aAAa,EAAE,CAAC;YAChB,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;gBAC5B,sFAAsF;gBACtF,IAAI,CAAC,KAAK,CAAC,IAAI;oBACX,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CACjD,KAAK,EACL,KAAK,CAAC,WAAW,EACjB,KAAK,CAAC,KAAK,CACd,CAAA;gBACL,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAA;gBACjD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAA;YAC9C,CAAC,CAAC,CAAA;QACN,CAAC;QAED,6FAA6F;QAC7F,MAAM,gBAAgB,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CACzC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,aAAa,IAAI,MAAM,CAAC,YAAY,CAC1D,CAAA;QAED,KAAK,MAAM,MAAM,IAAI,gBAAgB,EAAE,CAAC;YACpC,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,KAAK,EAAE,KAAK,CAAC,IAAI;gBACjB,IAAI,EAAE,iBAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,KAAK,EAAE,MAAM,CAAC,YAAY;aAC7B,CAAC,CAAA;YAEF,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,KAAK,EAAE,KAAK,CAAC,IAAI;gBACjB,IAAI,EAAE,iBAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;aACpB,CAAC,CAAA;YAEF,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YAC3B,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QACjC,CAAC;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;IACrD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CACX,WAA2B,EAC3B,OAAiB,EACjB,kBAA2B,IAAI,EAC/B,cAAuB,IAAI;QAE3B,IAAI,OAAO,EAAE,CAAC;YACV,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAA;YACrD,IAAI,CAAC,YAAY;gBAAE,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;QAC/C,CAAC;QAED,8FAA8F;QAC9F,MAAM,iBAAiB,GAAY,eAAe,CAAA;QAClD,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAE/B,IAAI,WAAW,EAAE,CAAC;YACd,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;gBAC5B,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAA;gBACxC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAA;YACvD,CAAC,CAAC,CAAA;QACN,CAAC;QAED,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAA;QACjD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,iBAAiB,CAAC,CAAC,CAAA;QAE/D,kGAAkG;QAClG,MAAM,gBAAgB,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CACzC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,aAAa,IAAI,MAAM,CAAC,YAAY,CAC1D,CAAA;QAED,KAAK,MAAM,MAAM,IAAI,gBAAgB,EAAE,CAAC;YACpC,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,KAAK,EAAE,KAAK,CAAC,IAAI;gBACjB,IAAI,EAAE,iBAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;aACpB,CAAC,CAAA;YAEF,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,KAAK,EAAE,KAAK,CAAC,IAAI;gBACjB,IAAI,EAAE,iBAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,KAAK,EAAE,MAAM,CAAC,YAAY;aAC7B,CAAC,CAAA;YAEF,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YAC3B,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QACjC,CAAC;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;IACrD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CACZ,IAAU,EACV,mBAA4B,KAAK;QAEjC,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAC/B,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAA;QACxC,IAAI,gBAAgB;YAAE,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAA;QACxE,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAA;QACxC,IAAI,gBAAgB;YAChB,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAA;QACxD,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;IACrD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ,CAAC,MAAqB;QAChC,MAAM,QAAQ,GAAG,eAAe,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAA;QACtE,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAA;QAE/C,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAC/B,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAA;QAClD,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAA;QACtC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAA;QACpD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAA;QAC1C,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;IACrD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACb,cAA8B,EAC9B,YAAoB;QAEpB,MAAM,QAAQ,GAAG,eAAe,CAAC,OAAO,CAAC,cAAc,CAAC;YACpD,CAAC,CAAC,cAAc;YAChB,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAA;QAC/C,MAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAA;QAEjC,QAAQ,CAAC,IAAI,GAAG,YAAY,CAAA;QAE5B,eAAe;QACf,MAAM,EAAE,GAAG,IAAI,KAAK,CAChB,eAAe,IAAI,CAAC,UAAU,CAC1B,QAAQ,CAAC,IAAI,CAChB,cAAc,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CACjD,CAAA;QACD,MAAM,IAAI,GAAG,IAAI,KAAK,CAClB,eAAe,IAAI,CAAC,UAAU,CAC1B,YAAY,CACf,cAAc,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAClD,CAAA;QACD,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QAEnC,4BAA4B;QAC5B,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YAChC,MAAM,aAAa,GACf,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CAC/C,QAAQ,EACR,MAAM,CAAC,WAAW,CACrB,CAAA;YAEL,2DAA2D;YAC3D,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa;gBAAE,OAAM;YAEzC,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CAC7D,QAAQ,EACR,MAAM,CAAC,WAAW,CACrB,CAAA;QACL,CAAC,CAAC,CAAA;QAEF,iCAAiC;QACjC,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;YACxC,MAAM,iBAAiB,GACnB,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,QAAQ,EACR,UAAU,CAAC,WAAW,EACtB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAC7B,UAAU,CAAC,qBAAqB,CACnC,CAAA;YAEL,gEAAgE;YAChE,IAAI,UAAU,CAAC,IAAI,KAAK,iBAAiB;gBAAE,OAAM;YAEjD,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CAC3D,QAAQ,EACR,UAAU,CAAC,WAAW,EACtB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAC7B,UAAU,CAAC,qBAAqB,CACnC,CAAA;QACL,CAAC,CAAC,CAAA;QAEF,iBAAiB;QACjB,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAC/B,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CACzD,QAAQ,EACR,KAAK,CAAC,WAAW,EACjB,KAAK,CAAC,KAAK,CACd,CAAA;YAED,0DAA0D;YAC1D,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY;gBAAE,OAAM;YAEvC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CACjD,QAAQ,EACR,KAAK,CAAC,WAAW,EACjB,KAAK,CAAC,KAAK,CACd,CAAA;QACL,CAAC,CAAC,CAAA;QAEF,oBAAoB;QACpB,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAA;QAE7B,2CAA2C;QAC3C,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAA;IAChD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CACX,WAA2B,EAC3B,MAAmB;QAEnB,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,OAAO,IAAI,CAAC,UAAU,CAAC,KAAM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAA;IAC5C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CACZ,WAA2B,EAC3B,OAAsB;QAEtB,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,YAAY,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QAClC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAA;QAC3D,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,KAAK,CAAC,CAAA;IACjD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CACd,WAA2B,EAC3B,oBAA0C,EAC1C,oBAA0C;QAE1C,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,SAAS,GAAG,eAAe,CAAC,aAAa,CAAC,oBAAoB,CAAC;YACjE,CAAC,CAAC,oBAAoB;YACtB,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,oBAAoB,CAAC,CAAA;QAChE,IAAI,CAAC,SAAS;YACV,MAAM,IAAI,YAAY,CAClB,WAAW,oBAAoB,2BAA2B,KAAK,CAAC,IAAI,UAAU,CACjF,CAAA;QAEL,IAAI,SAAS,GAA4B,SAAS,CAAA;QAClD,IAAI,eAAe,CAAC,aAAa,CAAC,oBAAoB,CAAC,EAAE,CAAC;YACtD,SAAS,GAAG,oBAAoB,CAAA;QACpC,CAAC;aAAM,CAAC;YACJ,SAAS,GAAG,SAAS,CAAC,KAAK,EAAE,CAAA;YAC7B,SAAS,CAAC,IAAI,GAAG,oBAAoB,CAAA;QACzC,CAAC;QAED,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,SAAS,EAAE,SAAS,CAAC,CAAA;IACzD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CACd,WAA2B,EAC3B,oBAA0C,EAC1C,SAAsB;QAEtB,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,SAAS,GAAG,eAAe,CAAC,aAAa,CAAC,oBAAoB,CAAC;YACjE,CAAC,CAAC,oBAAoB;YACtB,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,oBAAoB,CAAC,CAAA;QAChE,IAAI,CAAC,SAAS;YACV,MAAM,IAAI,YAAY,CAClB,WAAW,oBAAoB,2BAA2B,KAAK,CAAC,IAAI,UAAU,CACjF,CAAA;QAEL,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC,CAAC,CAAA;IAC/D,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,aAAa,CACf,WAA2B,EAC3B,cAAoE;QAEpE,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,YAAY,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QAClC,cAAc,CAAC,OAAO,CAAC,CAAC,gBAAgB,EAAE,EAAE;YACxC,IACI,gBAAgB,CAAC,SAAS,CAAC,IAAI;gBAC/B,gBAAgB,CAAC,SAAS,CAAC,IAAI,EACjC,CAAC;gBACC,YAAY;qBACP,iBAAiB,CAAC,gBAAgB,CAAC,SAAS,CAAC;qBAC7C,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;oBAChB,MAAM,UAAU,GACZ,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CAC/C,KAAK,EACL,MAAM,CAAC,WAAW,CACrB,CAAA;oBAEL,MAAM,CAAC,WAAW,CAAC,MAAM,CACrB,MAAM,CAAC,WAAW,CAAC,OAAO,CACtB,gBAAgB,CAAC,SAAS,CAAC,IAAI,CAClC,EACD,CAAC,CACJ,CAAA;oBACD,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;oBAExD,uDAAuD;oBACvD,IAAI,MAAM,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;wBAC7B,MAAM,CAAC,IAAI;4BACP,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CAC/C,YAAY,EACZ,MAAM,CAAC,WAAW,CACrB,CAAA;oBACT,CAAC;gBACL,CAAC,CAAC,CAAA;gBAEN,YAAY;qBACP,qBAAqB,CAAC,gBAAgB,CAAC,SAAS,CAAC;qBACjD,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;oBACpB,MAAM,cAAc,GAChB,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,KAAK,EACL,UAAU,CAAC,WAAW,EACtB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAC7B,UAAU,CAAC,qBAAqB,CACnC,CAAA;oBAEL,UAAU,CAAC,WAAW,CAAC,MAAM,CACzB,UAAU,CAAC,WAAW,CAAC,OAAO,CAC1B,gBAAgB,CAAC,SAAS,CAAC,IAAI,CAClC,EACD,CAAC,CACJ,CAAA;oBACD,UAAU,CAAC,WAAW,CAAC,IAAI,CACvB,gBAAgB,CAAC,SAAS,CAAC,IAAI,CAClC,CAAA;oBAED,mDAAmD;oBACnD,IAAI,UAAU,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;wBACrC,UAAU,CAAC,IAAI;4BACX,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,YAAY,EACZ,UAAU,CAAC,WAAW,EACtB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAC7B,UAAU,CAAC,qBAAqB,CACnC,CAAA;oBACT,CAAC;gBACL,CAAC,CAAC,CAAA;gBAEN,YAAY;qBACP,iBAAiB,CAAC,gBAAgB,CAAC,SAAS,CAAC;qBAC7C,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;oBACf,MAAM,SAAS,GACX,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CACpC,KAAK,EACL,KAAK,CAAC,WAAW,EACjB,KAAK,CAAC,KAAK,CACd,CAAA;oBAEL,KAAK,CAAC,WAAW,CAAC,MAAM,CACpB,KAAK,CAAC,WAAW,CAAC,OAAO,CACrB,gBAAgB,CAAC,SAAS,CAAC,IAAI,CAClC,EACD,CAAC,CACJ,CAAA;oBACD,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;oBAEvD,sDAAsD;oBACtD,IAAI,KAAK,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;wBAC3B,KAAK,CAAC,IAAI;4BACN,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CACpC,YAAY,EACZ,KAAK,CAAC,WAAW,EACjB,KAAK,CAAC,KAAK,CACd,CAAA;oBACT,CAAC;gBACL,CAAC,CAAC,CAAA;YACV,CAAC;YACD,MAAM,cAAc,GAAG,YAAY,CAAC,OAAO,CAAC,IAAI,CAC5C,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,gBAAgB,CAAC,SAAS,CAAC,IAAI,CAC9D,CAAA;YACD,IAAI,cAAc;gBACd,YAAY,CAAC,OAAO,CAChB,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,CAC/C,GAAG,gBAAgB,CAAC,SAAS,CAAA;QACtC,CAAC,CAAC,CAAA;QAEF,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,KAAK,CAAC,CAAA;IACjD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CACZ,WAA2B,EAC3B,YAAkC;QAElC,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,MAAM,GAAG,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YACtD,CAAC,CAAC,YAAY;YACd,CAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAA;QAC1C,IAAI,CAAC,MAAM;YACP,MAAM,IAAI,YAAY,CAClB,WAAW,YAAY,6BAA6B,KAAK,CAAC,IAAI,GAAG,CACpE,CAAA;QAEL,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC,MAAM,CAAC,CAAC,CAAA;IAC3C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACb,WAA2B,EAC3B,OAAiC;QAEjC,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAE5C,+EAA+E;QAC/E,MAAM,YAAY,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QAClC,OAAO,CAAC,OAAO,CAAC,CAAC,MAA4B,EAAE,EAAE;YAC7C,MAAM,cAAc,GAAG,eAAe,CAAC,aAAa,CAAC,MAAM,CAAC;gBACxD,CAAC,CAAC,MAAM;gBACR,CAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAA;YACpC,IAAI,CAAC,cAAc;gBACf,MAAM,IAAI,KAAK,CACX,WAAW,MAAM,6BAA6B,KAAK,CAAC,IAAI,GAAG,CAC9D,CAAA;YAEL,YAAY,CAAC,YAAY,CAAC,cAAc,CAAC,CAAA;YACzC,YAAY;iBACP,iBAAiB,CAAC,cAAc,CAAC;iBACjC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE,CAChB,YAAY,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAC9C,CAAA;YACL,YAAY;iBACP,iBAAiB,CAAC,cAAc,CAAC;iBACjC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAA;YACxD,YAAY;iBACP,qBAAqB,CAAC,cAAc,CAAC;iBACrC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,YAAY,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC,CAAA;QAC3D,CAAC,CAAC,CAAA;QAEF,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,KAAK,CAAC,CAAA;IACjD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAClB,WAA2B,EAC3B,WAAqB;QAErB,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,mDAAmD;QACnD,MAAM,YAAY,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QAClC,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YACpC,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,KAAK,MAAM,CAAC,IAAI,CAAC;gBAC5D,MAAM,CAAC,SAAS,GAAG,IAAI,CAAA;QAC/B,CAAC,CAAC,CAAA;QAEF,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,KAAK,CAAC,CAAA;QAC7C,4CAA4C;QAC5C,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YAC7B,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,KAAK,MAAM,CAAC,IAAI,CAAC;gBAC5D,MAAM,CAAC,SAAS,GAAG,IAAI,CAAA;QAC/B,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CACnB,WAA2B,EAC3B,OAAsB;QAEtB,MAAM,OAAO,CAAC,OAAO,EAAE,CAAA;IAC3B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,WAA2B;QAC5C,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,+DAA+D;QAC/D,MAAM,YAAY,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QAClC,YAAY,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YAC3C,MAAM,CAAC,SAAS,GAAG,KAAK,CAAA;QAC5B,CAAC,CAAC,CAAA;QAEF,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,KAAK,CAAC,CAAA;QAC7C,wDAAwD;QACxD,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YACpC,MAAM,CAAC,SAAS,GAAG,KAAK,CAAA;QAC5B,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,sBAAsB,CACxB,WAA2B,EAC3B,gBAA6B;QAE7B,MAAM,IAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAA;IACvE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB,CACzB,WAA2B,EAC3B,iBAAgC;QAEhC,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAE5C,qEAAqE;QACrE,MAAM,YAAY,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QAClC,iBAAiB,CAAC,OAAO,CAAC,CAAC,gBAAgB,EAAE,EAAE,CAC3C,YAAY,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,CACrD,CAAA;QACD,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,KAAK,CAAC,CAAA;IACjD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CACtB,WAA2B,EAC3B,YAAkC;QAElC,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,gBAAgB,GAAG,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChE,CAAC,CAAC,YAAY;YACd,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,YAAY,CAAC,CAAA;QACxD,IAAI,CAAC,gBAAgB;YACjB,MAAM,IAAI,YAAY,CAClB,qDAAqD,KAAK,CAAC,IAAI,EAAE,CACpE,CAAA;QAEL,MAAM,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAA;IAC/D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,qBAAqB,CACvB,WAA2B,EAC3B,iBAAgC;QAEhC,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAE5C,uEAAuE;QACvE,MAAM,YAAY,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QAClC,iBAAiB,CAAC,OAAO,CAAC,CAAC,gBAAgB,EAAE,EAAE,CAC3C,YAAY,CAAC,sBAAsB,CAAC,gBAAgB,CAAC,CACxD,CAAA;QAED,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,KAAK,CAAC,CAAA;IACjD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,qBAAqB,CACvB,WAA2B,EAC3B,eAA2B;QAE3B,MAAM,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE,CAAC,eAAe,CAAC,CAAC,CAAA;IACrE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,sBAAsB,CACxB,WAA2B,EAC3B,gBAA8B;QAE9B,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAE5C,oEAAoE;QACpE,MAAM,YAAY,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QAClC,gBAAgB,CAAC,OAAO,CAAC,CAAC,eAAe,EAAE,EAAE,CACzC,YAAY,CAAC,kBAAkB,CAAC,eAAe,CAAC,CACnD,CAAA;QACD,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,KAAK,CAAC,CAAA;IACjD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CACrB,WAA2B,EAC3B,WAAgC;QAEhC,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,eAAe,GAAG,eAAe,CAAC,YAAY,CAAC,WAAW,CAAC;YAC7D,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,CAAA;QACtD,IAAI,CAAC,eAAe;YAChB,MAAM,IAAI,YAAY,CAClB,oDAAoD,KAAK,CAAC,IAAI,EAAE,CACnE,CAAA;QAEL,MAAM,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC,eAAe,CAAC,CAAC,CAAA;IAC7D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CACtB,WAA2B,EAC3B,gBAA8B;QAE9B,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAE5C,sEAAsE;QACtE,MAAM,YAAY,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QAClC,gBAAgB,CAAC,OAAO,CAAC,CAAC,eAAe,EAAE,EAAE,CACzC,YAAY,CAAC,qBAAqB,CAAC,eAAe,CAAC,CACtD,CAAA;QAED,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,KAAK,CAAC,CAAA;IACjD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,yBAAyB,CAC3B,WAA2B,EAC3B,mBAAmC;QAEnC,MAAM,IAAI,YAAY,CAAC,gDAAgD,CAAC,CAAA;IAC5E,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,0BAA0B,CAC5B,WAA2B,EAC3B,oBAAsC;QAEtC,MAAM,IAAI,YAAY,CAAC,gDAAgD,CAAC,CAAA;IAC5E,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB,CACzB,WAA2B,EAC3B,eAAwC;QAExC,MAAM,IAAI,YAAY,CAAC,gDAAgD,CAAC,CAAA;IAC5E,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,wBAAwB,CAC1B,WAA2B,EAC3B,oBAAsC;QAEtC,MAAM,IAAI,YAAY,CAAC,gDAAgD,CAAC,CAAA;IAC5E,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAClB,WAA2B,EAC3B,UAA2B;QAE3B,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,CAAC,CAAA;IAC3D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CACnB,WAA2B,EAC3B,WAA8B;QAE9B,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,+DAA+D;QAC/D,MAAM,YAAY,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QAClC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE,CAC/B,YAAY,CAAC,aAAa,CAAC,UAAU,CAAC,CACzC,CAAA;QAED,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,KAAK,CAAC,CAAA;IACjD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAChB,WAA2B,EAC3B,gBAA0C;QAE1C,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,UAAU,GAAG,eAAe,CAAC,iBAAiB,CAAC,gBAAgB,CAAC;YAClE,CAAC,CAAC,gBAAgB;YAClB,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,KAAK,gBAAgB,CAAC,CAAA;QAClE,IAAI,CAAC,UAAU;YACX,MAAM,IAAI,YAAY,CAClB,+CAA+C,KAAK,CAAC,IAAI,EAAE,CAC9D,CAAA;QAEL,MAAM,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,CAAC,CAAA;IACzD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CACjB,WAA2B,EAC3B,WAA8B;QAE9B,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAE5C,iEAAiE;QACjE,MAAM,YAAY,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QAClC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE,CAC/B,YAAY,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAC5C,CAAA;QAED,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,KAAK,CAAC,CAAA;IACjD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACb,WAA2B,EAC3B,KAAiB;QAEjB,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAE5C,sFAAsF;QACtF,IAAI,CAAC,KAAK,CAAC,IAAI;YAAE,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAElE,MAAM,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAC5C,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAA;QACrC,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;IACzB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CACf,WAA2B,EAC3B,OAAqB;QAErB,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CACnC,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,CAAC,CACvC,CAAA;QACD,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IAC/B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CACX,WAA2B,EAC3B,WAAgC;QAEhC,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,KAAK,GAAG,eAAe,CAAC,YAAY,CAAC,WAAW,CAAC;YACnD,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,CAAA;QACvD,IAAI,CAAC,KAAK;YACN,MAAM,IAAI,YAAY,CAClB,kBAAkB,WAAW,2BAA2B,KAAK,CAAC,IAAI,EAAE,CACvE,CAAA;QAEL,sFAAsF;QACtF,IAAI,CAAC,KAAK,CAAC,IAAI;YAAE,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAElE,MAAM,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAA;QACnC,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAC9C,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACb,WAA2B,EAC3B,OAAqB;QAErB,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CACnC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,KAAK,CAAC,CACrC,CAAA;QACD,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IAC/B,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,UAAU,CAAC,SAAiB;QAC9B,MAAM,IAAI,CAAC,KAAK,CAAC,eAAe,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC,CAAA;IACjE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CAAC,QAAiB;QACjC,IAAI,MAAM,GAAuB,SAAS,CAAA;QAC1C,IACI,QAAQ;YACR,IAAI,CAAC,MAAM,CAAC,uCAAuC,CAAC,QAAQ,CAAC,EAC/D,CAAC;YACC,MAAM;gBACF,IAAI,CAAC,MAAM,CAAC,uCAAuC,CAAC,QAAQ,CAAC,CAAA;QACrE,CAAC;QAED,MAAM,IAAI,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAA;QAE7C,MAAM,0BAA0B,GAAG,IAAI,CAAC,mBAAmB,CAAA;QAC3D,IAAI,CAAC,0BAA0B;YAAE,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;QAC9D,IAAI,CAAC;YACD,MAAM,oBAAoB,GAAG,MAAM;gBAC/B,CAAC,CAAC,sBAAsB,MAAM,uCAAuC,MAAM,yCAAyC;gBACpH,CAAC,CAAC,0FAA0F,CAAA;YAChG,MAAM,eAAe,GAAoB,MAAM,IAAI,CAAC,KAAK,CACrD,oBAAoB,CACvB,CAAA;YACD,MAAM,OAAO,CAAC,GAAG,CACb,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CACrD,CAAA;YAED,MAAM,qBAAqB,GAAG,MAAM;gBAChC,CAAC,CAAC,uBAAuB,MAAM,uCAAuC,MAAM,0EAA0E;gBACtJ,CAAC,CAAC,4HAA4H,CAAA;YAClI,MAAM,gBAAgB,GAAoB,MAAM,IAAI,CAAC,KAAK,CACtD,qBAAqB,CACxB,CAAA;YACD,MAAM,OAAO,CAAC,GAAG,CACb,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CACtD,CAAA;YAED,IAAI,CAAC,0BAA0B;gBAAE,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAA;QACnE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC;gBACD,2DAA2D;gBAC3D,IAAI,CAAC,0BAA0B;oBAC3B,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAA;YACxC,CAAC;YAAC,OAAO,aAAa,EAAE,CAAC,CAAA,CAAC;YAC1B,MAAM,KAAK,CAAA;QACf,CAAC;gBAAS,CAAC;YACP,MAAM,IAAI,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAA;QAChD,CAAC;IACL,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAElE,KAAK,CAAC,SAAS,CAAC,SAAoB;QAC1C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,2BAA2B,EAAE,CAAC,CAAA;QACxE,IAAI,CAAC,QAAQ,EAAE,CAAC;YACZ,OAAO,EAAE,CAAA;QACb,CAAC;QAED,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,SAAS,GAAG,EAAE,CAAA;QAClB,CAAC;QAED,MAAM,eAAe,GAAG,SAAS;aAC5B,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,GAAG,GAAG,CAAC;aAC/B,IAAI,CAAC,IAAI,CAAC,CAAA;QACf,IAAI,KAAK,GAAG,sBAAsB,IAAI,CAAC,2BAA2B,EAAE,6GAChE,iBAAiB,CAAC,IACtB,GAAG,CAAA;QACH,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC;YAC1B,KAAK,IAAI,uBAAuB,eAAe,GAAG,CAAA;QACtD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;QACvC,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,MAAW,EAAE,EAAE;YAC/B,MAAM,IAAI,GAAG,IAAI,IAAI,EAAE,CAAA;YACvB,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAA;YAC1B,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,CAAA;YACjC,OAAO,IAAI,CAAA;QACf,CAAC,CAAC,CAAA;IACN,CAAC;IAES,KAAK,CAAC,gBAAgB,CAC5B,SAAiB,EACjB,YAA+B;QAE/B,IAAI,QAAQ,GAAuB,SAAS,CAAA;QAC5C,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAA;QAC1D,IACI,MAAM;YACN,IAAI,CAAC,MAAM,CAAC,uCAAuC,CAAC,MAAM,CAAC,EAC7D,CAAC;YACC,QAAQ;gBACJ,IAAI,CAAC,MAAM,CAAC,uCAAuC,CAAC,MAAM,CAAC,CAAA;QACnE,CAAC;QACD,OAAO,IAAI,CAAC,KAAK,CACb,UAAU,QAAQ,CAAC,CAAC,CAAC,IAAI,QAAQ,GAAG,CAAC,CAAC,CAAC,IAAI,iBACvC,MAAM,CAAC,CAAC,CAAC,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,IAC7B,sBACI,MAAM,CAAC,CAAC,CAAC,IAAI,MAAM,IAAI,CAAC,CAAC,CAAC,EAC9B,GAAG,IAAI,CAAC,UAAU,CACd,eAAe,CAClB,oBAAoB,YAAY,UAC7B,YAAY,KAAK,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,UACxC,UAAU,SAAS,IAAI,CAC1B,CAAA;IACL,CAAC;IAES,KAAK,CAAC,iBAAiB,CAAC,SAAiB,EAAE,MAAc;QAC/D,MAAM,CAAC,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAA;QACpD,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU,MAAM,KAAK,SAAS,IAAI,CAAC,CAAA;IACzD,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,UAAU,CAAC,UAAqB;QAC5C,6CAA6C;QAC7C,IAAI,UAAU,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxC,OAAO,EAAE,CAAA;QACb,CAAC;QAED,IAAI,QAAQ,GAAuD,EAAE,CAAA;QACrE,IAAI,YAA6B,CAAA;QAEjC,IAAI,CAAC,UAAU,EAAE,CAAC;YACd,MAAM,SAAS,GAAG,sDAAsD,CAAA;YACxE,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;YAE/C,MAAM,gBAAgB,GAAG,QAAQ;iBAC5B,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,IAAI,GAAG,CAAC;iBAC9B,IAAI,CAAC,IAAI,CAAC,CAAA;YACf,YAAY,GAAG,MAAM,IAAI,CAAC,KAAK,CAC3B,2EAA2E,gBAAgB,GAAG,CACjG,CAAA;QACL,CAAC;aAAM,CAAC;YACJ,MAAM,oBAAoB,GAAG,UAAU;iBAClC,MAAM,CAAC,CAAC,SAAS,EAAE,EAAE;gBAClB,OAAO,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,KAAK,CAAC,CAAA;YAC5C,CAAC,CAAC;iBACD,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,SAAS,GAAG,CAAC,CAAA;YAEzC,MAAM,iBAAiB,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,EAAE;gBACtD,OAAO,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,CAAA;YAC1C,CAAC,CAAC,CAAA;YAEF,MAAM,aAAa,GAAG,CAAC,IAAuB,EAAE,EAAE;gBAC9C,MAAM,QAAQ,GAAG;oBACb,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CACnC,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,CACzC;iBACJ,CAAA;gBAED,IAAI,oBAAoB,CAAC,MAAM,EAAE,CAAC;oBAC9B,QAAQ,CAAC,IAAI,CACT,IAAI,CAAC,KAAK,CACN,iDAAiD,IAAI,UACjD,IAAI,KAAK,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,UAChC,SAAS,oBAAoB,GAAG,CACnC,CACJ,CAAA;gBACL,CAAC;gBAED,OAAO,QAAQ,CAAA;YACnB,CAAC,CAAA;YACD,QAAQ,GAAG,CAAC,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC;iBACjD,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,GAAG,EAAE,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC;iBAC1C,MAAM,CAAC,OAAO,CAAC,CAAA;YACpB,YAAY,GAAG,CAAC,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC;iBACrD,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,GAAG,EAAE,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC;iBAC1C,MAAM,CAAC,OAAO,CAAC,CAAA;QACxB,CAAC;QAED,yDAAyD;QACzD,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxB,OAAO,EAAE,CAAA;QACb,CAAC;QAED,yCAAyC;QACzC,OAAO,OAAO,CAAC,GAAG,CACd,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YAC3B,MAAM,SAAS,GACX,OAAO,CAAC,UAAU,CAAC;gBACnB,IAAI,CAAC,MAAM,CAAC,uCAAuC,CAC/C,OAAO,CAAC,UAAU,CAAC,CACtB;gBACG,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,uCAAuC,CAClD,OAAO,CAAC,UAAU,CAAC,CACtB,IAAI,OAAO,CAAC,MAAM,CAAC,EAAE;gBACxB,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;YAEzB,MAAM,GAAG,GAAG,OAAO,CAAC,KAAK,CAAC,CAAA;YAE1B,MAAM,YAAY,GAAG,GAAG,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAA;YAClD,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,YAAY,EAAE,CAAC,CAAA;YAE1D,2BAA2B;YAC3B,MAAM,CAAC,SAAS,EAAE,SAAS,EAAE,aAAa,CAAC,GACvC,MAAM,OAAO,CAAC,GAAG,CAAC;gBACd,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,aAAa,CAAC;gBAChD,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,YAAY,CAAC;gBAC/C,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,kBAAkB,CAAC;aACxD,CAAC,CAAA;YAEN,uCAAuC;YACvC,IAAI,uBAAuB,GAAuB,SAAS,CAAA;YAC3D,MAAM,QAAQ,GAAW,OAAO,CAAC,KAAK,CAAC,CAAA;YACvC,MAAM,kBAAkB,GAAG,QAAQ;iBAC9B,WAAW,EAAE;iBACb,OAAO,CAAC,eAAe,CAAC,CAAA;YAC7B,IAAI,kBAAkB,KAAK,CAAC,CAAC,EAAE,CAAC;gBAC5B,uBAAuB,GAAG,QAAQ,CAAC,MAAM,CACrC,CAAC,EACD,kBAAkB,CACrB,CAAA;gBACD,MAAM,KAAK,GAAG,uBAAuB,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA;gBACtD,MAAM,OAAO,GAAG,uBAAuB,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA;gBACxD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;oBACf,uBAAuB;wBACnB,uBAAuB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;oBACzC,uBAAuB;wBACnB,uBAAuB,CAAC,MAAM,CAC1B,CAAC,EACD,uBAAuB,CAAC,WAAW,CAAC,GAAG,CAAC,CAC3C,CAAA;oBACL,uBAAuB;wBACnB,uBAAuB,CAAC,MAAM,CAC1B,uBAAuB,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAC3C,CAAA;gBACT,CAAC;qBAAM,IAAI,OAAO,KAAK,CAAC,CAAC,EAAE,CAAC;oBACxB,uBAAuB;wBACnB,uBAAuB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;oBAC3C,uBAAuB;wBACnB,uBAAuB,CAAC,MAAM,CAC1B,CAAC,EACD,uBAAuB,CAAC,WAAW,CAAC,GAAG,CAAC,CAC3C,CAAA;oBACL,uBAAuB;wBACnB,uBAAuB,CAAC,MAAM,CAC1B,uBAAuB,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAC3C,CAAA;gBACT,CAAC;YACL,CAAC;YAED,yCAAyC;YACzC,KAAK,CAAC,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAC7B,SAAS,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE;gBAC7B,MAAM,WAAW,GAAG,IAAI,WAAW,EAAE,CAAA;gBACrC,WAAW,CAAC,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAA;gBACnC,WAAW,CAAC,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAA;gBACjD,WAAW,CAAC,OAAO;oBACf,QAAQ,CAAC,YAAY,CAAC,KAAK,IAAI;wBAC/B,QAAQ,CAAC,YAAY,CAAC,KAAK,SAAS;wBAChC,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAC;wBACxB,CAAC,CAAC,SAAS,CAAA;gBACnB,WAAW,CAAC,UAAU,GAAG,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;gBAClD,gGAAgG;gBAChG,WAAW,CAAC,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;gBAC1C,WAAW,CAAC,OAAO,GAAG,EAAE,CAAA,CAAC,0CAA0C;gBACnE,WAAW,CAAC,WAAW;oBACnB,uBAAuB,KAAK,QAAQ,CAAC,MAAM,CAAC,CAAA;gBAChD,IAAI,WAAW,CAAC,WAAW,EAAE,CAAC;oBAC1B,WAAW,CAAC,kBAAkB,GAAG,WAAW,CAAA;gBAChD,CAAC;gBAED,IACI,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC;oBACxB,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,EAC1B,CAAC;oBACC,WAAW,CAAC,aAAa;wBACrB,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAA;oBAEnD,MAAM,iBAAiB,GACnB,IAAI,CAAC,wBAAwB,CAAC;wBAC1B,KAAK,EAAE,KAAK,CAAC,IAAI;wBACjB,IAAI,EAAE,iBAAiB,CAAC,gBAAgB;wBACxC,IAAI,EAAE,WAAW,CAAC,IAAI;qBACzB,CAAC,CAAA;oBAEN,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,KAAK,CAC5B,iBAAiB,CAAC,KAAK,EACvB,iBAAiB,CAAC,UAAU,CAC/B,CAAA;oBACD,IAAI,OAAO,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;wBACjC,WAAW,CAAC,YAAY,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAA;oBAC/C,CAAC;yBAAM,CAAC;wBACJ,WAAW,CAAC,YAAY,GAAG,EAAE,CAAA;oBACjC,CAAC;gBACL,CAAC;gBAED,IAAI,WAAW,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;oBACjC,WAAW,CAAC,IAAI,GAAG,QAAQ,CAAC,uBAAuB,CAC/C,GAAG,EACH,WAAW,CAAC,IAAI,CACnB,CAAA;gBACL,CAAC;gBAED,qEAAqE;gBACrE,MAAM,GAAG,GAAG,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA;gBACzC,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC;oBACb,MAAM,QAAQ,GAAG,WAAW,CAAC,IAAI,CAAA;oBACjC,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAA;oBACxC,IACI,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAClC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,KAAK,QAAQ,CAC5B,EACH,CAAC;wBACC,MAAM,GAAG,GAAG,QAAQ,CAChB,QAAQ,CAAC,SAAS,CACd,GAAG,GAAG,CAAC,EACP,QAAQ,CAAC,MAAM,GAAG,CAAC,CACtB,CACJ,CAAA;wBACD,IAAI,GAAG,EAAE,CAAC;4BACN,WAAW,CAAC,MAAM,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAA;4BACnC,WAAW,CAAC,IAAI,GAAG,QAAQ,CAAA,CAAC,2CAA2C;wBAC3E,CAAC;oBACL,CAAC;oBACD,IACI,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,IAAI,CACrC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,KAAK,QAAQ,CAC5B,EACH,CAAC;wBACC,MAAM,EAAE,GAAG,IAAI,MAAM,CACjB,IAAI,QAAQ,2BAA2B,CAC1C,CAAA;wBACD,MAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;wBAClC,IAAI,OAAO,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;4BACxB,WAAW,CAAC,SAAS,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;wBACvC,CAAC;wBACD,IACI,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,IAAI,CACjC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,KAAK,QAAQ,CAC5B,EACH,CAAC;4BACC,IAAI,OAAO,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;gCACxB,WAAW,CAAC,KAAK,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;4BACnC,CAAC;wBACL,CAAC;wBACD,WAAW,CAAC,IAAI,GAAG,QAAQ,CAAA,CAAC,oDAAoD;oBACpF,CAAC;gBACL,CAAC;gBAED,OAAO,WAAW,CAAA;YACtB,CAAC,CAAC,CACL,CAAA;YAED,qDAAqD;YACrD,IAAI,QAAQ,CAAA;YACZ,MAAM,UAAU,GAIV,EAAE,CAAA;YACR,MAAM,OAAO,GACT,mEAAmE,CAAA;YACvE,OAAO,CAAC,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;gBAC7C,UAAU,CAAC,IAAI,CAAC;oBACZ,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;oBACjB,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC;yBACf,MAAM,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;yBACjC,KAAK,CAAC,MAAM,CAAC;oBAClB,mBAAmB,EAAE,QAAQ,CAAC,CAAC,CAAC;iBACnC,CAAC,CAAA;YACN,CAAC;YAED,qBAAqB;YACrB,MAAM,0BAA0B,GAAG,QAAQ,CAAC,IAAI,CAC5C,aAAa,EACb,CAAC,YAAY,EAAE,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,CACvC,CAAA;YAED,KAAK,CAAC,WAAW,GAAG,0BAA0B,CAAC,GAAG,CAC9C,CAAC,UAAU,EAAE,EAAE;gBACX,MAAM,cAAc,GAAG,aAAa,CAAC,MAAM,CACvC,CAAC,YAAY,EAAE,EAAE,CACb,YAAY,CAAC,IAAI,CAAC,KAAK,UAAU,CAAC,IAAI,CAAC;oBACvC,YAAY,CAAC,OAAO,CAAC,KAAK,UAAU,CAAC,OAAO,CAAC,CACpD,CAAA;gBACD,MAAM,WAAW,GAAG,cAAc,CAAC,GAAG,CAClC,CAAC,YAAY,EAAE,EAAE,CAAC,YAAY,CAAC,MAAM,CAAC,CACzC,CAAA;gBACD,MAAM,qBAAqB,GAAG,cAAc,CAAC,GAAG,CAC5C,CAAC,YAAY,EAAE,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,CACvC,CAAA;gBAED,mCAAmC;gBACnC,MAAM,SAAS,GAAG,UAAU,CAAC,IAAI,CAC7B,CAAC,EAAE,EAAE,EAAE,CACH,EAAE,CAAC,mBAAmB;oBAClB,UAAU,CAAC,OAAO,CAAC;oBACvB,EAAE,CAAC,OAAO,CAAC,KAAK,CACZ,CAAC,MAAM,EAAE,EAAE,CACP,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CACzC,CACR,CAAA;gBAED,OAAO,IAAI,eAAe,CAAC;oBACvB,IAAI,EAAE,SAAS,EAAE,IAAI;oBACrB,WAAW,EAAE,WAAW;oBACxB,mBAAmB,EAAE,UAAU,CAAC,OAAO,CAAC;oBACxC,qBAAqB,EAAE,qBAAqB;oBAC5C,QAAQ,EAAE,UAAU,CAAC,WAAW,CAAC;oBACjC,QAAQ,EAAE,UAAU,CAAC,WAAW,CAAC;iBACpC,CAAC,CAAA;YACN,CAAC,CACJ,CAAA;YAED,gDAAgD;YAChD,IAAI,iBAAiB,CAAA;YACrB,MAAM,cAAc,GAA0C,EAAE,CAAA;YAChE,MAAM,WAAW,GAAG,yCAAyC,CAAA;YAC7D,OAAO,CAAC,iBAAiB,GAAG,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;gBAC1D,cAAc,CAAC,IAAI,CAAC;oBAChB,IAAI,EAAE,iBAAiB,CAAC,CAAC,CAAC;oBAC1B,OAAO,EAAE,iBAAiB,CAAC,CAAC,CAAC;yBACxB,MAAM,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;yBAC1C,KAAK,CAAC,MAAM,CAAC;iBACrB,CAAC,CAAA;YACN,CAAC;YAED,2BAA2B;YAC3B,MAAM,mBAAmB,GAAG,SAAS;iBAChC,MAAM,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,GAAG,CAAC;iBAC9C,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;iBACjC,MAAM,CACH,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,KAAK,CACxD;iBACA,GAAG,CAAC,KAAK,EAAE,WAAW,EAAE,EAAE;gBACvB,MAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAC1B,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,WAAW,CAC/C,CAAA;gBACD,MAAM,UAAU,GAAoB,MAAM,IAAI,CAAC,KAAK,CAChD,sBAAsB,OAAQ,CAAC,MAAM,CAAC,IAAI,CAC7C,CAAA;gBACD,MAAM,YAAY,GAAG,UAAU;qBAC1B,IAAI,CACD,CAAC,UAAU,EAAE,UAAU,EAAE,EAAE,CACvB,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;oBAC7B,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CACpC;qBACA,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAA;gBAC1C,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAC5B,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;wBACzC,OAAO,CAAC,CAAC,YAAY,CAAC,IAAI,CACtB,CAAC,WAAW,EAAE,EAAE,CACZ,WAAW,KAAK,MAAM,CAAC,IAAI,CAClC,CAAA;oBACL,CAAC,CAAC,CAAA;oBACF,IAAI,MAAM;wBAAE,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAA;gBACtC,CAAC;gBAED,0CAA0C;gBAC1C,MAAM,YAAY,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE;oBACjD,OAAO,OAAQ,CAAC,OAAO,CAAC,KAAK,CACzB,CAAC,MAAM,EAAE,EAAE,CAAC,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAClD,CAAA;gBACL,CAAC,CAAC,CAAA;gBAEF,OAAO,IAAI,WAAW,CAAC;oBACnB,IAAI,EAAE,YAAY;wBACd,CAAC,CAAC,YAAY,CAAC,IAAI;wBACnB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CAC/C,KAAK,EACL,YAAY,CACf;oBACP,WAAW,EAAE,YAAY;iBAC5B,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;YACN,KAAK,CAAC,OAAO,GAAG,CAAC,MAAM,OAAO,CAAC,GAAG,CAC9B,mBAAmB,CACtB,CAAkB,CAAA;YAEnB,eAAe;YACf,IAAI,MAAM,CAAA;YACV,MAAM,MAAM,GACR,kDAAkD,CAAA;YACtD,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;gBAC1C,KAAK,CAAC,MAAM,CAAC,IAAI,CACb,IAAI,UAAU,CAAC;oBACX,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;oBACf,UAAU,EAAE,MAAM,CAAC,CAAC,CAAC;iBACxB,CAAC,CACL,CAAA;YACL,CAAC;YAED,gBAAgB;YAChB,MAAM,eAAe,GAAG,SAAS;iBAC5B,MAAM,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,GAAG,CAAC;iBAC9C,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;iBACjC,MAAM,CACH,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,KAAK,CACxD,CAAC,SAAS;iBACV,GAAG,CAAC,KAAK,EAAE,WAAW,EAAE,EAAE;gBACvB,MAAM,QAAQ,GAAG,YAAY,CAAC,IAAI,CAC9B,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,WAAW,CACrD,CAAA;gBACD,MAAM,SAAS,GAAG,YAAY,CAAC,IAAI,CAAC,QAAS,CAAC,KAAK,CAAC,CAAC,CAAA;gBACrD,MAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAC1B,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,WAAW,CAC/C,CAAA;gBACD,MAAM,UAAU,GAAoB,MAAM,IAAI,CAAC,KAAK,CAChD,sBAAsB,OAAQ,CAAC,MAAM,CAAC,IAAI,CAC7C,CAAA;gBACD,MAAM,YAAY,GAAG,UAAU;qBAC1B,IAAI,CACD,CAAC,UAAU,EAAE,UAAU,EAAE,EAAE,CACvB,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;oBAC7B,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CACpC;qBACA,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAA;gBAC1C,MAAM,WAAW,GAAG,GAChB,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EACtD,GAAG,OAAQ,CAAC,MAAM,CAAC,EAAE,CAAA;gBAErB,MAAM,QAAQ,GACV,OAAQ,CAAC,QAAQ,CAAC,KAAK,GAAG;oBAC1B,OAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;gBAC5B,OAAO,IAAI,UAAU,CAAoB;oBACrC,KAAK,EAAE,KAAK;oBACZ,IAAI,EAAE,WAAW;oBACjB,WAAW,EAAE,YAAY;oBACzB,QAAQ,EAAE,QAAQ;oBAClB,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;iBAC9C,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;YACN,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAA;YAClD,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC,MAAM,CAC1B,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CACL,CAAA;YAEjB,OAAO,KAAK,CAAA;QAChB,CAAC,CAAC,CACL,CAAA;IACL,CAAC;IAED;;OAEG;IACO,cAAc,CACpB,KAAY,EACZ,iBAA2B,EAC3B,cAAwB;QAExB,MAAM,cAAc,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CACvC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,SAAS,CAC/B,CAAA;QACD,MAAM,gBAAgB,GAAG,cAAc,CAAC,IAAI,CACxC,CAAC,MAAM,EAAE,EAAE,CACP,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,kBAAkB,KAAK,WAAW,CACtE,CAAA;QACD,MAAM,WAAW,GAAG,cAAc,CAAC,MAAM,GAAG,CAAC,CAAA;QAC7C,IAAI,WAAW,IAAI,gBAAgB;YAC/B,MAAM,IAAI,YAAY,CAClB,gEAAgE,CACnE,CAAA;QAEL,MAAM,iBAAiB,GAAG,KAAK,CAAC,OAAO;aAClC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;aAC/D,IAAI,CAAC,IAAI,CAAC,CAAA;QACf,MAAM,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;QAClD,IAAI,GAAG,GAAG,gBAAgB,IAAI,CAAC,UAAU,CACrC,KAAK,CAAC,IAAI,CACb,KAAK,iBAAiB,EAAE,CAAA;QAEzB,MAAM,CAAC,WAAW,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;QAChE,MAAM,YAAY,GAAG,cAAc;YAC/B,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,SAAS,CAAC,OAAO,CACvD,aAAa,EACb,EAAE,CACL,EAAE;YACL,CAAC,CAAC,KAAK,CAAC,IAAI,CAAA;QAEhB,6DAA6D;QAC7D,KAAK,CAAC,OAAO;aACR,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC;aACnC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YAChB,MAAM,aAAa,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CACpC,CAAC,MAAM,EAAE,EAAE,CACP,MAAM,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;gBAC/B,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,IAAI,CAC5C,CAAA;YACD,IAAI,CAAC,aAAa;gBACd,KAAK,CAAC,OAAO,CAAC,IAAI,CACd,IAAI,WAAW,CAAC;oBACZ,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CACrD,KAAK,EACL,CAAC,MAAM,CAAC,IAAI,CAAC,CAChB;oBACD,WAAW,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC;iBAC7B,CAAC,CACL,CAAA;QACT,CAAC,CAAC,CAAA;QAEN,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3B,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO;iBAC3B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;gBACZ,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI;oBAC1B,CAAC,CAAC,MAAM,CAAC,IAAI;oBACb,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CAC/C,YAAY,EACZ,MAAM,CAAC,WAAW,CACrB,CAAA;gBACP,MAAM,WAAW,GAAG,MAAM,CAAC,WAAW;qBACjC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,UAAU,GAAG,CAAC;qBACtC,IAAI,CAAC,IAAI,CAAC,CAAA;gBACf,OAAO,eAAe,UAAU,aAAa,WAAW,GAAG,CAAA;YAC/D,CAAC,CAAC;iBACD,IAAI,CAAC,IAAI,CAAC,CAAA;YAEf,GAAG,IAAI,KAAK,UAAU,EAAE,CAAA;QAC5B,CAAC;QAED,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1B,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM;iBACzB,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;gBACX,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI;oBACxB,CAAC,CAAC,KAAK,CAAC,IAAI;oBACZ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,mBAAmB,CAC9C,YAAY,EACZ,KAAK,CAAC,UAAW,CACpB,CAAA;gBACP,OAAO,eAAe,SAAS,YAAY,KAAK,CAAC,UAAU,GAAG,CAAA;YAClE,CAAC,CAAC;iBACD,IAAI,CAAC,IAAI,CAAC,CAAA;YAEf,GAAG,IAAI,KAAK,SAAS,EAAE,CAAA;QAC3B,CAAC;QAED,IAAI,KAAK,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,IAAI,iBAAiB,EAAE,CAAC;YACpD,MAAM,cAAc,GAAG,KAAK,CAAC,WAAW;iBACnC,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE;gBACX,MAAM,CAAC,kBAAkB,CAAC,GAAG,IAAI,CAAC,cAAc,CAC5C,EAAE,CAAC,mBAAmB,CACzB,CAAA;gBACD,IAAI,kBAAkB,KAAK,QAAQ,EAAE,CAAC;oBAClC,OAAO,KAAK,CAAA;gBAChB,CAAC;gBACD,OAAO,IAAI,CAAA;YACf,CAAC,CAAC;iBACD,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE;gBACR,MAAM,CAAC,EAAE,eAAe,CAAC,GAAG,IAAI,CAAC,cAAc,CAC3C,EAAE,CAAC,mBAAmB,CACzB,CAAA;gBACD,MAAM,WAAW,GAAG,EAAE,CAAC,WAAW;qBAC7B,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,UAAU,GAAG,CAAC;qBACtC,IAAI,CAAC,IAAI,CAAC,CAAA;gBACf,IAAI,CAAC,EAAE,CAAC,IAAI;oBACR,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACnD,YAAY,EACZ,EAAE,CAAC,WAAW,EACd,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,EACrB,EAAE,CAAC,qBAAqB,CAC3B,CAAA;gBACL,MAAM,qBAAqB,GAAG,EAAE,CAAC,qBAAqB;qBACjD,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,UAAU,GAAG,CAAC;qBACtC,IAAI,CAAC,IAAI,CAAC,CAAA;gBAEf,IAAI,UAAU,GAAG,eAAe,EAAE,CAAC,IAAI,kBAAkB,WAAW,iBAAiB,eAAe,MAAM,qBAAqB,GAAG,CAAA;gBAClI,IAAI,EAAE,CAAC,QAAQ;oBAAE,UAAU,IAAI,cAAc,EAAE,CAAC,QAAQ,EAAE,CAAA;gBAC1D,IAAI,EAAE,CAAC,QAAQ;oBAAE,UAAU,IAAI,cAAc,EAAE,CAAC,QAAQ,EAAE,CAAA;gBAC1D,IAAI,EAAE,CAAC,UAAU;oBACb,UAAU,IAAI,eAAe,EAAE,CAAC,UAAU,EAAE,CAAA;gBAEhD,OAAO,UAAU,CAAA;YACrB,CAAC,CAAC;iBACD,IAAI,CAAC,IAAI,CAAC,CAAA;YAEf,GAAG,IAAI,KAAK,cAAc,EAAE,CAAA;QAChC,CAAC;QAED,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5B,MAAM,WAAW,GAAG,cAAc;iBAC7B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC;iBACnC,IAAI,CAAC,IAAI,CAAC,CAAA;YACf,GAAG,IAAI,kBAAkB,WAAW,GAAG,CAAA;QAC3C,CAAC;QAED,GAAG,IAAI,GAAG,CAAA;QAEV,IAAI,KAAK,CAAC,YAAY,EAAE,CAAC;YACrB,GAAG,IAAI,gBAAgB,CAAA;QAC3B,CAAC;QAED,OAAO,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;IACzB,CAAC;IAED;;OAEG;IACO,YAAY,CAClB,WAA2B,EAC3B,OAAiB;QAEjB,MAAM,SAAS,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAClD,CAAC,CAAC,WAAW,CAAC,IAAI;YAClB,CAAC,CAAC,WAAW,CAAA;QACjB,MAAM,KAAK,GAAG,OAAO;YACjB,CAAC,CAAC,wBAAwB,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;YACtD,CAAC,CAAC,cAAc,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAA;QAChD,OAAO,IAAI,KAAK,CAAC,KAAK,CAAC,CAAA;IAC3B,CAAC;IAES,aAAa,CAAC,IAAU;QAC9B,IAAI,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ,EAAE,CAAC;YACtC,OAAO,IAAI,KAAK,CAAC,gBAAgB,IAAI,CAAC,IAAI,QAAQ,IAAI,CAAC,UAAU,EAAE,CAAC,CAAA;QACxE,CAAC;aAAM,CAAC;YACJ,OAAO,IAAI,KAAK,CACZ,gBAAgB,IAAI,CAAC,IAAI,QAAQ,IAAI;iBAChC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC;iBAC3B,QAAQ,EAAE,EAAE,CACpB,CAAA;QACL,CAAC;IACL,CAAC;IAES,uBAAuB,CAAC,IAAU;QACxC,MAAM,UAAU,GACZ,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ;YAC/B,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE;YACxB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,QAAQ,EAAE,CAAA;QACrD,OAAO,IAAI,CAAC,wBAAwB,CAAC;YACjC,IAAI,EAAE,iBAAiB,CAAC,IAAI;YAC5B,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,KAAK,EAAE,UAAU;SACpB,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACO,WAAW,CAAC,UAAyB;QAC3C,MAAM,QAAQ,GAAG,eAAe,CAAC,MAAM,CAAC,UAAU,CAAC;YAC/C,CAAC,CAAC,UAAU,CAAC,IAAI;YACjB,CAAC,CAAC,UAAU,CAAA;QAChB,OAAO,IAAI,KAAK,CAAC,cAAc,QAAQ,GAAG,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACO,uBAAuB,CAAC,UAAyB;QACvD,MAAM,QAAQ,GAAG,eAAe,CAAC,MAAM,CAAC,UAAU,CAAC;YAC/C,CAAC,CAAC,UAAU,CAAC,IAAI;YACjB,CAAC,CAAC,UAAU,CAAA;QAChB,OAAO,IAAI,CAAC,wBAAwB,CAAC;YACjC,IAAI,EAAE,iBAAiB,CAAC,IAAI;YAC5B,IAAI,EAAE,QAAQ;SACjB,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACO,cAAc,CAAC,KAAY,EAAE,KAAiB;QACpD,MAAM,OAAO,GAAG,KAAK,CAAC,WAAW;aAC5B,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,UAAU,GAAG,CAAC;aACtC,IAAI,CAAC,IAAI,CAAC,CAAA;QACf,MAAM,CAAC,QAAQ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;QAC7D,OAAO,IAAI,KAAK,CACZ,UAAU,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,SACrC,QAAQ,CAAC,CAAC,CAAC,IAAI,QAAQ,IAAI,CAAC,CAAC,CAAC,EAClC,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAK,CAAC,QAAQ,SAAS,MAAM,OAAO,KACzD,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAC3C,EAAE,CACL,CAAA;IACL,CAAC;IAED;;OAEG;IACO,YAAY,CAAC,WAAgC;QACnD,MAAM,SAAS,GAAG,eAAe,CAAC,YAAY,CAAC,WAAW,CAAC;YACvD,CAAC,CAAC,WAAW,CAAC,IAAI;YAClB,CAAC,CAAC,WAAW,CAAA;QACjB,OAAO,IAAI,KAAK,CAAC,cAAc,IAAI,CAAC,UAAU,CAAC,SAAU,CAAC,EAAE,CAAC,CAAA;IACjE,CAAC;IAED;;OAEG;IACO,oBAAoB,CAC1B,MAAmB,EACnB,WAAqB;QAErB,IAAI,CAAC,GAAG,GAAG,GAAG,MAAM,CAAC,IAAI,GAAG,GAAG,CAAA;QAC/B,IAAI,eAAe,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,CAAC;YAC3C,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;QAChD,CAAC;aAAM,CAAC;YACJ,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;QAC5D,CAAC;QAED,IAAI,MAAM,CAAC,IAAI;YACX,CAAC;gBACG,WAAW;oBACX,MAAM,CAAC,IAAI;oBACX,QAAQ;oBACR,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;oBACnD,KAAK,CAAA;QACb,IAAI,MAAM,CAAC,SAAS,IAAI,CAAC,WAAW;YAAE,CAAC,IAAI,cAAc,CAAA;QACzD,IACI,MAAM,CAAC,WAAW,KAAK,IAAI;YAC3B,MAAM,CAAC,kBAAkB,KAAK,WAAW;YAEzC,8FAA8F;YAC9F,CAAC,IAAI,gBAAgB,CAAA;QACzB,IAAI,MAAM,CAAC,SAAS;YAAE,CAAC,IAAI,WAAW,GAAG,MAAM,CAAC,SAAS,CAAA;QACzD,IAAI,MAAM,CAAC,UAAU,KAAK,IAAI;YAAE,CAAC,IAAI,WAAW,CAAA;QAEhD,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;YACtB,CAAC,IAAI,QAAQ,MAAM,CAAC,YAAY,KAC5B,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,SAClD,EAAE,CAAA;QACN,CAAC;aAAM,CAAC;YACJ,IAAI,MAAM,CAAC,OAAO,KAAK,SAAS,IAAI,MAAM,CAAC,OAAO,KAAK,IAAI;gBACvD,CAAC,IAAI,YAAY,GAAG,MAAM,CAAC,OAAO,GAAG,GAAG,CAAA;QAChD,CAAC;QAED,OAAO,CAAC,CAAA;IACZ,CAAC;IAES,KAAK,CAAC,aAAa,CACzB,QAAe,EACf,QAAe,EACf,WAAW,GAAG,IAAI;QAElB,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAE/B,yBAAyB;QACzB,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAC/B,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAA;YACxC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAA;QAC1D,CAAC,CAAC,CAAA;QAEF,2CAA2C;QAC3C,IAAI,CAAC,WAAW,EAAE,YAAY,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;QACpE,MAAM,CAAC,EAAE,YAAY,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;QAC3D,QAAQ,CAAC,IAAI,GAAG,YAAY,GAAG,GAC3B,WAAW,CAAC,CAAC,CAAC,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC,EACtC,aAAa,YAAY,EAAE,CAAA;QAE3B,mBAAmB;QACnB,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAA;QACzD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAA;QAE7C,qDAAqD;QACrD,IAAI,WAAW,EAAE,CAAC;YACd,IAAI,cAAc,GAAG,QAAQ,CAAC,OAAO;iBAChC,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC;iBACzC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC,CAAA;YAExC,IAAI,cAAc,GAAG,QAAQ,CAAC,OAAO;iBAChC,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC;iBACzC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC,CAAA;YAExC,IAAI,cAAc,CAAC,MAAM,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC;gBAChD,cAAc,GAAG,QAAQ,CAAC,OAAO;qBAC5B,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE;oBACf,MAAM,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,CACnC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,CAChC,CAAA;oBACD,IAAI,SAAS,IAAI,SAAS,CAAC,aAAa;wBAAE,OAAO,KAAK,CAAA;oBACtD,OAAO,CAAC,MAAM,CAAC,aAAa,IAAI,SAAS,CAAA;gBAC7C,CAAC,CAAC;qBACD,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC,CAAA;YAC5C,CAAC;iBAAM,IAAI,cAAc,CAAC,MAAM,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC;gBACvD,cAAc,GAAG,QAAQ,CAAC,OAAO;qBAC5B,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE;oBACf,OAAO,CACH,CAAC,MAAM,CAAC,aAAa;wBACrB,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC,CACvD,CAAA;gBACL,CAAC,CAAC;qBACD,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC,CAAA;YAC5C,CAAC;YAED,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,QAAQ,CAAC,IAAI,CAChB,IAAI,cAAc,CAAC,IAAI,CACpB,IAAI,CACP,YAAY,cAAc,CAAC,IAAI,CAC5B,IAAI,CACP,SAAS,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAC7C,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,QAAQ,CAAC,IAAI,CAChB,IAAI,cAAc,CAAC,IAAI,CACpB,IAAI,CACP,YAAY,cAAc,CAAC,IAAI,CAC5B,IAAI,CACP,SAAS,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAC7C,CACJ,CAAA;QACL,CAAC;QAED,iBAAiB;QACjB,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAA;QAC3C,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAA;QAErD,mBAAmB;QACnB,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,QAAQ,CAAC,IAAI,CAChB,cAAc,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CACjD,CACJ,CAAA;QACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,QAAQ,CAAC,IAAI,CAChB,cAAc,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CACjD,CACJ,CAAA;QAED,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAA;QAE7B,yBAAyB;QACzB,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAC/B,sFAAsF;YACtF,IAAI,CAAC,KAAK,CAAC,IAAI;gBACX,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CACjD,QAAQ,EACR,KAAK,CAAC,WAAW,EACjB,KAAK,CAAC,KAAK,CACd,CAAA;YACL,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAA;YACpD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAA;QAC9C,CAAC,CAAC,CAAA;QAEF,uDAAuD;QACvD,mDAAmD;QACnD,QAAQ,CAAC,OAAO;aACX,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE;YACf,MAAM,cAAc,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,CACxC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,CAChC,CAAA;YACD,kFAAkF;YAClF,qCAAqC;YACrC,OAAO,CACH,MAAM,CAAC,aAAa;gBACpB,MAAM,CAAC,YAAY;gBACnB,CAAC,CAAC,cAAc;oBACZ,CAAC,CAAC,cAAc,CAAC,aAAa;wBAC1B,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC,CACzC,CAAA;QACL,CAAC,CAAC;aACD,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YAChB,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,KAAK,EAAE,QAAQ,CAAC,IAAI;gBACpB,IAAI,EAAE,iBAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;aACpB,CAAC,CAAA;YAEF,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,KAAK,EAAE,QAAQ,CAAC,IAAI;gBACpB,IAAI,EAAE,iBAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,KAAK,EAAE,MAAM,CAAC,YAAY;aAC7B,CAAC,CAAA;YAEF,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YAC3B,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QACjC,CAAC,CAAC,CAAA;QAEN,6CAA6C;QAC7C,QAAQ,CAAC,OAAO;aACX,MAAM,CACH,CAAC,MAAM,EAAE,EAAE,CACP,MAAM,CAAC,aAAa;YACpB,MAAM,CAAC,YAAY;YACnB,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC,CAC5D;aACA,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YAChB,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,KAAK,EAAE,QAAQ,CAAC,IAAI;gBACpB,IAAI,EAAE,iBAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,KAAK,EAAE,MAAM,CAAC,YAAY;aAC7B,CAAC,CAAA;YAEF,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,KAAK,EAAE,QAAQ,CAAC,IAAI;gBACpB,IAAI,EAAE,iBAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;aACpB,CAAC,CAAA;YAEF,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YAC3B,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QACjC,CAAC,CAAC,CAAA;QAEN,qCAAqC;QACrC,QAAQ,CAAC,OAAO;aACX,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,aAAa,IAAI,MAAM,CAAC,YAAY,CAAC;aAC/D,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YAChB,MAAM,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,CACnC,CAAC,CAAC,EAAE,EAAE,CACF,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI;gBACtB,CAAC,CAAC,aAAa;gBACf,MAAM,CAAC,aAAa;gBACpB,CAAC,CAAC,YAAY,KAAK,MAAM,CAAC,YAAY,CAC7C,CAAA;YAED,IAAI,CAAC,SAAS;gBAAE,OAAM;YAEtB,oBAAoB;YACpB,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,KAAK,EAAE,QAAQ,CAAC,IAAI;gBACpB,IAAI,EAAE,iBAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,SAAS,CAAC,IAAI;aACvB,CAAC,CAAA;YAEF,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,KAAK,EAAE,QAAQ,CAAC,IAAI;gBACpB,IAAI,EAAE,iBAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,KAAK,EAAE,MAAM,CAAC,YAAY;aAC7B,CAAC,CAAA;YAEF,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YAC3B,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YAE3B,gBAAgB;YAChB,MAAM,iBAAiB,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBACpD,KAAK,EAAE,QAAQ,CAAC,IAAI;gBACpB,IAAI,EAAE,iBAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,SAAS,CAAC,IAAI;gBACpB,KAAK,EAAE,SAAS,CAAC,YAAY;aAChC,CAAC,CAAA;YAEF,MAAM,iBAAiB,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBACpD,KAAK,EAAE,QAAQ,CAAC,IAAI;gBACpB,IAAI,EAAE,iBAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;aACpB,CAAC,CAAA;YAEF,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAA;YACnC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAA;QACvC,CAAC,CAAC,CAAA;QAEN,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;QACjD,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACO,cAAc,CAAC,SAAiB;QACtC,OAAO,CACH,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YACzB,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC;YACtB,CAAC,CAAC,CAAC,SAAS,EAAE,SAAS,CAAC,CACC,CAAA;IACrC,CAAC;IAED;;OAEG;IACO,UAAU,CAChB,MAA6B,EAC7B,aAAuB;QAEvB,MAAM,SAAS,GACX,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,eAAe,CAAC,MAAM,CAAC,MAAM,CAAC;YAC7D,CAAC,CAAC,MAAM,CAAC,IAAI;YACb,CAAC,CAAC,MAAM,CAAA;QAChB,OAAO,SAAS;aACX,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;aACzB,KAAK,CAAC,GAAG,CAAC;aACV,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aAC1C,IAAI,CAAC,GAAG,CAAC,CAAA;IAClB,CAAC;IAED;;OAEG;IACH,kBAAkB,CACd,WAA2B,EAC3B,OAAgB;QAEhB,MAAM,IAAI,YAAY,CAAC,+CAA+C,CAAC,CAAA;IAC3E,CAAC;CACJ", "file": "AbstractSqliteQueryRunner.js", "sourcesContent": ["import { QueryRunner } from \"../../query-runner/QueryRunner\"\nimport { ObjectLiteral } from \"../../common/ObjectLiteral\"\nimport { TransactionNotStartedError } from \"../../error/TransactionNotStartedError\"\nimport { TableColumn } from \"../../schema-builder/table/TableColumn\"\nimport { Table } from \"../../schema-builder/table/Table\"\nimport { TableIndex } from \"../../schema-builder/table/TableIndex\"\nimport { TableForeignKey } from \"../../schema-builder/table/TableForeignKey\"\nimport { View } from \"../../schema-builder/view/View\"\nimport { Query } from \"../Query\"\nimport { AbstractSqliteDriver } from \"./AbstractSqliteDriver\"\nimport { ReadStream } from \"../../platform/PlatformTools\"\nimport { TableIndexOptions } from \"../../schema-builder/options/TableIndexOptions\"\nimport { TableUnique } from \"../../schema-builder/table/TableUnique\"\nimport { BaseQueryRunner } from \"../../query-runner/BaseQueryRunner\"\nimport { OrmUtils } from \"../../util/OrmUtils\"\nimport { TableCheck } from \"../../schema-builder/table/TableCheck\"\nimport { IsolationLevel } from \"../types/IsolationLevel\"\nimport { TableExclusion } from \"../../schema-builder/table/TableExclusion\"\nimport { TransactionAlreadyStartedError, TypeORMError } from \"../../error\"\nimport { MetadataTableType } from \"../types/MetadataTableType\"\nimport { InstanceChecker } from \"../../util/InstanceChecker\"\n\n/**\n * Runs queries on a single sqlite database connection.\n */\nexport abstract class AbstractSqliteQueryRunner\n    extends BaseQueryRunner\n    implements QueryRunner\n{\n    // -------------------------------------------------------------------------\n    // Public Implemented Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Database driver used by connection.\n     */\n    driver: AbstractSqliteDriver\n\n    protected transactionPromise: Promise<any> | null = null\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor() {\n        super()\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates/uses database connection from the connection pool to perform further operations.\n     * Returns obtained database connection.\n     */\n    connect(): Promise<any> {\n        return Promise.resolve(this.driver.databaseConnection)\n    }\n\n    /**\n     * Releases used database connection.\n     * We just clear loaded tables and sql in memory, because sqlite do not support multiple connections thus query runners.\n     */\n    release(): Promise<void> {\n        this.loadedTables = []\n        this.clearSqlMemory()\n        return Promise.resolve()\n    }\n\n    /**\n     * Starts transaction.\n     */\n    async startTransaction(isolationLevel?: IsolationLevel): Promise<void> {\n        if (this.driver.transactionSupport === \"none\")\n            throw new TypeORMError(\n                `Transactions aren't supported by ${this.connection.driver.options.type}.`,\n            )\n\n        if (\n            this.isTransactionActive &&\n            this.driver.transactionSupport === \"simple\"\n        )\n            throw new TransactionAlreadyStartedError()\n\n        if (\n            isolationLevel &&\n            isolationLevel !== \"READ UNCOMMITTED\" &&\n            isolationLevel !== \"SERIALIZABLE\"\n        )\n            throw new TypeORMError(\n                `SQLite only supports SERIALIZABLE and READ UNCOMMITTED isolation`,\n            )\n\n        this.isTransactionActive = true\n        try {\n            await this.broadcaster.broadcast(\"BeforeTransactionStart\")\n        } catch (err) {\n            this.isTransactionActive = false\n            throw err\n        }\n\n        if (this.transactionDepth === 0) {\n            if (isolationLevel) {\n                if (isolationLevel === \"READ UNCOMMITTED\") {\n                    await this.query(\"PRAGMA read_uncommitted = true\")\n                } else {\n                    await this.query(\"PRAGMA read_uncommitted = false\")\n                }\n            }\n            await this.query(\"BEGIN TRANSACTION\")\n        } else {\n            await this.query(`SAVEPOINT typeorm_${this.transactionDepth}`)\n        }\n        this.transactionDepth += 1\n\n        await this.broadcaster.broadcast(\"AfterTransactionStart\")\n    }\n\n    /**\n     * Commits transaction.\n     * Error will be thrown if transaction was not started.\n     */\n    async commitTransaction(): Promise<void> {\n        if (!this.isTransactionActive) throw new TransactionNotStartedError()\n\n        await this.broadcaster.broadcast(\"BeforeTransactionCommit\")\n\n        if (this.transactionDepth > 1) {\n            await this.query(\n                `RELEASE SAVEPOINT typeorm_${this.transactionDepth - 1}`,\n            )\n        } else {\n            await this.query(\"COMMIT\")\n            this.isTransactionActive = false\n        }\n        this.transactionDepth -= 1\n\n        await this.broadcaster.broadcast(\"AfterTransactionCommit\")\n    }\n\n    /**\n     * Rollbacks transaction.\n     * Error will be thrown if transaction was not started.\n     */\n    async rollbackTransaction(): Promise<void> {\n        if (!this.isTransactionActive) throw new TransactionNotStartedError()\n\n        await this.broadcaster.broadcast(\"BeforeTransactionRollback\")\n\n        if (this.transactionDepth > 1) {\n            await this.query(\n                `ROLLBACK TO SAVEPOINT typeorm_${this.transactionDepth - 1}`,\n            )\n        } else {\n            await this.query(\"ROLLBACK\")\n            this.isTransactionActive = false\n        }\n        this.transactionDepth -= 1\n\n        await this.broadcaster.broadcast(\"AfterTransactionRollback\")\n    }\n\n    /**\n     * Returns raw data stream.\n     */\n    stream(\n        query: string,\n        parameters?: any[],\n        onEnd?: Function,\n        onError?: Function,\n    ): Promise<ReadStream> {\n        throw new TypeORMError(`Stream is not supported by sqlite driver.`)\n    }\n\n    /**\n     * Returns all available database names including system databases.\n     */\n    async getDatabases(): Promise<string[]> {\n        return Promise.resolve([])\n    }\n\n    /**\n     * Returns all available schema names including system schemas.\n     * If database parameter specified, returns schemas of that database.\n     */\n    async getSchemas(database?: string): Promise<string[]> {\n        return Promise.resolve([])\n    }\n\n    /**\n     * Checks if database with the given name exist.\n     */\n    async hasDatabase(database: string): Promise<boolean> {\n        return Promise.resolve(false)\n    }\n\n    /**\n     * Loads currently using database\n     */\n    async getCurrentDatabase(): Promise<undefined> {\n        return Promise.resolve(undefined)\n    }\n\n    /**\n     * Checks if schema with the given name exist.\n     */\n    async hasSchema(schema: string): Promise<boolean> {\n        throw new TypeORMError(`This driver does not support table schemas`)\n    }\n\n    /**\n     * Loads currently using database schema\n     */\n    async getCurrentSchema(): Promise<undefined> {\n        return Promise.resolve(undefined)\n    }\n\n    /**\n     * Checks if table with the given name exist in the database.\n     */\n    async hasTable(tableOrName: Table | string): Promise<boolean> {\n        const tableName = InstanceChecker.isTable(tableOrName)\n            ? tableOrName.name\n            : tableOrName\n        const sql = `SELECT * FROM \"sqlite_master\" WHERE \"type\" = 'table' AND \"name\" = '${tableName}'`\n        const result = await this.query(sql)\n        return result.length ? true : false\n    }\n\n    /**\n     * Checks if column with the given name exist in the given table.\n     */\n    async hasColumn(\n        tableOrName: Table | string,\n        columnName: string,\n    ): Promise<boolean> {\n        const tableName = InstanceChecker.isTable(tableOrName)\n            ? tableOrName.name\n            : tableOrName\n        const sql = `PRAGMA table_xinfo(${this.escapePath(tableName)})`\n        const columns: ObjectLiteral[] = await this.query(sql)\n        return !!columns.find((column) => column[\"name\"] === columnName)\n    }\n\n    /**\n     * Creates a new database.\n     */\n    async createDatabase(\n        database: string,\n        ifNotExist?: boolean,\n    ): Promise<void> {\n        return Promise.resolve()\n    }\n\n    /**\n     * Drops database.\n     */\n    async dropDatabase(database: string, ifExist?: boolean): Promise<void> {\n        return Promise.resolve()\n    }\n\n    /**\n     * Creates a new table schema.\n     */\n    async createSchema(\n        schemaPath: string,\n        ifNotExist?: boolean,\n    ): Promise<void> {\n        return Promise.resolve()\n    }\n\n    /**\n     * Drops table schema.\n     */\n    async dropSchema(schemaPath: string, ifExist?: boolean): Promise<void> {\n        return Promise.resolve()\n    }\n\n    /**\n     * Creates a new table.\n     */\n    async createTable(\n        table: Table,\n        ifNotExist: boolean = false,\n        createForeignKeys: boolean = true,\n        createIndices: boolean = true,\n    ): Promise<void> {\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n\n        if (ifNotExist) {\n            const isTableExist = await this.hasTable(table)\n            if (isTableExist) return Promise.resolve()\n        }\n\n        upQueries.push(this.createTableSql(table, createForeignKeys))\n        downQueries.push(this.dropTableSql(table))\n\n        if (createIndices) {\n            table.indices.forEach((index) => {\n                // new index may be passed without name. In this case we generate index name manually.\n                if (!index.name)\n                    index.name = this.connection.namingStrategy.indexName(\n                        table,\n                        index.columnNames,\n                        index.where,\n                    )\n                upQueries.push(this.createIndexSql(table, index))\n                downQueries.push(this.dropIndexSql(index))\n            })\n        }\n\n        // if table have column with generated type, we must add the expression to the metadata table\n        const generatedColumns = table.columns.filter(\n            (column) => column.generatedType && column.asExpression,\n        )\n\n        for (const column of generatedColumns) {\n            const insertQuery = this.insertTypeormMetadataSql({\n                table: table.name,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n                value: column.asExpression,\n            })\n\n            const deleteQuery = this.deleteTypeormMetadataSql({\n                table: table.name,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n            })\n\n            upQueries.push(insertQuery)\n            downQueries.push(deleteQuery)\n        }\n\n        await this.executeQueries(upQueries, downQueries)\n    }\n\n    /**\n     * Drops the table.\n     */\n    async dropTable(\n        tableOrName: Table | string,\n        ifExist?: boolean,\n        dropForeignKeys: boolean = true,\n        dropIndices: boolean = true,\n    ): Promise<void> {\n        if (ifExist) {\n            const isTableExist = await this.hasTable(tableOrName)\n            if (!isTableExist) return Promise.resolve()\n        }\n\n        // if dropTable called with dropForeignKeys = true, we must create foreign keys in down query.\n        const createForeignKeys: boolean = dropForeignKeys\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n\n        if (dropIndices) {\n            table.indices.forEach((index) => {\n                upQueries.push(this.dropIndexSql(index))\n                downQueries.push(this.createIndexSql(table, index))\n            })\n        }\n\n        upQueries.push(this.dropTableSql(table, ifExist))\n        downQueries.push(this.createTableSql(table, createForeignKeys))\n\n        // if table had columns with generated type, we must remove the expression from the metadata table\n        const generatedColumns = table.columns.filter(\n            (column) => column.generatedType && column.asExpression,\n        )\n\n        for (const column of generatedColumns) {\n            const deleteQuery = this.deleteTypeormMetadataSql({\n                table: table.name,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n            })\n\n            const insertQuery = this.insertTypeormMetadataSql({\n                table: table.name,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n                value: column.asExpression,\n            })\n\n            upQueries.push(deleteQuery)\n            downQueries.push(insertQuery)\n        }\n\n        await this.executeQueries(upQueries, downQueries)\n    }\n\n    /**\n     * Creates a new view.\n     */\n    async createView(\n        view: View,\n        syncWithMetadata: boolean = false,\n    ): Promise<void> {\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n        upQueries.push(this.createViewSql(view))\n        if (syncWithMetadata) upQueries.push(this.insertViewDefinitionSql(view))\n        downQueries.push(this.dropViewSql(view))\n        if (syncWithMetadata)\n            downQueries.push(this.deleteViewDefinitionSql(view))\n        await this.executeQueries(upQueries, downQueries)\n    }\n\n    /**\n     * Drops the view.\n     */\n    async dropView(target: View | string): Promise<void> {\n        const viewName = InstanceChecker.isView(target) ? target.name : target\n        const view = await this.getCachedView(viewName)\n\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n        upQueries.push(this.deleteViewDefinitionSql(view))\n        upQueries.push(this.dropViewSql(view))\n        downQueries.push(this.insertViewDefinitionSql(view))\n        downQueries.push(this.createViewSql(view))\n        await this.executeQueries(upQueries, downQueries)\n    }\n\n    /**\n     * Renames the given table.\n     */\n    async renameTable(\n        oldTableOrName: Table | string,\n        newTableName: string,\n    ): Promise<void> {\n        const oldTable = InstanceChecker.isTable(oldTableOrName)\n            ? oldTableOrName\n            : await this.getCachedTable(oldTableOrName)\n        const newTable = oldTable.clone()\n\n        newTable.name = newTableName\n\n        // rename table\n        const up = new Query(\n            `ALTER TABLE ${this.escapePath(\n                oldTable.name,\n            )} RENAME TO ${this.escapePath(newTableName)}`,\n        )\n        const down = new Query(\n            `ALTER TABLE ${this.escapePath(\n                newTableName,\n            )} RENAME TO ${this.escapePath(oldTable.name)}`,\n        )\n        await this.executeQueries(up, down)\n\n        // rename unique constraints\n        newTable.uniques.forEach((unique) => {\n            const oldUniqueName =\n                this.connection.namingStrategy.uniqueConstraintName(\n                    oldTable,\n                    unique.columnNames,\n                )\n\n            // Skip renaming if Unique has user defined constraint name\n            if (unique.name !== oldUniqueName) return\n\n            unique.name = this.connection.namingStrategy.uniqueConstraintName(\n                newTable,\n                unique.columnNames,\n            )\n        })\n\n        // rename foreign key constraints\n        newTable.foreignKeys.forEach((foreignKey) => {\n            const oldForeignKeyName =\n                this.connection.namingStrategy.foreignKeyName(\n                    oldTable,\n                    foreignKey.columnNames,\n                    this.getTablePath(foreignKey),\n                    foreignKey.referencedColumnNames,\n                )\n\n            // Skip renaming if foreign key has user defined constraint name\n            if (foreignKey.name !== oldForeignKeyName) return\n\n            foreignKey.name = this.connection.namingStrategy.foreignKeyName(\n                newTable,\n                foreignKey.columnNames,\n                this.getTablePath(foreignKey),\n                foreignKey.referencedColumnNames,\n            )\n        })\n\n        // rename indices\n        newTable.indices.forEach((index) => {\n            const oldIndexName = this.connection.namingStrategy.indexName(\n                oldTable,\n                index.columnNames,\n                index.where,\n            )\n\n            // Skip renaming if Index has user defined constraint name\n            if (index.name !== oldIndexName) return\n\n            index.name = this.connection.namingStrategy.indexName(\n                newTable,\n                index.columnNames,\n                index.where,\n            )\n        })\n\n        // rename old table;\n        oldTable.name = newTable.name\n\n        // recreate table with new constraint names\n        await this.recreateTable(newTable, oldTable)\n    }\n\n    /**\n     * Creates a new column from the column in the table.\n     */\n    async addColumn(\n        tableOrName: Table | string,\n        column: TableColumn,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        return this.addColumns(table!, [column])\n    }\n\n    /**\n     * Creates a new columns from the column in the table.\n     */\n    async addColumns(\n        tableOrName: Table | string,\n        columns: TableColumn[],\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const changedTable = table.clone()\n        columns.forEach((column) => changedTable.addColumn(column))\n        await this.recreateTable(changedTable, table)\n    }\n\n    /**\n     * Renames column in the given table.\n     */\n    async renameColumn(\n        tableOrName: Table | string,\n        oldTableColumnOrName: TableColumn | string,\n        newTableColumnOrName: TableColumn | string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const oldColumn = InstanceChecker.isTableColumn(oldTableColumnOrName)\n            ? oldTableColumnOrName\n            : table.columns.find((c) => c.name === oldTableColumnOrName)\n        if (!oldColumn)\n            throw new TypeORMError(\n                `Column \"${oldTableColumnOrName}\" was not found in the \"${table.name}\" table.`,\n            )\n\n        let newColumn: TableColumn | undefined = undefined\n        if (InstanceChecker.isTableColumn(newTableColumnOrName)) {\n            newColumn = newTableColumnOrName\n        } else {\n            newColumn = oldColumn.clone()\n            newColumn.name = newTableColumnOrName\n        }\n\n        return this.changeColumn(table, oldColumn, newColumn)\n    }\n\n    /**\n     * Changes a column in the table.\n     */\n    async changeColumn(\n        tableOrName: Table | string,\n        oldTableColumnOrName: TableColumn | string,\n        newColumn: TableColumn,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const oldColumn = InstanceChecker.isTableColumn(oldTableColumnOrName)\n            ? oldTableColumnOrName\n            : table.columns.find((c) => c.name === oldTableColumnOrName)\n        if (!oldColumn)\n            throw new TypeORMError(\n                `Column \"${oldTableColumnOrName}\" was not found in the \"${table.name}\" table.`,\n            )\n\n        await this.changeColumns(table, [{ oldColumn, newColumn }])\n    }\n\n    /**\n     * Changes a column in the table.\n     * Changed column looses all its keys in the db.\n     */\n    async changeColumns(\n        tableOrName: Table | string,\n        changedColumns: { oldColumn: TableColumn; newColumn: TableColumn }[],\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const changedTable = table.clone()\n        changedColumns.forEach((changedColumnSet) => {\n            if (\n                changedColumnSet.newColumn.name !==\n                changedColumnSet.oldColumn.name\n            ) {\n                changedTable\n                    .findColumnUniques(changedColumnSet.oldColumn)\n                    .forEach((unique) => {\n                        const uniqueName =\n                            this.connection.namingStrategy.uniqueConstraintName(\n                                table,\n                                unique.columnNames,\n                            )\n\n                        unique.columnNames.splice(\n                            unique.columnNames.indexOf(\n                                changedColumnSet.oldColumn.name,\n                            ),\n                            1,\n                        )\n                        unique.columnNames.push(changedColumnSet.newColumn.name)\n\n                        // rename Unique only if it has default constraint name\n                        if (unique.name === uniqueName) {\n                            unique.name =\n                                this.connection.namingStrategy.uniqueConstraintName(\n                                    changedTable,\n                                    unique.columnNames,\n                                )\n                        }\n                    })\n\n                changedTable\n                    .findColumnForeignKeys(changedColumnSet.oldColumn)\n                    .forEach((foreignKey) => {\n                        const foreignKeyName =\n                            this.connection.namingStrategy.foreignKeyName(\n                                table,\n                                foreignKey.columnNames,\n                                this.getTablePath(foreignKey),\n                                foreignKey.referencedColumnNames,\n                            )\n\n                        foreignKey.columnNames.splice(\n                            foreignKey.columnNames.indexOf(\n                                changedColumnSet.oldColumn.name,\n                            ),\n                            1,\n                        )\n                        foreignKey.columnNames.push(\n                            changedColumnSet.newColumn.name,\n                        )\n\n                        // rename FK only if it has default constraint name\n                        if (foreignKey.name === foreignKeyName) {\n                            foreignKey.name =\n                                this.connection.namingStrategy.foreignKeyName(\n                                    changedTable,\n                                    foreignKey.columnNames,\n                                    this.getTablePath(foreignKey),\n                                    foreignKey.referencedColumnNames,\n                                )\n                        }\n                    })\n\n                changedTable\n                    .findColumnIndices(changedColumnSet.oldColumn)\n                    .forEach((index) => {\n                        const indexName =\n                            this.connection.namingStrategy.indexName(\n                                table,\n                                index.columnNames,\n                                index.where,\n                            )\n\n                        index.columnNames.splice(\n                            index.columnNames.indexOf(\n                                changedColumnSet.oldColumn.name,\n                            ),\n                            1,\n                        )\n                        index.columnNames.push(changedColumnSet.newColumn.name)\n\n                        // rename Index only if it has default constraint name\n                        if (index.name === indexName) {\n                            index.name =\n                                this.connection.namingStrategy.indexName(\n                                    changedTable,\n                                    index.columnNames,\n                                    index.where,\n                                )\n                        }\n                    })\n            }\n            const originalColumn = changedTable.columns.find(\n                (column) => column.name === changedColumnSet.oldColumn.name,\n            )\n            if (originalColumn)\n                changedTable.columns[\n                    changedTable.columns.indexOf(originalColumn)\n                ] = changedColumnSet.newColumn\n        })\n\n        await this.recreateTable(changedTable, table)\n    }\n\n    /**\n     * Drops column in the table.\n     */\n    async dropColumn(\n        tableOrName: Table | string,\n        columnOrName: TableColumn | string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const column = InstanceChecker.isTableColumn(columnOrName)\n            ? columnOrName\n            : table.findColumnByName(columnOrName)\n        if (!column)\n            throw new TypeORMError(\n                `Column \"${columnOrName}\" was not found in table \"${table.name}\"`,\n            )\n\n        await this.dropColumns(table, [column])\n    }\n\n    /**\n     * Drops the columns in the table.\n     */\n    async dropColumns(\n        tableOrName: Table | string,\n        columns: TableColumn[] | string[],\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n\n        // clone original table and remove column and its constraints from cloned table\n        const changedTable = table.clone()\n        columns.forEach((column: TableColumn | string) => {\n            const columnInstance = InstanceChecker.isTableColumn(column)\n                ? column\n                : table.findColumnByName(column)\n            if (!columnInstance)\n                throw new Error(\n                    `Column \"${column}\" was not found in table \"${table.name}\"`,\n                )\n\n            changedTable.removeColumn(columnInstance)\n            changedTable\n                .findColumnUniques(columnInstance)\n                .forEach((unique) =>\n                    changedTable.removeUniqueConstraint(unique),\n                )\n            changedTable\n                .findColumnIndices(columnInstance)\n                .forEach((index) => changedTable.removeIndex(index))\n            changedTable\n                .findColumnForeignKeys(columnInstance)\n                .forEach((fk) => changedTable.removeForeignKey(fk))\n        })\n\n        await this.recreateTable(changedTable, table)\n    }\n\n    /**\n     * Creates a new primary key.\n     */\n    async createPrimaryKey(\n        tableOrName: Table | string,\n        columnNames: string[],\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        // clone original table and mark columns as primary\n        const changedTable = table.clone()\n        changedTable.columns.forEach((column) => {\n            if (columnNames.find((columnName) => columnName === column.name))\n                column.isPrimary = true\n        })\n\n        await this.recreateTable(changedTable, table)\n        // mark columns as primary in original table\n        table.columns.forEach((column) => {\n            if (columnNames.find((columnName) => columnName === column.name))\n                column.isPrimary = true\n        })\n    }\n\n    /**\n     * Updates composite primary keys.\n     */\n    async updatePrimaryKeys(\n        tableOrName: Table | string,\n        columns: TableColumn[],\n    ): Promise<void> {\n        await Promise.resolve()\n    }\n\n    /**\n     * Drops a primary key.\n     */\n    async dropPrimaryKey(tableOrName: Table | string): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        // clone original table and mark primary columns as non-primary\n        const changedTable = table.clone()\n        changedTable.primaryColumns.forEach((column) => {\n            column.isPrimary = false\n        })\n\n        await this.recreateTable(changedTable, table)\n        // mark primary columns as non-primary in original table\n        table.primaryColumns.forEach((column) => {\n            column.isPrimary = false\n        })\n    }\n\n    /**\n     * Creates a new unique constraint.\n     */\n    async createUniqueConstraint(\n        tableOrName: Table | string,\n        uniqueConstraint: TableUnique,\n    ): Promise<void> {\n        await this.createUniqueConstraints(tableOrName, [uniqueConstraint])\n    }\n\n    /**\n     * Creates a new unique constraints.\n     */\n    async createUniqueConstraints(\n        tableOrName: Table | string,\n        uniqueConstraints: TableUnique[],\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n\n        // clone original table and add unique constraints in to cloned table\n        const changedTable = table.clone()\n        uniqueConstraints.forEach((uniqueConstraint) =>\n            changedTable.addUniqueConstraint(uniqueConstraint),\n        )\n        await this.recreateTable(changedTable, table)\n    }\n\n    /**\n     * Drops an unique constraint.\n     */\n    async dropUniqueConstraint(\n        tableOrName: Table | string,\n        uniqueOrName: TableUnique | string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const uniqueConstraint = InstanceChecker.isTableUnique(uniqueOrName)\n            ? uniqueOrName\n            : table.uniques.find((u) => u.name === uniqueOrName)\n        if (!uniqueConstraint)\n            throw new TypeORMError(\n                `Supplied unique constraint was not found in table ${table.name}`,\n            )\n\n        await this.dropUniqueConstraints(table, [uniqueConstraint])\n    }\n\n    /**\n     * Creates an unique constraints.\n     */\n    async dropUniqueConstraints(\n        tableOrName: Table | string,\n        uniqueConstraints: TableUnique[],\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n\n        // clone original table and remove unique constraints from cloned table\n        const changedTable = table.clone()\n        uniqueConstraints.forEach((uniqueConstraint) =>\n            changedTable.removeUniqueConstraint(uniqueConstraint),\n        )\n\n        await this.recreateTable(changedTable, table)\n    }\n\n    /**\n     * Creates new check constraint.\n     */\n    async createCheckConstraint(\n        tableOrName: Table | string,\n        checkConstraint: TableCheck,\n    ): Promise<void> {\n        await this.createCheckConstraints(tableOrName, [checkConstraint])\n    }\n\n    /**\n     * Creates new check constraints.\n     */\n    async createCheckConstraints(\n        tableOrName: Table | string,\n        checkConstraints: TableCheck[],\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n\n        // clone original table and add check constraints in to cloned table\n        const changedTable = table.clone()\n        checkConstraints.forEach((checkConstraint) =>\n            changedTable.addCheckConstraint(checkConstraint),\n        )\n        await this.recreateTable(changedTable, table)\n    }\n\n    /**\n     * Drops check constraint.\n     */\n    async dropCheckConstraint(\n        tableOrName: Table | string,\n        checkOrName: TableCheck | string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const checkConstraint = InstanceChecker.isTableCheck(checkOrName)\n            ? checkOrName\n            : table.checks.find((c) => c.name === checkOrName)\n        if (!checkConstraint)\n            throw new TypeORMError(\n                `Supplied check constraint was not found in table ${table.name}`,\n            )\n\n        await this.dropCheckConstraints(table, [checkConstraint])\n    }\n\n    /**\n     * Drops check constraints.\n     */\n    async dropCheckConstraints(\n        tableOrName: Table | string,\n        checkConstraints: TableCheck[],\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n\n        // clone original table and remove check constraints from cloned table\n        const changedTable = table.clone()\n        checkConstraints.forEach((checkConstraint) =>\n            changedTable.removeCheckConstraint(checkConstraint),\n        )\n\n        await this.recreateTable(changedTable, table)\n    }\n\n    /**\n     * Creates a new exclusion constraint.\n     */\n    async createExclusionConstraint(\n        tableOrName: Table | string,\n        exclusionConstraint: TableExclusion,\n    ): Promise<void> {\n        throw new TypeORMError(`Sqlite does not support exclusion constraints.`)\n    }\n\n    /**\n     * Creates a new exclusion constraints.\n     */\n    async createExclusionConstraints(\n        tableOrName: Table | string,\n        exclusionConstraints: TableExclusion[],\n    ): Promise<void> {\n        throw new TypeORMError(`Sqlite does not support exclusion constraints.`)\n    }\n\n    /**\n     * Drops exclusion constraint.\n     */\n    async dropExclusionConstraint(\n        tableOrName: Table | string,\n        exclusionOrName: TableExclusion | string,\n    ): Promise<void> {\n        throw new TypeORMError(`Sqlite does not support exclusion constraints.`)\n    }\n\n    /**\n     * Drops exclusion constraints.\n     */\n    async dropExclusionConstraints(\n        tableOrName: Table | string,\n        exclusionConstraints: TableExclusion[],\n    ): Promise<void> {\n        throw new TypeORMError(`Sqlite does not support exclusion constraints.`)\n    }\n\n    /**\n     * Creates a new foreign key.\n     */\n    async createForeignKey(\n        tableOrName: Table | string,\n        foreignKey: TableForeignKey,\n    ): Promise<void> {\n        await this.createForeignKeys(tableOrName, [foreignKey])\n    }\n\n    /**\n     * Creates a new foreign keys.\n     */\n    async createForeignKeys(\n        tableOrName: Table | string,\n        foreignKeys: TableForeignKey[],\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        // clone original table and add foreign keys in to cloned table\n        const changedTable = table.clone()\n        foreignKeys.forEach((foreignKey) =>\n            changedTable.addForeignKey(foreignKey),\n        )\n\n        await this.recreateTable(changedTable, table)\n    }\n\n    /**\n     * Drops a foreign key from the table.\n     */\n    async dropForeignKey(\n        tableOrName: Table | string,\n        foreignKeyOrName: TableForeignKey | string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const foreignKey = InstanceChecker.isTableForeignKey(foreignKeyOrName)\n            ? foreignKeyOrName\n            : table.foreignKeys.find((fk) => fk.name === foreignKeyOrName)\n        if (!foreignKey)\n            throw new TypeORMError(\n                `Supplied foreign key was not found in table ${table.name}`,\n            )\n\n        await this.dropForeignKeys(tableOrName, [foreignKey])\n    }\n\n    /**\n     * Drops a foreign keys from the table.\n     */\n    async dropForeignKeys(\n        tableOrName: Table | string,\n        foreignKeys: TableForeignKey[],\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n\n        // clone original table and remove foreign keys from cloned table\n        const changedTable = table.clone()\n        foreignKeys.forEach((foreignKey) =>\n            changedTable.removeForeignKey(foreignKey),\n        )\n\n        await this.recreateTable(changedTable, table)\n    }\n\n    /**\n     * Creates a new index.\n     */\n    async createIndex(\n        tableOrName: Table | string,\n        index: TableIndex,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n\n        // new index may be passed without name. In this case we generate index name manually.\n        if (!index.name) index.name = this.generateIndexName(table, index)\n\n        const up = this.createIndexSql(table, index)\n        const down = this.dropIndexSql(index)\n        await this.executeQueries(up, down)\n        table.addIndex(index)\n    }\n\n    /**\n     * Creates a new indices\n     */\n    async createIndices(\n        tableOrName: Table | string,\n        indices: TableIndex[],\n    ): Promise<void> {\n        const promises = indices.map((index) =>\n            this.createIndex(tableOrName, index),\n        )\n        await Promise.all(promises)\n    }\n\n    /**\n     * Drops an index from the table.\n     */\n    async dropIndex(\n        tableOrName: Table | string,\n        indexOrName: TableIndex | string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const index = InstanceChecker.isTableIndex(indexOrName)\n            ? indexOrName\n            : table.indices.find((i) => i.name === indexOrName)\n        if (!index)\n            throw new TypeORMError(\n                `Supplied index ${indexOrName} was not found in table ${table.name}`,\n            )\n\n        // old index may be passed without name. In this case we generate index name manually.\n        if (!index.name) index.name = this.generateIndexName(table, index)\n\n        const up = this.dropIndexSql(index)\n        const down = this.createIndexSql(table, index)\n        await this.executeQueries(up, down)\n        table.removeIndex(index)\n    }\n\n    /**\n     * Drops an indices from the table.\n     */\n    async dropIndices(\n        tableOrName: Table | string,\n        indices: TableIndex[],\n    ): Promise<void> {\n        const promises = indices.map((index) =>\n            this.dropIndex(tableOrName, index),\n        )\n        await Promise.all(promises)\n    }\n\n    /**\n     * Clears all table contents.\n     * Note: this operation uses SQL's TRUNCATE query which cannot be reverted in transactions.\n     */\n    async clearTable(tableName: string): Promise<void> {\n        await this.query(`DELETE FROM ${this.escapePath(tableName)}`)\n    }\n\n    /**\n     * Removes all tables from the currently connected database.\n     */\n    async clearDatabase(database?: string): Promise<void> {\n        let dbPath: string | undefined = undefined\n        if (\n            database &&\n            this.driver.getAttachedDatabaseHandleByRelativePath(database)\n        ) {\n            dbPath =\n                this.driver.getAttachedDatabaseHandleByRelativePath(database)\n        }\n\n        await this.query(`PRAGMA foreign_keys = OFF`)\n\n        const isAnotherTransactionActive = this.isTransactionActive\n        if (!isAnotherTransactionActive) await this.startTransaction()\n        try {\n            const selectViewDropsQuery = dbPath\n                ? `SELECT 'DROP VIEW \"${dbPath}\".\"' || name || '\";' as query FROM \"${dbPath}\".\"sqlite_master\" WHERE \"type\" = 'view'`\n                : `SELECT 'DROP VIEW \"' || name || '\";' as query FROM \"sqlite_master\" WHERE \"type\" = 'view'`\n            const dropViewQueries: ObjectLiteral[] = await this.query(\n                selectViewDropsQuery,\n            )\n            await Promise.all(\n                dropViewQueries.map((q) => this.query(q[\"query\"])),\n            )\n\n            const selectTableDropsQuery = dbPath\n                ? `SELECT 'DROP TABLE \"${dbPath}\".\"' || name || '\";' as query FROM \"${dbPath}\".\"sqlite_master\" WHERE \"type\" = 'table' AND \"name\" != 'sqlite_sequence'`\n                : `SELECT 'DROP TABLE \"' || name || '\";' as query FROM \"sqlite_master\" WHERE \"type\" = 'table' AND \"name\" != 'sqlite_sequence'`\n            const dropTableQueries: ObjectLiteral[] = await this.query(\n                selectTableDropsQuery,\n            )\n            await Promise.all(\n                dropTableQueries.map((q) => this.query(q[\"query\"])),\n            )\n\n            if (!isAnotherTransactionActive) await this.commitTransaction()\n        } catch (error) {\n            try {\n                // we throw original error even if rollback thrown an error\n                if (!isAnotherTransactionActive)\n                    await this.rollbackTransaction()\n            } catch (rollbackError) {}\n            throw error\n        } finally {\n            await this.query(`PRAGMA foreign_keys = ON`)\n        }\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    protected async loadViews(viewNames?: string[]): Promise<View[]> {\n        const hasTable = await this.hasTable(this.getTypeormMetadataTableName())\n        if (!hasTable) {\n            return []\n        }\n\n        if (!viewNames) {\n            viewNames = []\n        }\n\n        const viewNamesString = viewNames\n            .map((name) => \"'\" + name + \"'\")\n            .join(\", \")\n        let query = `SELECT \"t\".* FROM \"${this.getTypeormMetadataTableName()}\" \"t\" INNER JOIN \"sqlite_master\" s ON \"s\".\"name\" = \"t\".\"name\" AND \"s\".\"type\" = 'view' WHERE \"t\".\"type\" = '${\n            MetadataTableType.VIEW\n        }'`\n        if (viewNamesString.length > 0)\n            query += ` AND \"t\".\"name\" IN (${viewNamesString})`\n        const dbViews = await this.query(query)\n        return dbViews.map((dbView: any) => {\n            const view = new View()\n            view.name = dbView[\"name\"]\n            view.expression = dbView[\"value\"]\n            return view\n        })\n    }\n\n    protected async loadTableRecords(\n        tablePath: string,\n        tableOrIndex: \"table\" | \"index\",\n    ) {\n        let database: string | undefined = undefined\n        const [schema, tableName] = this.splitTablePath(tablePath)\n        if (\n            schema &&\n            this.driver.getAttachedDatabasePathRelativeByHandle(schema)\n        ) {\n            database =\n                this.driver.getAttachedDatabasePathRelativeByHandle(schema)\n        }\n        return this.query(\n            `SELECT ${database ? `'${database}'` : null} as database, ${\n                schema ? `'${schema}'` : null\n            } as schema, * FROM ${\n                schema ? `\"${schema}\".` : \"\"\n            }${this.escapePath(\n                `sqlite_master`,\n            )} WHERE \"type\" = '${tableOrIndex}' AND \"${\n                tableOrIndex === \"table\" ? \"name\" : \"tbl_name\"\n            }\" IN ('${tableName}')`,\n        )\n    }\n\n    protected async loadPragmaRecords(tablePath: string, pragma: string) {\n        const [, tableName] = this.splitTablePath(tablePath)\n        return this.query(`PRAGMA ${pragma}(\"${tableName}\")`)\n    }\n\n    /**\n     * Loads all tables (with given names) from the database and creates a Table from them.\n     */\n    protected async loadTables(tableNames?: string[]): Promise<Table[]> {\n        // if no tables given then no need to proceed\n        if (tableNames && tableNames.length === 0) {\n            return []\n        }\n\n        let dbTables: { database?: string; name: string; sql: string }[] = []\n        let dbIndicesDef: ObjectLiteral[]\n\n        if (!tableNames) {\n            const tablesSql = `SELECT * FROM \"sqlite_master\" WHERE \"type\" = 'table'`\n            dbTables.push(...(await this.query(tablesSql)))\n\n            const tableNamesString = dbTables\n                .map(({ name }) => `'${name}'`)\n                .join(\", \")\n            dbIndicesDef = await this.query(\n                `SELECT * FROM \"sqlite_master\" WHERE \"type\" = 'index' AND \"tbl_name\" IN (${tableNamesString})`,\n            )\n        } else {\n            const tableNamesWithoutDot = tableNames\n                .filter((tableName) => {\n                    return tableName.split(\".\").length === 1\n                })\n                .map((tableName) => `'${tableName}'`)\n\n            const tableNamesWithDot = tableNames.filter((tableName) => {\n                return tableName.split(\".\").length > 1\n            })\n\n            const queryPromises = (type: \"table\" | \"index\") => {\n                const promises = [\n                    ...tableNamesWithDot.map((tableName) =>\n                        this.loadTableRecords(tableName, type),\n                    ),\n                ]\n\n                if (tableNamesWithoutDot.length) {\n                    promises.push(\n                        this.query(\n                            `SELECT * FROM \"sqlite_master\" WHERE \"type\" = '${type}' AND \"${\n                                type === \"table\" ? \"name\" : \"tbl_name\"\n                            }\" IN (${tableNamesWithoutDot})`,\n                        ),\n                    )\n                }\n\n                return promises\n            }\n            dbTables = (await Promise.all(queryPromises(\"table\")))\n                .reduce((acc, res) => [...acc, ...res], [])\n                .filter(Boolean)\n            dbIndicesDef = (await Promise.all(queryPromises(\"index\")))\n                .reduce((acc, res) => [...acc, ...res], [])\n                .filter(Boolean)\n        }\n\n        // if tables were not found in the db, no need to proceed\n        if (dbTables.length === 0) {\n            return []\n        }\n\n        // create table schemas for loaded tables\n        return Promise.all(\n            dbTables.map(async (dbTable) => {\n                const tablePath =\n                    dbTable[\"database\"] &&\n                    this.driver.getAttachedDatabaseHandleByRelativePath(\n                        dbTable[\"database\"],\n                    )\n                        ? `${this.driver.getAttachedDatabaseHandleByRelativePath(\n                              dbTable[\"database\"],\n                          )}.${dbTable[\"name\"]}`\n                        : dbTable[\"name\"]\n\n                const sql = dbTable[\"sql\"]\n\n                const withoutRowid = sql.includes(\"WITHOUT ROWID\")\n                const table = new Table({ name: tablePath, withoutRowid })\n\n                // load columns and indices\n                const [dbColumns, dbIndices, dbForeignKeys]: ObjectLiteral[][] =\n                    await Promise.all([\n                        this.loadPragmaRecords(tablePath, `table_xinfo`),\n                        this.loadPragmaRecords(tablePath, `index_list`),\n                        this.loadPragmaRecords(tablePath, `foreign_key_list`),\n                    ])\n\n                // find column name with auto increment\n                let autoIncrementColumnName: string | undefined = undefined\n                const tableSql: string = dbTable[\"sql\"]\n                const autoIncrementIndex = tableSql\n                    .toUpperCase()\n                    .indexOf(\"AUTOINCREMENT\")\n                if (autoIncrementIndex !== -1) {\n                    autoIncrementColumnName = tableSql.substr(\n                        0,\n                        autoIncrementIndex,\n                    )\n                    const comma = autoIncrementColumnName.lastIndexOf(\",\")\n                    const bracket = autoIncrementColumnName.lastIndexOf(\"(\")\n                    if (comma !== -1) {\n                        autoIncrementColumnName =\n                            autoIncrementColumnName.substr(comma)\n                        autoIncrementColumnName =\n                            autoIncrementColumnName.substr(\n                                0,\n                                autoIncrementColumnName.lastIndexOf('\"'),\n                            )\n                        autoIncrementColumnName =\n                            autoIncrementColumnName.substr(\n                                autoIncrementColumnName.indexOf('\"') + 1,\n                            )\n                    } else if (bracket !== -1) {\n                        autoIncrementColumnName =\n                            autoIncrementColumnName.substr(bracket)\n                        autoIncrementColumnName =\n                            autoIncrementColumnName.substr(\n                                0,\n                                autoIncrementColumnName.lastIndexOf('\"'),\n                            )\n                        autoIncrementColumnName =\n                            autoIncrementColumnName.substr(\n                                autoIncrementColumnName.indexOf('\"') + 1,\n                            )\n                    }\n                }\n\n                // create columns from the loaded columns\n                table.columns = await Promise.all(\n                    dbColumns.map(async (dbColumn) => {\n                        const tableColumn = new TableColumn()\n                        tableColumn.name = dbColumn[\"name\"]\n                        tableColumn.type = dbColumn[\"type\"].toLowerCase()\n                        tableColumn.default =\n                            dbColumn[\"dflt_value\"] !== null &&\n                            dbColumn[\"dflt_value\"] !== undefined\n                                ? dbColumn[\"dflt_value\"]\n                                : undefined\n                        tableColumn.isNullable = dbColumn[\"notnull\"] === 0\n                        // primary keys are numbered starting with 1, columns that aren't primary keys are marked with 0\n                        tableColumn.isPrimary = dbColumn[\"pk\"] > 0\n                        tableColumn.comment = \"\" // SQLite does not support column comments\n                        tableColumn.isGenerated =\n                            autoIncrementColumnName === dbColumn[\"name\"]\n                        if (tableColumn.isGenerated) {\n                            tableColumn.generationStrategy = \"increment\"\n                        }\n\n                        if (\n                            dbColumn[\"hidden\"] === 2 ||\n                            dbColumn[\"hidden\"] === 3\n                        ) {\n                            tableColumn.generatedType =\n                                dbColumn[\"hidden\"] === 2 ? \"VIRTUAL\" : \"STORED\"\n\n                            const asExpressionQuery =\n                                this.selectTypeormMetadataSql({\n                                    table: table.name,\n                                    type: MetadataTableType.GENERATED_COLUMN,\n                                    name: tableColumn.name,\n                                })\n\n                            const results = await this.query(\n                                asExpressionQuery.query,\n                                asExpressionQuery.parameters,\n                            )\n                            if (results[0] && results[0].value) {\n                                tableColumn.asExpression = results[0].value\n                            } else {\n                                tableColumn.asExpression = \"\"\n                            }\n                        }\n\n                        if (tableColumn.type === \"varchar\") {\n                            tableColumn.enum = OrmUtils.parseSqlCheckExpression(\n                                sql,\n                                tableColumn.name,\n                            )\n                        }\n\n                        // parse datatype and attempt to retrieve length, precision and scale\n                        const pos = tableColumn.type.indexOf(\"(\")\n                        if (pos !== -1) {\n                            const fullType = tableColumn.type\n                            const dataType = fullType.substr(0, pos)\n                            if (\n                                this.driver.withLengthColumnTypes.find(\n                                    (col) => col === dataType,\n                                )\n                            ) {\n                                const len = parseInt(\n                                    fullType.substring(\n                                        pos + 1,\n                                        fullType.length - 1,\n                                    ),\n                                )\n                                if (len) {\n                                    tableColumn.length = len.toString()\n                                    tableColumn.type = dataType // remove the length part from the datatype\n                                }\n                            }\n                            if (\n                                this.driver.withPrecisionColumnTypes.find(\n                                    (col) => col === dataType,\n                                )\n                            ) {\n                                const re = new RegExp(\n                                    `^${dataType}\\\\((\\\\d+),?\\\\s?(\\\\d+)?\\\\)`,\n                                )\n                                const matches = fullType.match(re)\n                                if (matches && matches[1]) {\n                                    tableColumn.precision = +matches[1]\n                                }\n                                if (\n                                    this.driver.withScaleColumnTypes.find(\n                                        (col) => col === dataType,\n                                    )\n                                ) {\n                                    if (matches && matches[2]) {\n                                        tableColumn.scale = +matches[2]\n                                    }\n                                }\n                                tableColumn.type = dataType // remove the precision/scale part from the datatype\n                            }\n                        }\n\n                        return tableColumn\n                    }),\n                )\n\n                // find foreign key constraints from CREATE TABLE sql\n                let fkResult\n                const fkMappings: {\n                    name: string\n                    columns: string[]\n                    referencedTableName: string\n                }[] = []\n                const fkRegex =\n                    /CONSTRAINT \"([^\"]*)\" FOREIGN KEY ?\\((.*?)\\) REFERENCES \"([^\"]*)\"/g\n                while ((fkResult = fkRegex.exec(sql)) !== null) {\n                    fkMappings.push({\n                        name: fkResult[1],\n                        columns: fkResult[2]\n                            .substr(1, fkResult[2].length - 2)\n                            .split(`\", \"`),\n                        referencedTableName: fkResult[3],\n                    })\n                }\n\n                // build foreign keys\n                const tableForeignKeyConstraints = OrmUtils.uniq(\n                    dbForeignKeys,\n                    (dbForeignKey) => dbForeignKey[\"id\"],\n                )\n\n                table.foreignKeys = tableForeignKeyConstraints.map(\n                    (foreignKey) => {\n                        const ownForeignKeys = dbForeignKeys.filter(\n                            (dbForeignKey) =>\n                                dbForeignKey[\"id\"] === foreignKey[\"id\"] &&\n                                dbForeignKey[\"table\"] === foreignKey[\"table\"],\n                        )\n                        const columnNames = ownForeignKeys.map(\n                            (dbForeignKey) => dbForeignKey[\"from\"],\n                        )\n                        const referencedColumnNames = ownForeignKeys.map(\n                            (dbForeignKey) => dbForeignKey[\"to\"],\n                        )\n\n                        // find related foreign key mapping\n                        const fkMapping = fkMappings.find(\n                            (it) =>\n                                it.referencedTableName ===\n                                    foreignKey[\"table\"] &&\n                                it.columns.every(\n                                    (column) =>\n                                        columnNames.indexOf(column) !== -1,\n                                ),\n                        )\n\n                        return new TableForeignKey({\n                            name: fkMapping?.name,\n                            columnNames: columnNames,\n                            referencedTableName: foreignKey[\"table\"],\n                            referencedColumnNames: referencedColumnNames,\n                            onDelete: foreignKey[\"on_delete\"],\n                            onUpdate: foreignKey[\"on_update\"],\n                        })\n                    },\n                )\n\n                // find unique constraints from CREATE TABLE sql\n                let uniqueRegexResult\n                const uniqueMappings: { name: string; columns: string[] }[] = []\n                const uniqueRegex = /CONSTRAINT \"([^\"]*)\" UNIQUE ?\\((.*?)\\)/g\n                while ((uniqueRegexResult = uniqueRegex.exec(sql)) !== null) {\n                    uniqueMappings.push({\n                        name: uniqueRegexResult[1],\n                        columns: uniqueRegexResult[2]\n                            .substr(1, uniqueRegexResult[2].length - 2)\n                            .split(`\", \"`),\n                    })\n                }\n\n                // build unique constraints\n                const tableUniquePromises = dbIndices\n                    .filter((dbIndex) => dbIndex[\"origin\"] === \"u\")\n                    .map((dbIndex) => dbIndex[\"name\"])\n                    .filter(\n                        (value, index, self) => self.indexOf(value) === index,\n                    )\n                    .map(async (dbIndexName) => {\n                        const dbIndex = dbIndices.find(\n                            (dbIndex) => dbIndex[\"name\"] === dbIndexName,\n                        )\n                        const indexInfos: ObjectLiteral[] = await this.query(\n                            `PRAGMA index_info(\"${dbIndex![\"name\"]}\")`,\n                        )\n                        const indexColumns = indexInfos\n                            .sort(\n                                (indexInfo1, indexInfo2) =>\n                                    parseInt(indexInfo1[\"seqno\"]) -\n                                    parseInt(indexInfo2[\"seqno\"]),\n                            )\n                            .map((indexInfo) => indexInfo[\"name\"])\n                        if (indexColumns.length === 1) {\n                            const column = table.columns.find((column) => {\n                                return !!indexColumns.find(\n                                    (indexColumn) =>\n                                        indexColumn === column.name,\n                                )\n                            })\n                            if (column) column.isUnique = true\n                        }\n\n                        // find existent mapping by a column names\n                        const foundMapping = uniqueMappings.find((mapping) => {\n                            return mapping!.columns.every(\n                                (column) => indexColumns.indexOf(column) !== -1,\n                            )\n                        })\n\n                        return new TableUnique({\n                            name: foundMapping\n                                ? foundMapping.name\n                                : this.connection.namingStrategy.uniqueConstraintName(\n                                      table,\n                                      indexColumns,\n                                  ),\n                            columnNames: indexColumns,\n                        })\n                    })\n                table.uniques = (await Promise.all(\n                    tableUniquePromises,\n                )) as TableUnique[]\n\n                // build checks\n                let result\n                const regexp =\n                    /CONSTRAINT \"([^\"]*)\" CHECK ?(\\(.*?\\))([,]|[)]$)/g\n                while ((result = regexp.exec(sql)) !== null) {\n                    table.checks.push(\n                        new TableCheck({\n                            name: result[1],\n                            expression: result[2],\n                        }),\n                    )\n                }\n\n                // build indices\n                const indicesPromises = dbIndices\n                    .filter((dbIndex) => dbIndex[\"origin\"] === \"c\")\n                    .map((dbIndex) => dbIndex[\"name\"])\n                    .filter(\n                        (value, index, self) => self.indexOf(value) === index,\n                    ) // unqiue\n                    .map(async (dbIndexName) => {\n                        const indexDef = dbIndicesDef.find(\n                            (dbIndexDef) => dbIndexDef[\"name\"] === dbIndexName,\n                        )\n                        const condition = /WHERE (.*)/.exec(indexDef![\"sql\"])\n                        const dbIndex = dbIndices.find(\n                            (dbIndex) => dbIndex[\"name\"] === dbIndexName,\n                        )\n                        const indexInfos: ObjectLiteral[] = await this.query(\n                            `PRAGMA index_info(\"${dbIndex![\"name\"]}\")`,\n                        )\n                        const indexColumns = indexInfos\n                            .sort(\n                                (indexInfo1, indexInfo2) =>\n                                    parseInt(indexInfo1[\"seqno\"]) -\n                                    parseInt(indexInfo2[\"seqno\"]),\n                            )\n                            .map((indexInfo) => indexInfo[\"name\"])\n                        const dbIndexPath = `${\n                            dbTable[\"database\"] ? `${dbTable[\"database\"]}.` : \"\"\n                        }${dbIndex![\"name\"]}`\n\n                        const isUnique =\n                            dbIndex![\"unique\"] === \"1\" ||\n                            dbIndex![\"unique\"] === 1\n                        return new TableIndex(<TableIndexOptions>{\n                            table: table,\n                            name: dbIndexPath,\n                            columnNames: indexColumns,\n                            isUnique: isUnique,\n                            where: condition ? condition[1] : undefined,\n                        })\n                    })\n                const indices = await Promise.all(indicesPromises)\n                table.indices = indices.filter(\n                    (index) => !!index,\n                ) as TableIndex[]\n\n                return table\n            }),\n        )\n    }\n\n    /**\n     * Builds create table sql.\n     */\n    protected createTableSql(\n        table: Table,\n        createForeignKeys?: boolean,\n        temporaryTable?: boolean,\n    ): Query {\n        const primaryColumns = table.columns.filter(\n            (column) => column.isPrimary,\n        )\n        const hasAutoIncrement = primaryColumns.find(\n            (column) =>\n                column.isGenerated && column.generationStrategy === \"increment\",\n        )\n        const skipPrimary = primaryColumns.length > 1\n        if (skipPrimary && hasAutoIncrement)\n            throw new TypeORMError(\n                `Sqlite does not support AUTOINCREMENT on composite primary key`,\n            )\n\n        const columnDefinitions = table.columns\n            .map((column) => this.buildCreateColumnSql(column, skipPrimary))\n            .join(\", \")\n        const [database] = this.splitTablePath(table.name)\n        let sql = `CREATE TABLE ${this.escapePath(\n            table.name,\n        )} (${columnDefinitions}`\n\n        const [databaseNew, tableName] = this.splitTablePath(table.name)\n        const newTableName = temporaryTable\n            ? `${databaseNew ? `${databaseNew}.` : \"\"}${tableName.replace(\n                  /^temporary_/,\n                  \"\",\n              )}`\n            : table.name\n\n        // need for `addColumn()` method, because it recreates table.\n        table.columns\n            .filter((column) => column.isUnique)\n            .forEach((column) => {\n                const isUniqueExist = table.uniques.some(\n                    (unique) =>\n                        unique.columnNames.length === 1 &&\n                        unique.columnNames[0] === column.name,\n                )\n                if (!isUniqueExist)\n                    table.uniques.push(\n                        new TableUnique({\n                            name: this.connection.namingStrategy.uniqueConstraintName(\n                                table,\n                                [column.name],\n                            ),\n                            columnNames: [column.name],\n                        }),\n                    )\n            })\n\n        if (table.uniques.length > 0) {\n            const uniquesSql = table.uniques\n                .map((unique) => {\n                    const uniqueName = unique.name\n                        ? unique.name\n                        : this.connection.namingStrategy.uniqueConstraintName(\n                              newTableName,\n                              unique.columnNames,\n                          )\n                    const columnNames = unique.columnNames\n                        .map((columnName) => `\"${columnName}\"`)\n                        .join(\", \")\n                    return `CONSTRAINT \"${uniqueName}\" UNIQUE (${columnNames})`\n                })\n                .join(\", \")\n\n            sql += `, ${uniquesSql}`\n        }\n\n        if (table.checks.length > 0) {\n            const checksSql = table.checks\n                .map((check) => {\n                    const checkName = check.name\n                        ? check.name\n                        : this.connection.namingStrategy.checkConstraintName(\n                              newTableName,\n                              check.expression!,\n                          )\n                    return `CONSTRAINT \"${checkName}\" CHECK (${check.expression})`\n                })\n                .join(\", \")\n\n            sql += `, ${checksSql}`\n        }\n\n        if (table.foreignKeys.length > 0 && createForeignKeys) {\n            const foreignKeysSql = table.foreignKeys\n                .filter((fk) => {\n                    const [referencedDatabase] = this.splitTablePath(\n                        fk.referencedTableName,\n                    )\n                    if (referencedDatabase !== database) {\n                        return false\n                    }\n                    return true\n                })\n                .map((fk) => {\n                    const [, referencedTable] = this.splitTablePath(\n                        fk.referencedTableName,\n                    )\n                    const columnNames = fk.columnNames\n                        .map((columnName) => `\"${columnName}\"`)\n                        .join(\", \")\n                    if (!fk.name)\n                        fk.name = this.connection.namingStrategy.foreignKeyName(\n                            newTableName,\n                            fk.columnNames,\n                            this.getTablePath(fk),\n                            fk.referencedColumnNames,\n                        )\n                    const referencedColumnNames = fk.referencedColumnNames\n                        .map((columnName) => `\"${columnName}\"`)\n                        .join(\", \")\n\n                    let constraint = `CONSTRAINT \"${fk.name}\" FOREIGN KEY (${columnNames}) REFERENCES \"${referencedTable}\" (${referencedColumnNames})`\n                    if (fk.onDelete) constraint += ` ON DELETE ${fk.onDelete}`\n                    if (fk.onUpdate) constraint += ` ON UPDATE ${fk.onUpdate}`\n                    if (fk.deferrable)\n                        constraint += ` DEFERRABLE ${fk.deferrable}`\n\n                    return constraint\n                })\n                .join(\", \")\n\n            sql += `, ${foreignKeysSql}`\n        }\n\n        if (primaryColumns.length > 1) {\n            const columnNames = primaryColumns\n                .map((column) => `\"${column.name}\"`)\n                .join(\", \")\n            sql += `, PRIMARY KEY (${columnNames})`\n        }\n\n        sql += `)`\n\n        if (table.withoutRowid) {\n            sql += \" WITHOUT ROWID\"\n        }\n\n        return new Query(sql)\n    }\n\n    /**\n     * Builds drop table sql.\n     */\n    protected dropTableSql(\n        tableOrName: Table | string,\n        ifExist?: boolean,\n    ): Query {\n        const tableName = InstanceChecker.isTable(tableOrName)\n            ? tableOrName.name\n            : tableOrName\n        const query = ifExist\n            ? `DROP TABLE IF EXISTS ${this.escapePath(tableName)}`\n            : `DROP TABLE ${this.escapePath(tableName)}`\n        return new Query(query)\n    }\n\n    protected createViewSql(view: View): Query {\n        if (typeof view.expression === \"string\") {\n            return new Query(`CREATE VIEW \"${view.name}\" AS ${view.expression}`)\n        } else {\n            return new Query(\n                `CREATE VIEW \"${view.name}\" AS ${view\n                    .expression(this.connection)\n                    .getQuery()}`,\n            )\n        }\n    }\n\n    protected insertViewDefinitionSql(view: View): Query {\n        const expression =\n            typeof view.expression === \"string\"\n                ? view.expression.trim()\n                : view.expression(this.connection).getQuery()\n        return this.insertTypeormMetadataSql({\n            type: MetadataTableType.VIEW,\n            name: view.name,\n            value: expression,\n        })\n    }\n\n    /**\n     * Builds drop view sql.\n     */\n    protected dropViewSql(viewOrPath: View | string): Query {\n        const viewName = InstanceChecker.isView(viewOrPath)\n            ? viewOrPath.name\n            : viewOrPath\n        return new Query(`DROP VIEW \"${viewName}\"`)\n    }\n\n    /**\n     * Builds remove view sql.\n     */\n    protected deleteViewDefinitionSql(viewOrPath: View | string): Query {\n        const viewName = InstanceChecker.isView(viewOrPath)\n            ? viewOrPath.name\n            : viewOrPath\n        return this.deleteTypeormMetadataSql({\n            type: MetadataTableType.VIEW,\n            name: viewName,\n        })\n    }\n\n    /**\n     * Builds create index sql.\n     */\n    protected createIndexSql(table: Table, index: TableIndex): Query {\n        const columns = index.columnNames\n            .map((columnName) => `\"${columnName}\"`)\n            .join(\", \")\n        const [database, tableName] = this.splitTablePath(table.name)\n        return new Query(\n            `CREATE ${index.isUnique ? \"UNIQUE \" : \"\"}INDEX ${\n                database ? `\"${database}\".` : \"\"\n            }${this.escapePath(index.name!)} ON \"${tableName}\" (${columns}) ${\n                index.where ? \"WHERE \" + index.where : \"\"\n            }`,\n        )\n    }\n\n    /**\n     * Builds drop index sql.\n     */\n    protected dropIndexSql(indexOrName: TableIndex | string): Query {\n        const indexName = InstanceChecker.isTableIndex(indexOrName)\n            ? indexOrName.name\n            : indexOrName\n        return new Query(`DROP INDEX ${this.escapePath(indexName!)}`)\n    }\n\n    /**\n     * Builds a query for create column.\n     */\n    protected buildCreateColumnSql(\n        column: TableColumn,\n        skipPrimary?: boolean,\n    ): string {\n        let c = '\"' + column.name + '\"'\n        if (InstanceChecker.isColumnMetadata(column)) {\n            c += \" \" + this.driver.normalizeType(column)\n        } else {\n            c += \" \" + this.connection.driver.createFullType(column)\n        }\n\n        if (column.enum)\n            c +=\n                ' CHECK( \"' +\n                column.name +\n                '\" IN (' +\n                column.enum.map((val) => \"'\" + val + \"'\").join(\",\") +\n                \") )\"\n        if (column.isPrimary && !skipPrimary) c += \" PRIMARY KEY\"\n        if (\n            column.isGenerated === true &&\n            column.generationStrategy === \"increment\"\n        )\n            // don't use skipPrimary here since updates can update already exist primary without auto inc.\n            c += \" AUTOINCREMENT\"\n        if (column.collation) c += \" COLLATE \" + column.collation\n        if (column.isNullable !== true) c += \" NOT NULL\"\n\n        if (column.asExpression) {\n            c += ` AS (${column.asExpression}) ${\n                column.generatedType ? column.generatedType : \"VIRTUAL\"\n            }`\n        } else {\n            if (column.default !== undefined && column.default !== null)\n                c += \" DEFAULT (\" + column.default + \")\"\n        }\n\n        return c\n    }\n\n    protected async recreateTable(\n        newTable: Table,\n        oldTable: Table,\n        migrateData = true,\n    ): Promise<void> {\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n\n        // drop old table indices\n        oldTable.indices.forEach((index) => {\n            upQueries.push(this.dropIndexSql(index))\n            downQueries.push(this.createIndexSql(oldTable, index))\n        })\n\n        // change table name into 'temporary_table'\n        let [databaseNew, tableNameNew] = this.splitTablePath(newTable.name)\n        const [, tableNameOld] = this.splitTablePath(oldTable.name)\n        newTable.name = tableNameNew = `${\n            databaseNew ? `${databaseNew}.` : \"\"\n        }temporary_${tableNameNew}`\n\n        // create new table\n        upQueries.push(this.createTableSql(newTable, true, true))\n        downQueries.push(this.dropTableSql(newTable))\n\n        // migrate all data from the old table into new table\n        if (migrateData) {\n            let newColumnNames = newTable.columns\n                .filter((column) => !column.generatedType)\n                .map((column) => `\"${column.name}\"`)\n\n            let oldColumnNames = oldTable.columns\n                .filter((column) => !column.generatedType)\n                .map((column) => `\"${column.name}\"`)\n\n            if (oldColumnNames.length < newColumnNames.length) {\n                newColumnNames = newTable.columns\n                    .filter((column) => {\n                        const oldColumn = oldTable.columns.find(\n                            (c) => c.name === column.name,\n                        )\n                        if (oldColumn && oldColumn.generatedType) return false\n                        return !column.generatedType && oldColumn\n                    })\n                    .map((column) => `\"${column.name}\"`)\n            } else if (oldColumnNames.length > newColumnNames.length) {\n                oldColumnNames = oldTable.columns\n                    .filter((column) => {\n                        return (\n                            !column.generatedType &&\n                            newTable.columns.find((c) => c.name === column.name)\n                        )\n                    })\n                    .map((column) => `\"${column.name}\"`)\n            }\n\n            upQueries.push(\n                new Query(\n                    `INSERT INTO ${this.escapePath(\n                        newTable.name,\n                    )}(${newColumnNames.join(\n                        \", \",\n                    )}) SELECT ${oldColumnNames.join(\n                        \", \",\n                    )} FROM ${this.escapePath(oldTable.name)}`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `INSERT INTO ${this.escapePath(\n                        oldTable.name,\n                    )}(${oldColumnNames.join(\n                        \", \",\n                    )}) SELECT ${newColumnNames.join(\n                        \", \",\n                    )} FROM ${this.escapePath(newTable.name)}`,\n                ),\n            )\n        }\n\n        // drop old table\n        upQueries.push(this.dropTableSql(oldTable))\n        downQueries.push(this.createTableSql(oldTable, true))\n\n        // rename old table\n        upQueries.push(\n            new Query(\n                `ALTER TABLE ${this.escapePath(\n                    newTable.name,\n                )} RENAME TO ${this.escapePath(tableNameOld)}`,\n            ),\n        )\n        downQueries.push(\n            new Query(\n                `ALTER TABLE ${this.escapePath(\n                    oldTable.name,\n                )} RENAME TO ${this.escapePath(tableNameNew)}`,\n            ),\n        )\n\n        newTable.name = oldTable.name\n\n        // recreate table indices\n        newTable.indices.forEach((index) => {\n            // new index may be passed without name. In this case we generate index name manually.\n            if (!index.name)\n                index.name = this.connection.namingStrategy.indexName(\n                    newTable,\n                    index.columnNames,\n                    index.where,\n                )\n            upQueries.push(this.createIndexSql(newTable, index))\n            downQueries.push(this.dropIndexSql(index))\n        })\n\n        // update generated columns in \"typeorm_metadata\" table\n        // Step 1: clear data for removed generated columns\n        oldTable.columns\n            .filter((column) => {\n                const newTableColumn = newTable.columns.find(\n                    (c) => c.name === column.name,\n                )\n                // we should delete record from \"typeorm_metadata\" if generated column was removed\n                // or it was changed to non-generated\n                return (\n                    column.generatedType &&\n                    column.asExpression &&\n                    (!newTableColumn ||\n                        (!newTableColumn.generatedType &&\n                            !newTableColumn.asExpression))\n                )\n            })\n            .forEach((column) => {\n                const deleteQuery = this.deleteTypeormMetadataSql({\n                    table: oldTable.name,\n                    type: MetadataTableType.GENERATED_COLUMN,\n                    name: column.name,\n                })\n\n                const insertQuery = this.insertTypeormMetadataSql({\n                    table: oldTable.name,\n                    type: MetadataTableType.GENERATED_COLUMN,\n                    name: column.name,\n                    value: column.asExpression,\n                })\n\n                upQueries.push(deleteQuery)\n                downQueries.push(insertQuery)\n            })\n\n        // Step 2: add data for new generated columns\n        newTable.columns\n            .filter(\n                (column) =>\n                    column.generatedType &&\n                    column.asExpression &&\n                    !oldTable.columns.some((c) => c.name === column.name),\n            )\n            .forEach((column) => {\n                const insertQuery = this.insertTypeormMetadataSql({\n                    table: newTable.name,\n                    type: MetadataTableType.GENERATED_COLUMN,\n                    name: column.name,\n                    value: column.asExpression,\n                })\n\n                const deleteQuery = this.deleteTypeormMetadataSql({\n                    table: newTable.name,\n                    type: MetadataTableType.GENERATED_COLUMN,\n                    name: column.name,\n                })\n\n                upQueries.push(insertQuery)\n                downQueries.push(deleteQuery)\n            })\n\n        // Step 3: update changed expressions\n        newTable.columns\n            .filter((column) => column.generatedType && column.asExpression)\n            .forEach((column) => {\n                const oldColumn = oldTable.columns.find(\n                    (c) =>\n                        c.name === column.name &&\n                        c.generatedType &&\n                        column.generatedType &&\n                        c.asExpression !== column.asExpression,\n                )\n\n                if (!oldColumn) return\n\n                // update expression\n                const deleteQuery = this.deleteTypeormMetadataSql({\n                    table: oldTable.name,\n                    type: MetadataTableType.GENERATED_COLUMN,\n                    name: oldColumn.name,\n                })\n\n                const insertQuery = this.insertTypeormMetadataSql({\n                    table: newTable.name,\n                    type: MetadataTableType.GENERATED_COLUMN,\n                    name: column.name,\n                    value: column.asExpression,\n                })\n\n                upQueries.push(deleteQuery)\n                upQueries.push(insertQuery)\n\n                // revert update\n                const revertInsertQuery = this.insertTypeormMetadataSql({\n                    table: newTable.name,\n                    type: MetadataTableType.GENERATED_COLUMN,\n                    name: oldColumn.name,\n                    value: oldColumn.asExpression,\n                })\n\n                const revertDeleteQuery = this.deleteTypeormMetadataSql({\n                    table: oldTable.name,\n                    type: MetadataTableType.GENERATED_COLUMN,\n                    name: column.name,\n                })\n\n                downQueries.push(revertInsertQuery)\n                downQueries.push(revertDeleteQuery)\n            })\n\n        await this.executeQueries(upQueries, downQueries)\n        this.replaceCachedTable(oldTable, newTable)\n    }\n\n    /**\n     * tablePath e.g. \"myDB.myTable\", \"myTable\"\n     */\n    protected splitTablePath(tablePath: string): [string | undefined, string] {\n        return (\n            tablePath.indexOf(\".\") !== -1\n                ? tablePath.split(\".\")\n                : [undefined, tablePath]\n        ) as [string | undefined, string]\n    }\n\n    /**\n     * Escapes given table or view path. Tolerates leading/trailing dots\n     */\n    protected escapePath(\n        target: Table | View | string,\n        disableEscape?: boolean,\n    ): string {\n        const tableName =\n            InstanceChecker.isTable(target) || InstanceChecker.isView(target)\n                ? target.name\n                : target\n        return tableName\n            .replace(/^\\.+|\\.+$/g, \"\")\n            .split(\".\")\n            .map((i) => (disableEscape ? i : `\"${i}\"`))\n            .join(\".\")\n    }\n\n    /**\n     * Change table comment.\n     */\n    changeTableComment(\n        tableOrName: Table | string,\n        comment?: string,\n    ): Promise<void> {\n        throw new TypeORMError(`sqlit driver does not support change comment.`)\n    }\n}\n"], "sourceRoot": "../.."}