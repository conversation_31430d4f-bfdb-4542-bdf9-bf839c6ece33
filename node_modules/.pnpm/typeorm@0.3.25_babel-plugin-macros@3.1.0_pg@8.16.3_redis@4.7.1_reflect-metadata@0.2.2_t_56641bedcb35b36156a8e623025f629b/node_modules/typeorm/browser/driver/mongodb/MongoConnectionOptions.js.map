{"version": 3, "sources": ["../browser/src/driver/mongodb/MongoConnectionOptions.ts"], "names": [], "mappings": "", "file": "MongoConnectionOptions.js", "sourcesContent": ["import { BaseDataSourceOptions } from \"../../data-source/BaseDataSourceOptions\"\nimport { ReadPreference } from \"./typings\"\n\n/**\n * MongoDB specific connection options.\n */\nexport interface MongoConnectionOptions extends BaseDataSourceOptions {\n    /**\n     * Database type.\n     */\n    readonly type: \"mongodb\"\n\n    /**\n     * Connection url where the connection is performed.\n     */\n    readonly url?: string\n\n    /**\n     * Database host.\n     */\n    readonly host?: string\n\n    /**\n     * Database host replica set.\n     */\n    readonly hostReplicaSet?: string\n\n    /**\n     * Database host port.\n     */\n    readonly port?: number\n\n    /**\n     * Database username.\n     */\n    readonly username?: string\n\n    /**\n     * Database password.\n     */\n    readonly password?: string\n\n    /**\n     * Database name to connect to.\n     */\n    readonly database?: string\n\n    /**\n     * The driver object\n     * This defaults to require(\"mongodb\")\n     */\n    readonly driver?: any\n\n    /**\n     * MongoClientOptions\n     * Synced with https://mongodb.github.io/node-mongodb-native/5.9/interfaces/MongoClientOptions.html\n     */\n\n    /**\n     * The name of the application that created this MongoClient instance.\n     * MongoDB 3.4 and newer will print this value in the server log upon establishing each connection.\n     * It is also recorded in the slow query log and profile collections\n     */\n    readonly appName?: string\n\n    /**\n     * Specify the authentication mechanism that MongoDB will use to authenticate the connection.\n     */\n    readonly authMechanism?: string\n\n    /**\n     * Specify the database name associated with the user’s credentials.\n     */\n    readonly authSource?: string\n\n    /**\n     * Optionally enable in-use auto encryption\n     */\n    readonly autoEncryption?: any\n\n    /**\n     * Verifies the certificate `cert` is issued to `hostname`.\n     */\n    readonly checkServerIdentity?: Function\n\n    /**\n     * An array or comma-delimited string of compressors to enable network\n     * compression for communication between this client and a mongod/mongos instance.\n     */\n    readonly compressors?: string | string[]\n\n    /**\n     * The time in milliseconds to attempt a connection before timing out.\n     */\n    readonly connectTimeoutMS?: number\n\n    /**\n     * Allow a driver to force a Single topology type with a connection string containing one host\n     */\n    readonly directConnection?: boolean\n\n    /**\n     * IP family\n     */\n    readonly family?: number\n\n    /**\n     * Force server to assign `_id` values instead of driver\n     */\n    readonly forceServerObjectId?: boolean\n\n    /**\n     * serialize will not emit undefined fields\n     * note that the driver sets this to `false`\n     */\n    readonly ignoreUndefined?: boolean\n\n    /**\n     * @deprecated TCP Connection keep alive enabled. Will not be able to turn off in the future.\n     */\n    readonly keepAlive?: boolean\n\n    /**\n     * @deprecated The number of milliseconds to wait before initiating keepAlive on the TCP socket.\n     * Will not be configurable in the future.\n     */\n    readonly keepAliveInitialDelay?: number\n\n    /**\n     * The size (in milliseconds) of the latency window for selecting among multiple suitable MongoDB instances.\n     */\n    readonly localThresholdMS?: number\n\n    /**\n     * Specifies, in seconds, how stale a secondary can be before the client stops using it for read operations.\n     */\n    readonly maxStalenessSeconds?: number\n\n    /**\n     * The minimum number of connections in the connection pool.\n     */\n    readonly minPoolSize?: number\n\n    /**\n     * Enable command monitoring for this client\n     */\n    readonly monitorCommands?: boolean\n\n    /**\n     * TCP Connection no delay\n     */\n    readonly noDelay?: boolean\n\n    /**\n     * A primary key factory function for generation of custom `_id` keys\n     */\n    readonly pkFactory?: any\n\n    /**\n     * when deserializing a Binary will return it as a node.js Buffer instance.\n     */\n    readonly promoteBuffers?: boolean\n\n    /**\n     * when deserializing a Long will fit it into a Number if it's smaller than 53 bits.\n     */\n    readonly promoteLongs?: boolean\n\n    /**\n     * when deserializing will promote BSON values to their Node.js closest equivalent types.\n     */\n    readonly promoteValues?: boolean\n\n    /**\n     * Enabling the raw option will return a Node.js Buffer which is allocated using allocUnsafe API\n     */\n    readonly raw?: boolean\n\n    /**\n     * Specify a read concern for the collection (only MongoDB 3.2 or higher supported)\n     */\n    readonly readConcern?: any\n\n    /**\n     * Specifies the read preferences for this connection\n     */\n    readonly readPreference?: ReadPreference | string\n\n    /**\n     * Specifies the tags document as a comma-separated list of colon-separated key-value pairs.\n     */\n    readonly readPreferenceTags?: any[]\n\n    /**\n     * Specifies the name of the replica set, if the mongod is a member of a replica set.\n     */\n    readonly replicaSet?: string\n\n    /**\n     * Enable retryable writes.\n     */\n    readonly retryWrites?: boolean\n\n    /**\n     * serialize the javascript functions\n     */\n    readonly serializeFunctions?: boolean\n\n    /**\n     * The time in milliseconds to attempt a send or receive on a socket before the attempt times out.\n     */\n    readonly socketTimeoutMS?: number\n\n    /**\n     * @deprecated A boolean to enable or disables TLS/SSL for the connection.\n     * (The ssl option is equivalent to the tls option.)\n     */\n    readonly ssl?: boolean\n\n    /**\n     * @deprecated SSL Root Certificate file path.\n     *\n     * Will be removed in the next major version. Please use tlsCAFile instead.\n     */\n    readonly sslCA?: string\n\n    /**\n     * @deprecated SSL Certificate revocation list file path.\n     *\n     * Will be removed in the next major version.\n     */\n    readonly sslCRL?: string\n\n    /**\n     * @deprecated SSL Certificate file path.\n     *\n     * Will be removed in the next major version. Please use tlsCertificateKeyFile instead.\n     */\n    readonly sslCert?: string\n\n    /**\n     * @deprecated SSL Key file file path.\n     *\n     * Will be removed in the next major version. Please use tlsCertificateKeyFile instead.\n     */\n    readonly sslKey?: string\n\n    /**\n     * @deprecated SSL Certificate pass phrase.\n     *\n     * Will be removed in the next major version. Please use tlsCertificateKeyFilePassword instead.\n     */\n    readonly sslPass?: string\n\n    /**\n     * @deprecated Validate mongod server certificate against Certificate Authority\n     *\n     * Will be removed in the next major version. Please use tlsAllowInvalidCertificates instead.\n     */\n    readonly sslValidate?: boolean\n\n    /**\n     * Enables or disables TLS/SSL for the connection.\n     */\n    readonly tls?: boolean\n\n    /**\n     * Bypasses validation of the certificates presented by the mongod/mongos instance\n     */\n    readonly tlsAllowInvalidCertificates?: boolean\n\n    /**\n     * Specifies the location of a local .pem file that contains the root certificate chain from the Certificate Authority.\n     */\n    readonly tlsCAFile?: string\n\n    /**\n     * Specifies the location of a local .pem file that contains the client's TLS/SSL certificate and key.\n     */\n    readonly tlsCertificateKeyFile?: string\n\n    /**\n     * Specifies the password to de-crypt the tlsCertificateKeyFile.\n     */\n    readonly tlsCertificateKeyFilePassword?: string\n\n    /**\n     * @deprecated The write concern w value\n     *\n     * Please use the `writeConcern` option instead\n     */\n    readonly w?: string | number\n\n    /**\n     * A MongoDB WriteConcern, which describes the level of acknowledgement\n     * requested from MongoDB for write operations.\n     */\n    readonly writeConcern?: any\n\n    /**\n     * @deprecated The write concern timeout\n     *\n     * Please use the `writeConcern` option instead\n     */\n    readonly wtimeoutMS?: number\n}\n"], "sourceRoot": "../.."}