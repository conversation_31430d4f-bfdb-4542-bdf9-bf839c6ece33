{"version": 3, "sources": ["../browser/src/common/RelationType.ts"], "names": [], "mappings": "", "file": "RelationType.js", "sourcesContent": ["/**\n * Wrapper type for relation type definitions in entities.\n * Used to circumvent ESM modules circular dependency issue caused by reflection metadata saving the type of the property.\n *\n * Usage example:\n * @Entity()\n * export default class User {\n *\n *     @OneToOne(() => Profile, profile => profile.user)\n *     profile: Relation<Profile>;\n *\n * }\n */\nexport type Relation<T> = T\n"], "sourceRoot": ".."}