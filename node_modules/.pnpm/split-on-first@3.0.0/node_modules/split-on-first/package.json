{"name": "split-on-first", "version": "3.0.0", "description": "Split a string on the first occurance of a given separator", "license": "MIT", "repository": "sindresorhus/split-on-first", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["split", "string", "first", "once", "occurrence", "separator", "delimiter", "text"], "devDependencies": {"ava": "^3.15.0", "tsd": "^0.14.0", "xo": "^0.38.2"}}